# 宝塔面板部署指南 - 石化管道巡检系统

## 系统要求

- **服务器**: Linux (Ubuntu 18.04+ / CentOS 7+)
- **内存**: 2GB以上
- **磁盘**: 20GB以上
- **宝塔面板**: 7.7.0以上版本

## 部署架构

```
宝塔面板管理
├── Nginx (反向代理 + 静态文件)
├── MySQL 8.0 (数据库)
├── Node.js 18+ (后端API)
├── PM2 (进程管理)
└── SSL证书管理
```

## 第一步：安装宝塔面板

### 1.1 安装宝塔面板
```bash
# Ubuntu/Debian
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh

# CentOS
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
```

### 1.2 登录宝塔面板
安装完成后，访问面板地址并登录。

## 第二步：安装运行环境

在宝塔面板的"软件商店"中安装以下软件：

### 2.1 必装软件
- **Nginx** 1.20+
- **MySQL** 8.0
- **Node.js版本管理器** (安装Node.js 18+)
- **PM2管理器** 4.0+

### 2.2 可选软件
- **phpMyAdmin** (数据库管理)
- **SSL证书管理**
- **防火墙**

## 第三步：创建网站

### 3.1 创建前端网站
1. 点击"网站" → "添加站点"
2. 域名：`your-domain.com`
3. 根目录：`/www/wwwroot/pipeline-frontend`
4. PHP版本：纯静态
5. 创建数据库：否

### 3.2 创建后台管理网站
1. 点击"网站" → "添加站点"
2. 域名：`admin.your-domain.com`
3. 根目录：`/www/wwwroot/pipeline-admin`
4. PHP版本：纯静态
5. 创建数据库：否

## 第四步：配置数据库

### 4.1 创建数据库
1. 点击"数据库" → "添加数据库"
2. 数据库名：`pipeline_inspection`
3. 用户名：`pipeline_user`
4. 密码：设置强密码
5. 访问权限：本地服务器

### 4.2 导入数据库结构
1. 点击数据库名称进入phpMyAdmin
2. 选择`pipeline_inspection`数据库
3. 点击"导入"
4. 选择`backend/database/init.sql`文件
5. 点击执行

## 第五步：部署后端API

### 5.1 上传后端代码
```bash
# 在服务器上执行
cd /www/wwwroot
git clone <your-repo-url> pipeline-backend
cd pipeline-backend/backend
```

### 5.2 安装依赖
```bash
# 使用宝塔面板的Node.js版本管理器
npm install --production
```

### 5.3 配置环境变量
创建 `.env` 文件：
```env
NODE_ENV=production
PORT=3001

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=pipeline_inspection
DB_USER=pipeline_user
DB_PASS=your-database-password

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# 文件上传路径
UPLOAD_PATH=/www/wwwroot/pipeline-backend/backend/uploads
```

### 5.4 创建PM2配置
在宝塔面板的"PM2管理器"中：
1. 点击"添加项目"
2. 项目名称：`pipeline-api`
3. 启动文件：`/www/wwwroot/pipeline-backend/backend/src/app.js`
4. 运行目录：`/www/wwwroot/pipeline-backend/backend`
5. 点击"保存并启动"

## 第六步：构建和部署前端

### 6.1 本地构建前端
```bash
# 在本地开发环境
npm run build:h5
```

### 6.2 上传前端文件
将 `dist/build/h5` 目录下的所有文件上传到：
`/www/wwwroot/pipeline-frontend/`

### 6.3 构建和部署后台管理
```bash
# 在admin目录
npm run build
```
将 `dist` 目录下的所有文件上传到：
`/www/wwwroot/pipeline-admin/`

## 第七步：配置Nginx

### 7.1 前端网站Nginx配置
在宝塔面板中，点击前端网站的"设置" → "配置文件"，替换为：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置（如果有SSL证书）
    ssl_certificate /www/server/panel/vhost/cert/your-domain.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/your-domain.com/privkey.pem;
    
    root /www/wwwroot/pipeline-frontend;
    index index.html;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传代理
    location /uploads/ {
        proxy_pass http://127.0.0.1:3001/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 7.2 后台管理网站Nginx配置
点击后台管理网站的"设置" → "配置文件"，替换为：

```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name admin.your-domain.com;
    
    # SSL配置
    ssl_certificate /www/server/panel/vhost/cert/admin.your-domain.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/admin.your-domain.com/privkey.pem;
    
    root /www/wwwroot/pipeline-admin;
    index index.html;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 第八步：配置SSL证书

### 8.1 申请免费SSL证书
1. 在网站设置中点击"SSL"
2. 选择"Let's Encrypt"
3. 输入邮箱地址
4. 点击"申请"

### 8.2 强制HTTPS
在SSL设置中开启"强制HTTPS"

## 第九步：配置防火墙

### 9.1 开放必要端口
在宝塔面板的"安全"中开放：
- 80 (HTTP)
- 443 (HTTPS)
- 3001 (API，仅内网)

### 9.2 关闭不必要端口
确保数据库端口3306仅允许本地访问。

## 第十步：测试和验证

### 10.1 检查服务状态
1. PM2管理器中查看API服务状态
2. 访问 `https://your-domain.com/api/health` 检查API
3. 访问前端和后台管理界面

### 10.2 功能测试
1. 用户登录：admin / admin123
2. 上传图片功能
3. 数据库读写功能

## 维护和监控

### 日志查看
- **API日志**: PM2管理器 → 查看日志
- **Nginx日志**: 网站设置 → 日志
- **数据库日志**: MySQL管理

### 备份设置
1. 数据库自动备份：数据库 → 备份设置
2. 网站文件备份：网站 → 备份设置
3. 定期下载备份文件

### 性能监控
- 使用宝塔面板的系统监控
- 设置告警通知
- 定期检查磁盘空间

## 故障排除

### 常见问题

1. **API无法访问**
   - 检查PM2服务状态
   - 查看API日志
   - 检查端口是否开放

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接信息
   - 查看数据库日志

3. **文件上传失败**
   - 检查uploads目录权限
   - 查看磁盘空间
   - 检查Nginx配置

4. **SSL证书问题**
   - 重新申请证书
   - 检查域名解析
   - 查看Nginx错误日志

## 安全建议

1. **修改默认密码**
2. **定期更新系统**
3. **配置防火墙规则**
4. **启用访问日志**
5. **定期备份数据**
6. **监控异常访问**

## 更新部署

### 更新后端
```bash
cd /www/wwwroot/pipeline-backend
git pull
cd backend
npm install --production
# 在PM2管理器中重启服务
```

### 更新前端
1. 本地构建新版本
2. 上传到对应目录
3. 清除浏览器缓存

通过宝塔面板部署，您可以享受到可视化管理、自动备份、SSL证书管理等便利功能！
