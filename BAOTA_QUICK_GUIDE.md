# 宝塔面板快速部署指南

## 🚀 5 分钟快速部署

### 第一步：准备环境

1. 安装宝塔面板
2. 在软件商店安装：
   - Nginx 1.20+
   - MySQL 8.0
   - Node.js 版本管理器
   - PM2 管理器

### 第二步：创建数据库

1. 数据库 → 添加数据库
   - 数据库名：`pipeline_inspection`
   - 用户名：`pipeline_user`
   - 密码：设置强密码

### 第三步：上传代码

#### 方法 1：使用 Git 克隆（推荐）

**适用场景：** 代码托管在 Git 仓库（如 GitHub、GitLab、Gitee 等）

1. **打开宝塔面板终端**

   - 登录宝塔面板
   - 点击左侧菜单"终端"
   - 或者使用 SSH 客户端连接服务器

2. **切换到网站根目录**

   ```bash
   cd /www/wwwroot
   ```

3. **克隆代码仓库**

   ```bash
   # 替换<your-repo-url>为你的实际仓库地址
   git clone <your-repo-url> pipeline-backend

   # 示例：
   # git clone https://github.com/username/pipeline-project.git pipeline-backend
   # git clone https://gitee.com/username/pipeline-project.git pipeline-backend
   ```

4. **验证上传结果**
   ```bash
   ls -la pipeline-backend/
   # 应该看到项目文件夹结构，包含backend、frontend、admin等目录
   ```

#### 方法 2：文件上传

**适用场景：** 没有 Git 仓库或需要上传本地修改的代码

1. **准备代码包**

   - 将整个项目打包为 zip 或 tar.gz 格式
   - 确保包含所有必要文件（backend、frontend、admin 目录等）

2. **通过宝塔面板上传**

   - 登录宝塔面板
   - 点击"文件"菜单
   - 导航到`/www/wwwroot/`目录
   - 点击"上传"按钮
   - 选择你的代码压缩包
   - 等待上传完成

3. **解压文件**

   - 在文件管理器中右键点击上传的压缩包
   - 选择"解压"
   - 解压到当前目录
   - 将解压后的文件夹重命名为`pipeline-backend`

4. **或使用终端解压**

   ```bash
   cd /www/wwwroot
   # 如果是zip文件
   unzip your-project.zip
   mv your-project-folder pipeline-backend

   # 如果是tar.gz文件
   tar -xzf your-project.tar.gz
   mv your-project-folder pipeline-backend
   ```

#### 方法 3：使用 FTP/SFTP 客户端

**适用场景：** 习惯使用 FTP 工具的用户

1. **获取服务器信息**

   - 服务器 IP 地址
   - SSH 端口（通常是 22）
   - 用户名和密码

2. **使用 FTP 客户端**
   - 推荐工具：FileZilla、WinSCP、Xftp 等
   - 连接到服务器
   - 导航到`/www/wwwroot/`目录
   - 创建`pipeline-backend`文件夹
   - 上传所有项目文件到该文件夹

#### 验证上传成功

无论使用哪种方法，上传完成后都需要验证：

1. **检查目录结构**

   ```bash
   cd /www/wwwroot/pipeline-backend
   ls -la
   ```

   应该看到类似以下结构：

   ```
   drwxr-xr-x  backend/     # 后端代码
   drwxr-xr-x  frontend/    # 前端代码
   drwxr-xr-x  admin/       # 管理后台代码
   -rw-r--r--  README.md    # 项目说明
   -rw-r--r--  package.json # 项目配置
   ```

2. **设置文件权限**

   ```bash
   # 设置正确的文件所有者和权限
   chown -R www:www /www/wwwroot/pipeline-backend
   chmod -R 755 /www/wwwroot/pipeline-backend
   ```

3. **检查关键文件**

   ```bash
   # 确认后端入口文件存在
   ls -la /www/wwwroot/pipeline-backend/backend/src/app.js

   # 确认环境配置示例文件存在
   ls -la /www/wwwroot/pipeline-backend/backend/.env.example
   ```

#### 常见问题解决

**问题 1：权限不足**

```bash
# 解决方案：使用sudo或切换到root用户
sudo chown -R www:www /www/wwwroot/pipeline-backend
```

**问题 2：磁盘空间不足**

```bash
# 检查磁盘空间
df -h
# 清理不必要的文件或扩容
```

**问题 3：Git 克隆失败**

```bash
# 检查网络连接
ping github.com
# 或使用国内镜像
git clone https://gitee.com/your-repo pipeline-backend
```

### 第四步：配置后端

```bash
cd /www/wwwroot/pipeline-backend/backend
npm install --production

# 创建环境配置
cp .env.example .env
# 编辑.env文件，修改数据库密码
```

### 第五步：导入数据库

1. 进入 phpMyAdmin
2. 选择`pipeline_inspection`数据库
3. 导入`backend/baota-database-config.sql`

### 第六步：启动后端服务

1. 打开 PM2 管理器
2. 添加项目：
   - 项目名：`pipeline-api`
   - 目录：`/www/wwwroot/pipeline-backend/backend`
   - 启动文件：`src/app.js`
3. 启动服务

### 第七步：创建网站

1. 添加站点：
   - 域名：`your-domain.com`
   - 目录：`/www/wwwroot/pipeline-frontend`
2. 添加站点：
   - 域名：`admin.your-domain.com`
   - 目录：`/www/wwwroot/pipeline-admin`

### 第八步：配置 Nginx

复制以下配置到对应网站的配置文件中：

**前端网站配置：**

```nginx
location /api/ {
    proxy_pass http://127.0.0.1:3001/api/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

location /uploads/ {
    proxy_pass http://127.0.0.1:3001/uploads/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

### 第九步：部署前端文件

1. 本地构建：`npm run build:h5`
2. 上传`dist/build/h5/*`到`/www/wwwroot/pipeline-frontend/`
3. 本地构建：`cd admin && npm run build`
4. 上传`admin/dist/*`到`/www/wwwroot/pipeline-admin/`

### 第十步：申请 SSL 证书

1. 网站设置 → SSL → Let's Encrypt
2. 开启强制 HTTPS

## ✅ 完成！

访问地址：

- 前端：https://your-domain.com
- 后台：https://admin.your-domain.com
- 默认账户：admin / admin123

## 🔧 常用命令

### PM2 管理

```bash
pm2 list                 # 查看进程
pm2 restart pipeline-api # 重启服务
pm2 logs pipeline-api    # 查看日志
pm2 stop pipeline-api    # 停止服务
```

### 文件权限

```bash
chown -R www:www /www/wwwroot/pipeline-backend
chmod -R 755 /www/wwwroot/pipeline-backend
chmod -R 777 /www/wwwroot/pipeline-backend/backend/uploads
```

### 数据库备份

```bash
# 在宝塔面板数据库管理中设置自动备份
# 或手动备份：
mysqldump -u pipeline_user -p pipeline_inspection > backup.sql
```

## 🚨 故障排除

### API 无法访问

1. 检查 PM2 服务状态
2. 查看端口 3001 是否开放
3. 检查 Nginx 配置

### 数据库连接失败

1. 检查.env 文件配置
2. 验证数据库用户权限
3. 查看 MySQL 服务状态

### 文件上传失败

1. 检查 uploads 目录权限
2. 查看磁盘空间
3. 检查 Nginx 上传大小限制

## 📞 技术支持

遇到问题请：

1. 查看宝塔面板日志
2. 检查 PM2 服务日志
3. 查看 Nginx 错误日志
4. 联系技术支持团队
