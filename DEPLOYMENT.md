# 石化管道巡检系统部署指南

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │  后台管理系统   │    │   后端API       │
│  (Vue3+uni-app) │    │ (Vue3+Element)  │    │ (Node.js+MySQL) │
│   Port: 80      │    │   Port: 8080    │    │   Port: 3001    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Nginx代理     │
                    │ HTTPS: 443/8443 │
                    └─────────────────┘
```

## 部署方式

### 方式一：Docker部署（推荐）

#### 1. 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 服务器内存 >= 2GB
- 磁盘空间 >= 10GB

#### 2. 快速部署
```bash
# 克隆项目
git clone <your-repo-url>
cd pipeline-inspection

# 执行部署脚本
chmod +x deploy.sh
./deploy.sh
```

#### 3. 手动部署
```bash
# 创建必要目录
mkdir -p uploads logs nginx/ssl

# 生成SSL证书（开发环境）
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem \
    -subj "/C=CN/ST=State/L=City/O=Organization/CN=your-domain.com"

# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 方式二：传统部署

#### 1. 环境要求
- Node.js 18+
- MySQL 8.0+
- Nginx 1.20+
- PM2（进程管理）

#### 2. 数据库设置
```bash
# 创建数据库
mysql -u root -p < backend/database/init.sql
```

#### 3. 后端部署
```bash
cd backend
npm install --production
npm install -g pm2

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 启动服务
pm2 start ecosystem.config.js --env production
```

#### 4. 前端构建
```bash
# 前端应用
npm install
npm run build:h5

# 后台管理
cd admin
npm install
npm run build
```

#### 5. Nginx配置
```bash
# 复制配置文件
cp nginx/nginx.conf /etc/nginx/sites-available/pipeline-inspection
ln -s /etc/nginx/sites-available/pipeline-inspection /etc/nginx/sites-enabled/

# 重启Nginx
systemctl restart nginx
```

## 配置说明

### 环境变量配置

#### 后端环境变量 (.env)
```env
NODE_ENV=production
PORT=3001

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=pipeline_inspection
DB_USER=pipeline_user
DB_PASS=your-secure-password

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# 文件上传路径
UPLOAD_PATH=/var/www/pipeline-inspection/uploads
```

#### Docker环境变量
修改 `docker-compose.yml` 中的环境变量：
```yaml
environment:
  DB_HOST: mysql
  DB_USER: pipeline_user
  DB_PASS: your-secure-password
  JWT_SECRET: your-production-jwt-secret
```

### 域名配置

修改 `nginx/nginx.conf` 中的域名：
```nginx
server_name your-domain.com;        # 前端应用
server_name admin.your-domain.com;  # 后台管理
```

### SSL证书配置

#### 使用Let's Encrypt（推荐）
```bash
# 安装certbot
apt install certbot python3-certbot-nginx

# 获取证书
certbot --nginx -d your-domain.com -d admin.your-domain.com

# 自动续期
crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 使用自签名证书（开发环境）
```bash
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem
```

## 访问地址

部署完成后，可通过以下地址访问：

- **前端应用**: https://your-domain.com
- **后台管理**: https://admin.your-domain.com:8443
- **API接口**: https://your-domain.com/api

## 默认账户

- **用户名**: admin
- **密码**: admin123

⚠️ **重要**: 首次登录后请立即修改默认密码！

## 监控和维护

### 查看日志
```bash
# Docker部署
docker-compose logs -f

# 传统部署
pm2 logs
tail -f /var/log/nginx/access.log
```

### 服务管理
```bash
# Docker部署
docker-compose restart    # 重启所有服务
docker-compose stop       # 停止所有服务
docker-compose down       # 停止并删除容器

# 传统部署
pm2 restart all          # 重启后端服务
systemctl restart nginx # 重启Nginx
```

### 数据备份
```bash
# 数据库备份
docker exec pipeline-mysql mysqldump -u root -p pipeline_inspection > backup.sql

# 文件备份
tar -czf uploads-backup.tar.gz uploads/
```

### 性能优化

#### 1. 数据库优化
- 定期清理过期日志
- 优化查询索引
- 配置数据库连接池

#### 2. 静态资源优化
- 启用Gzip压缩
- 配置浏览器缓存
- 使用CDN加速

#### 3. 服务器优化
- 配置防火墙
- 设置文件描述符限制
- 监控系统资源使用

## 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
netstat -tlnp | grep :3001

# 检查日志
docker-compose logs backend
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"

# 检查网络连接
docker-compose exec backend ping mysql
```

#### 3. 文件上传失败
```bash
# 检查目录权限
ls -la uploads/
chmod 755 uploads/

# 检查磁盘空间
df -h
```

#### 4. SSL证书问题
```bash
# 检查证书有效期
openssl x509 -in nginx/ssl/cert.pem -text -noout

# 测试SSL配置
nginx -t
```

## 安全建议

1. **修改默认密码**: 首次部署后立即修改
2. **使用HTTPS**: 生产环境必须使用SSL证书
3. **防火墙配置**: 只开放必要端口
4. **定期更新**: 及时更新系统和依赖包
5. **数据备份**: 定期备份数据库和文件
6. **访问控制**: 配置IP白名单或VPN访问
7. **日志监控**: 监控异常访问和错误日志

## 技术支持

如遇到部署问题，请检查：
1. 系统日志和错误信息
2. 网络连接和防火墙设置
3. 环境变量和配置文件
4. 服务器资源使用情况

更多技术支持请联系开发团队。
