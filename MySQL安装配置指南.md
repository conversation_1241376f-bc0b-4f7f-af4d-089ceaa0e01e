# 🗄️ MySQL安装配置指南

## 📋 当前状态
检测到您的系统中没有运行MySQL服务，需要先安装和配置MySQL数据库。

## 🚀 快速安装MySQL

### 方法1：使用MySQL官方安装包 (推荐)

#### 1.1 下载MySQL
访问官方网站下载：https://dev.mysql.com/downloads/mysql/
- 选择 "MySQL Community Server"
- 选择 Windows 版本
- 下载 MSI 安装包

#### 1.2 安装MySQL
1. 运行下载的MSI安装包
2. 选择 "Developer Default" 安装类型
3. 设置root用户密码（请记住这个密码）
4. 完成安装

#### 1.3 验证安装
打开命令行，输入：
```bash
mysql --version
```

### 方法2：使用XAMPP (简单快速)

#### 2.1 下载XAMPP
访问：https://www.apachefriends.org/
- 下载Windows版本的XAMPP
- 安装时确保选中MySQL组件

#### 2.2 启动MySQL
1. 打开XAMPP控制面板
2. 点击MySQL旁边的"Start"按钮
3. 确保状态显示为绿色"Running"

#### 2.3 设置密码（可选）
1. 在XAMPP控制面板点击MySQL的"Admin"
2. 进入phpMyAdmin
3. 点击"用户账户"设置root密码

### 方法3：使用Docker (适合开发者)

#### 3.1 安装Docker Desktop
下载并安装：https://www.docker.com/products/docker-desktop

#### 3.2 运行MySQL容器
```bash
docker run --name mysql-pipeline -e MYSQL_ROOT_PASSWORD=123456 -p 3306:3306 -d mysql:8.0
```

## ⚙️ 配置数据库

### 1. 创建数据库
无论使用哪种方法安装，都需要创建数据库：

#### 使用命令行：
```bash
mysql -u root -p
CREATE DATABASE pipeline_inspection DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit
```

#### 使用phpMyAdmin（XAMPP用户）：
1. 访问 http://localhost/phpmyadmin
2. 点击"新建"
3. 数据库名称：`pipeline_inspection`
4. 排序规则：`utf8mb4_unicode_ci`
5. 点击"创建"

### 2. 更新后端配置
编辑 `backend/.env` 文件：

```env
# 根据您的MySQL配置修改
DB_HOST=localhost
DB_PORT=3306
DB_NAME=pipeline_inspection
DB_USER=root
DB_PASSWORD=您设置的MySQL密码
```

## 🧪 测试连接

### 1. 测试MySQL连接
```bash
mysql -u root -p -e "SHOW DATABASES;"
```

### 2. 启动后端服务
```bash
cd backend
npm run dev
```

## 🔧 常见问题解决

### 问题1：MySQL服务无法启动
**解决方案：**
1. 检查端口3306是否被占用
2. 以管理员身份运行安装程序
3. 重启计算机后再试

### 问题2：忘记MySQL密码
**解决方案：**
1. 停止MySQL服务
2. 以安全模式启动MySQL
3. 重置root密码

### 问题3：连接被拒绝
**解决方案：**
1. 确认MySQL服务正在运行
2. 检查防火墙设置
3. 验证用户名密码正确

## 📞 获取帮助

### 检查MySQL状态
```bash
# Windows服务方式
sc query mysql80

# XAMPP方式
# 查看XAMPP控制面板中MySQL状态

# Docker方式
docker ps | findstr mysql
```

### 查看MySQL日志
- **Windows服务**：查看Windows事件查看器
- **XAMPP**：查看 `xampp/mysql/data/` 目录下的错误日志
- **Docker**：`docker logs mysql-pipeline`

## 🎯 推荐配置

### 开发环境推荐
- **新手用户**：使用XAMPP，简单易用
- **有经验用户**：使用MySQL官方安装包
- **Docker用户**：使用Docker容器

### 生产环境推荐
- 使用MySQL官方安装包
- 设置强密码
- 配置定期备份
- 启用SSL连接

## ✅ 完成后的下一步

MySQL安装配置完成后：

1. **更新.env文件**中的数据库密码
2. **运行数据库迁移**：`npm run migrate`
3. **启动后端服务**：`npm run dev`
4. **测试API**：访问 http://localhost:3001/health

---

**需要帮助？**
如果在安装过程中遇到问题，请：
1. 检查错误信息
2. 确认系统权限
3. 查看防火墙设置
4. 重启相关服务

安装完成后，请返回主启动指南继续后续步骤！
