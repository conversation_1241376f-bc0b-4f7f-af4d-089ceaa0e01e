{"name": "pipeline-inspection-admin", "version": "1.0.0", "description": "管道巡检系统后台管理", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "npm run serve"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0", "element-plus": "^2.3.8", "axios": "^1.4.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "@element-plus/icons-vue": "^2.1.0", "dayjs": "^1.11.9", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^8.0.1", "eslint": "^8.45.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.15.1", "sass": "^1.64.1", "sass-loader": "^13.3.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}