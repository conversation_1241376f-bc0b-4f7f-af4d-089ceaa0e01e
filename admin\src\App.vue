<template>
  <div id="app">
    <!-- 登录页面不使用布局 -->
    <template v-if="$route.path === '/login'">
      <router-view />
    </template>

    <!-- 其他页面使用主布局 -->
    <MainLayout v-else>
      <router-view />
    </MainLayout>
  </div>
</template>

<script>
import MainLayout from '@/layout/MainLayout.vue'

export default {
  name: 'App',
  components: {
    MainLayout
  }
}
</script>

<style lang="scss">
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f0f2f5;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Element Plus 样式覆盖
.el-menu {
  border-right: none !important;
}

.el-menu-item {
  &:hover {
    background-color: #ecf5ff !important;
    color: #409eff !important;
  }
  
  &.is-active {
    background-color: #409eff !important;
    color: #fff !important;
  }
}

.el-submenu__title {
  &:hover {
    background-color: #ecf5ff !important;
    color: #409eff !important;
  }
}

// 卡片样式
.dashboard-card {
  margin-bottom: 20px;
  
  .el-card__body {
    padding: 20px;
  }
}

// 统计卡片
.stat-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 20px;
  
  .stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    opacity: 0.9;
  }
  
  &.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  &.warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }
  
  &.danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
  }
  
  &.info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
}

// 表格样式
.data-table {
  .el-table__header {
    background-color: #fafafa;
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 状态标签
.status-tag {
  &.active {
    color: #67c23a;
  }
  
  &.inactive {
    color: #f56c6c;
  }
  
  &.normal {
    color: #67c23a;
  }
  
  &.abnormal {
    color: #f56c6c;
  }
  
  &.warning {
    color: #e6a23c;
  }
}

// 响应式布局
@media (max-width: 768px) {
  .el-aside {
    width: 200px !important;
  }
  
  .stat-card {
    margin-bottom: 15px;
    
    .stat-number {
      font-size: 24px;
    }
  }
}
</style>
