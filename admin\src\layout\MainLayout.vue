<template>
  <div class="main-layout">
    <el-container class="layout">
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <div class="logo">
          <h2>管道巡检系统</h2>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          background-color="#001529"
          text-color="rgba(255, 255, 255, 0.65)"
          active-text-color="#fff"
          router
        >
          <el-menu-item index="/dashboard">
            <el-icon><Odometer /></el-icon>
            <span>仪表板</span>
          </el-menu-item>
          <el-menu-item index="/users">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
          <el-menu-item index="/devices">
            <el-icon><Monitor /></el-icon>
            <span>设备管理</span>
          </el-menu-item>
          <el-menu-item index="/inspections">
            <el-icon><Document /></el-icon>
            <span>巡检记录</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container class="main-content">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <h3>{{ pageTitle }}</h3>
          </div>
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="currentUser?.avatar">
                  {{ currentUser?.name?.charAt(0) || 'A' }}
                </el-avatar>
                <span class="username">{{ currentUser?.name || '管理员' }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                  <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 页面内容 -->
        <el-main class="content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'MainLayout',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    // 计算属性
    const currentUser = computed(() => store.getters.currentUser)
    const activeMenu = computed(() => route.path)
    const pageTitle = computed(() => {
      const titles = {
        '/dashboard': '仪表板',
        '/users': '用户管理',
        '/devices': '设备管理',
        '/inspections': '巡检记录'
      }
      return titles[route.path] || '管道巡检系统'
    })
    
    // 处理用户菜单命令
    const handleCommand = async (command) => {
      switch (command) {
        case 'profile':
          ElMessage.info('个人信息功能开发中...')
          break
        case 'settings':
          ElMessage.info('系统设置功能开发中...')
          break
        case 'logout':
          await handleLogout()
          break
      }
    }
    
    // 退出登录
    const handleLogout = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '确认退出',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        store.dispatch('logout')
        ElMessage.success('已退出登录')
        router.push('/login')
        
      } catch (error) {
        // 用户取消退出
      }
    }
    
    return {
      currentUser,
      activeMenu,
      pageTitle,
      handleCommand
    }
  }
}
</script>

<style lang="scss" scoped>
.layout {
  height: 100vh;
  
  .sidebar {
    background: #001529;
    
    .logo {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid #1f1f1f;
      
      h2 {
        color: white;
        margin: 0;
        font-size: 16px;
      }
    }
    
    .sidebar-menu {
      border-right: none;
    }
  }
  
  .main-content {
    .header {
      background: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      box-shadow: 0 1px 4px rgba(0,21,41,.08);
      
      .header-left h3 {
        margin: 0;
        color: #333;
      }
      
      .header-right {
        .user-info {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          padding: 8px 12px;
          border-radius: 4px;
          transition: background-color 0.3s;
          
          &:hover {
            background-color: #f5f5f5;
          }
          
          .username {
            color: #333;
            font-size: 14px;
          }
        }
      }
    }
    
    .content {
      background: #f0f2f5;
      padding: 24px;
    }
  }
}
</style>
