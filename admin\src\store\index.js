import { createStore } from 'vuex'
import axios from 'axios'

// 配置axios
axios.defaults.baseURL = 'http://localhost:3001/api'
axios.defaults.timeout = 10000

// 请求拦截器
axios.interceptors.request.use(
  config => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default createStore({
  state: {
    user: JSON.parse(localStorage.getItem('admin_user') || 'null'),
    token: localStorage.getItem('admin_token') || '',
    loading: false
  },
  
  getters: {
    isLoggedIn: state => !!state.token,
    currentUser: state => state.user
  },
  
  mutations: {
    SET_USER(state, user) {
      state.user = user
      localStorage.setItem('admin_user', JSON.stringify(user))
    },
    
    SET_TOKEN(state, token) {
      state.token = token
      localStorage.setItem('admin_token', token)
    },
    
    CLEAR_AUTH(state) {
      state.user = null
      state.token = ''
      localStorage.removeItem('admin_user')
      localStorage.removeItem('admin_token')
    },
    
    SET_LOADING(state, loading) {
      state.loading = loading
    }
  },
  
  actions: {
    // 登录
    async login({ commit }, credentials) {
      try {
        commit('SET_LOADING', true)
        const response = await axios.post('/auth/login', credentials)
        
        if (response.token && response.user) {
          commit('SET_TOKEN', response.token)
          commit('SET_USER', response.user)
          return { success: true }
        } else {
          throw new Error('登录响应格式错误')
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { 
          success: false, 
          message: error.response?.data?.error || error.message || '登录失败'
        }
      } finally {
        commit('SET_LOADING', false)
      }
    },
    
    // 登出
    logout({ commit }) {
      commit('CLEAR_AUTH')
    },
    
    // 获取用户信息
    async fetchUserInfo({ commit, state }) {
      if (!state.token) return
      
      try {
        const response = await axios.get('/auth/me')
        commit('SET_USER', response.user)
      } catch (error) {
        console.error('获取用户信息失败:', error)
        commit('CLEAR_AUTH')
      }
    }
  },
  
  modules: {
    // Dashboard模块
    dashboard: {
      namespaced: true,
      actions: {
        async getStats() {
          return await axios.get('/statistics/dashboard')
        },
        
        async getRecentInspections() {
          const response = await axios.get('/inspections?limit=10')
          return response.data || []
        },
        
        async getActiveUsers() {
          const response = await axios.get('/users?limit=10')
          return response.data || []
        },
        
        async getTrendData(_, period = '7d') {
          return await axios.get(`/statistics/trend?days=${period.replace('d', '')}`)
        },
        
        async getStatusData() {
          const stats = await axios.get('/statistics/dashboard')
          return {
            normal: stats.overview?.totalInspections - stats.overview?.abnormalInspections || 0,
            abnormal: stats.overview?.abnormalInspections || 0,
            warning: 0
          }
        }
      }
    },
    
    // 用户模块
    users: {
      namespaced: true,
      actions: {
        async getUsers(_, params = {}) {
          return await axios.get('/users', { params })
        },
        
        async createUser(_, userData) {
          return await axios.post('/users', userData)
        },
        
        async updateUser(_, { id, ...userData }) {
          return await axios.put(`/users/${id}`, userData)
        },
        
        async deleteUser(_, id) {
          return await axios.delete(`/users/${id}`)
        }
      }
    },
    
    // 设备模块
    devices: {
      namespaced: true,
      actions: {
        async getDevices(_, params = {}) {
          return await axios.get('/devices', { params })
        },
        
        async createDevice(_, deviceData) {
          return await axios.post('/devices', deviceData)
        },
        
        async updateDevice(_, { deviceId, ...deviceData }) {
          return await axios.put(`/devices/${deviceId}`, deviceData)
        },
        
        async deleteDevice(_, deviceId) {
          return await axios.delete(`/devices/${deviceId}`)
        }
      }
    },
    
    // 巡检记录模块
    inspections: {
      namespaced: true,
      actions: {
        async getInspections(_, params = {}) {
          return await axios.get('/inspections', { params })
        },
        
        async getInspection(_, id) {
          return await axios.get(`/inspections/${id}`)
        },
        
        async updateInspection(_, { id, ...data }) {
          return await axios.put(`/inspections/${id}`, data)
        },
        
        async deleteInspection(_, id) {
          return await axios.delete(`/inspections/${id}`)
        }
      }
    }
  }
})
