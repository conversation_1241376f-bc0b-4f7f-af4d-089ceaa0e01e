// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
}

#app {
  height: 100%;
}

// 布局样式
.layout {
  height: 100vh;
  
  .sidebar {
    background: #001529;
    color: white;
    
    .logo {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: bold;
      color: white;
      border-bottom: 1px solid #1f1f1f;
    }
  }
  
  .main-content {
    background: #f0f2f5;
    
    .header {
      background: white;
      padding: 0 24px;
      box-shadow: 0 1px 4px rgba(0,21,41,.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
    
    .content {
      padding: 24px;
      min-height: calc(100vh - 64px);
    }
  }
}

// 统计卡片样式
.stat-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  .stat-number {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #666;
  }
  
  &.info .stat-number {
    color: #409eff;
  }
  
  &.success .stat-number {
    color: #67c23a;
  }
  
  &.warning .stat-number {
    color: #e6a23c;
  }
  
  &.danger .stat-number {
    color: #f56c6c;
  }
}

// 仪表板卡片样式
.dashboard-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// 数据表格样式
.data-table {
  background: white;
  
  .el-table__header {
    background: #fafafa;
  }
}

// 状态标签样式
.status-tag {
  &.normal {
    color: #67c23a;
  }
  
  &.warning {
    color: #e6a23c;
  }
  
  &.abnormal {
    color: #f56c6c;
  }
}

// 登录页面样式
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .login-form {
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 400px;
    
    .title {
      text-align: center;
      margin-bottom: 30px;
      font-size: 24px;
      color: #333;
    }
    
    .el-form-item {
      margin-bottom: 24px;
    }
    
    .login-button {
      width: 100%;
      height: 44px;
      font-size: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .layout {
    .sidebar {
      position: fixed;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      
      &.mobile-open {
        transform: translateX(0);
      }
    }
    
    .main-content {
      margin-left: 0;
    }
  }
  
  .stat-card {
    margin-bottom: 16px;
    
    .stat-number {
      font-size: 24px;
    }
  }
  
  .dashboard-card {
    margin-bottom: 16px;
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-20 {
  margin-top: 20px;
}

// Element Plus 自定义样式
.el-menu--dark {
  background-color: #001529 !important;
  
  .el-menu-item {
    color: rgba(255, 255, 255, 0.65);
    
    &:hover {
      background-color: #1890ff !important;
      color: white;
    }
    
    &.is-active {
      background-color: #1890ff !important;
      color: white;
    }
  }
  
  .el-sub-menu__title {
    color: rgba(255, 255, 255, 0.65);
    
    &:hover {
      background-color: #1890ff !important;
      color: white;
    }
  }
}

// 进度条自定义样式
#nprogress .bar {
  background: #1890ff !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px #1890ff, 0 0 5px #1890ff !important;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}
