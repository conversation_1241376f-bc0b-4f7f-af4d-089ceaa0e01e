<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card info">
          <div class="stat-number">{{ stats.totalUsers }}</div>
          <div class="stat-label">总用户数</div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card success">
          <div class="stat-number">{{ stats.totalInspections }}</div>
          <div class="stat-label">总巡检次数</div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card warning">
          <div class="stat-number">{{ stats.abnormalInspections }}</div>
          <div class="stat-label">异常巡检</div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stat-card danger">
          <div class="stat-number">{{ stats.totalDevices }}</div>
          <div class="stat-label">设备总数</div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <!-- 巡检趋势图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>巡检趋势</span>
              <el-select v-model="trendPeriod" size="small" style="width: 100px">
                <el-option label="7天" value="7d" />
                <el-option label="30天" value="30d" />
                <el-option label="90天" value="90d" />
              </el-select>
            </div>
          </template>
          <v-chart 
            :option="trendChartOption" 
            style="height: 300px"
            autoresize
          />
        </el-card>
      </el-col>

      <!-- 巡检状态分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <template #header>
            <span>巡检状态分布</span>
          </template>
          <v-chart 
            :option="statusChartOption" 
            style="height: 300px"
            autoresize
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-row :gutter="20">
      <!-- 最近巡检记录 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>最近巡检记录</span>
              <el-button type="text" @click="$router.push('/inspections')">
                查看全部
              </el-button>
            </div>
          </template>
          <el-table 
            :data="recentInspections" 
            class="data-table"
            size="small"
          >
            <el-table-column prop="device_id" label="设备ID" width="100" />
            <el-table-column prop="user.display_name" label="巡检员" width="80" />
            <el-table-column prop="inspection_status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag 
                  :type="getStatusType(row.inspection_status)" 
                  size="small"
                >
                  {{ getStatusText(row.inspection_status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="inspection_time" label="时间" width="120">
              <template #default="{ row }">
                {{ formatTime(row.inspection_time) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 活跃用户 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>活跃用户</span>
              <el-button type="text" @click="$router.push('/users')">
                查看全部
              </el-button>
            </div>
          </template>
          <el-table 
            :data="activeUsers" 
            class="data-table"
            size="small"
          >
            <el-table-column prop="display_name" label="姓名" width="80" />
            <el-table-column prop="department" label="部门" width="100" />
            <el-table-column prop="total_inspections" label="巡检次数" width="80" />
            <el-table-column prop="normal_rate" label="正常率" width="80">
              <template #default="{ row }">
                <span :class="getRateClass(row.normal_rate)">
                  {{ row.normal_rate }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import dayjs from 'dayjs'

export default {
  name: 'Dashboard',
  setup() {
    const store = useStore()
    
    // 响应式数据
    const stats = reactive({
      totalUsers: 0,
      totalInspections: 0,
      abnormalInspections: 0,
      totalDevices: 0
    })
    
    const trendPeriod = ref('7d')
    const recentInspections = ref([])
    const activeUsers = ref([])
    
    // 图表配置
    const trendChartOption = ref({
      title: {
        text: '巡检趋势',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['正常', '异常', '警告'],
        bottom: 0
      },
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '正常',
          type: 'line',
          data: [],
          smooth: true,
          itemStyle: { color: '#67c23a' }
        },
        {
          name: '异常',
          type: 'line',
          data: [],
          smooth: true,
          itemStyle: { color: '#f56c6c' }
        },
        {
          name: '警告',
          type: 'line',
          data: [],
          smooth: true,
          itemStyle: { color: '#e6a23c' }
        }
      ]
    })
    
    const statusChartOption = ref({
      title: {
        text: '状态分布',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '巡检状态',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 0, name: '正常', itemStyle: { color: '#67c23a' } },
            { value: 0, name: '异常', itemStyle: { color: '#f56c6c' } },
            { value: 0, name: '警告', itemStyle: { color: '#e6a23c' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    })

    // 方法
    const loadDashboardData = async () => {
      try {
        // 加载统计数据
        const statsData = await store.dispatch('dashboard/getStats')
        Object.assign(stats, statsData)
        
        // 加载最近巡检记录
        recentInspections.value = await store.dispatch('dashboard/getRecentInspections')
        
        // 加载活跃用户
        activeUsers.value = await store.dispatch('dashboard/getActiveUsers')
        
        // 加载图表数据
        await loadChartData()
        
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
      }
    }
    
    const loadChartData = async () => {
      try {
        // 加载趋势数据
        const trendData = await store.dispatch('dashboard/getTrendData', trendPeriod.value)
        trendChartOption.value.xAxis.data = trendData.dates
        trendChartOption.value.series[0].data = trendData.normal
        trendChartOption.value.series[1].data = trendData.abnormal
        trendChartOption.value.series[2].data = trendData.warning
        
        // 加载状态分布数据
        const statusData = await store.dispatch('dashboard/getStatusData')
        statusChartOption.value.series[0].data = [
          { value: statusData.normal, name: '正常' },
          { value: statusData.abnormal, name: '异常' },
          { value: statusData.warning, name: '警告' }
        ]
        
      } catch (error) {
        console.error('加载图表数据失败:', error)
      }
    }
    
    const getStatusType = (status) => {
      const types = {
        normal: 'success',
        abnormal: 'danger',
        warning: 'warning'
      }
      return types[status] || 'info'
    }
    
    const getStatusText = (status) => {
      const texts = {
        normal: '正常',
        abnormal: '异常',
        warning: '警告'
      }
      return texts[status] || status
    }
    
    const getRateClass = (rate) => {
      if (rate >= 95) return 'status-tag normal'
      if (rate >= 85) return 'status-tag warning'
      return 'status-tag abnormal'
    }
    
    const formatTime = (time) => {
      return dayjs(time).format('MM-DD HH:mm')
    }

    // 监听趋势周期变化
    watch(trendPeriod, () => {
      loadChartData()
    })

    // 生命周期
    onMounted(() => {
      loadDashboardData()
    })

    return {
      stats,
      trendPeriod,
      recentInspections,
      activeUsers,
      trendChartOption,
      statusChartOption,
      getStatusType,
      getStatusText,
      getRateClass,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .stats-row {
    margin-bottom: 15px;
  }
}
</style>
