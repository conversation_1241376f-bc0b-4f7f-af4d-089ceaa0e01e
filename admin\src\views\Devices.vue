<template>
  <div class="devices-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>设备管理</span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增设备
          </el-button>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :model="searchForm" inline>
          <el-form-item label="搜索">
            <el-input
              v-model="searchForm.search"
              placeholder="设备ID、设备名称、位置"
              clearable
              @clear="handleSearch"
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="设备类型">
            <el-select v-model="searchForm.type" placeholder="选择类型" clearable>
              <el-option label="输送管道" value="输送管道" />
              <el-option label="控制阀门" value="控制阀门" />
              <el-option label="增压设备" value="增压设备" />
              <el-option label="监测设备" value="监测设备" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
              <el-option label="正常" value="active" />
              <el-option label="维护中" value="maintenance" />
              <el-option label="停用" value="inactive" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 设备表格 -->
      <el-table
        v-loading="loading"
        :data="devices"
        style="width: 100%"
      >
        <el-table-column prop="device_id" label="设备ID" width="120" />
        <el-table-column prop="device_name" label="设备名称" width="150" />
        <el-table-column prop="device_type" label="设备类型" width="120" />
        <el-table-column prop="location" label="位置" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editDevice(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteDevice(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

export default {
  name: 'Devices',
  setup() {
    const store = useStore()
    
    const loading = ref(false)
    const devices = ref([])
    
    const searchForm = reactive({
      search: '',
      type: '',
      status: ''
    })
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })
    
    // 获取设备列表
    const fetchDevices = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.page,
          limit: pagination.limit,
          ...searchForm
        }
        
        const response = await store.dispatch('devices/getDevices', params)
        devices.value = response.data || []
        pagination.total = response.pagination?.total || 0
        
      } catch (error) {
        console.error('获取设备列表失败:', error)
        ElMessage.error('获取设备列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索
    const handleSearch = () => {
      pagination.page = 1
      fetchDevices()
    }
    
    // 重置搜索
    const resetSearch = () => {
      searchForm.search = ''
      searchForm.type = ''
      searchForm.status = ''
      pagination.page = 1
      fetchDevices()
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.limit = size
      pagination.page = 1
      fetchDevices()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      fetchDevices()
    }
    
    // 显示创建对话框
    const showCreateDialog = () => {
      ElMessage.info('创建设备功能开发中...')
    }
    
    // 编辑设备
    const editDevice = (device) => {
      ElMessage.info('编辑设备功能开发中...')
    }
    
    // 删除设备
    const deleteDevice = async (device) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除设备 "${device.device_id}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await store.dispatch('devices/deleteDevice', device.device_id)
        ElMessage.success('删除成功')
        fetchDevices()
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除设备失败:', error)
          ElMessage.error('删除设备失败')
        }
      }
    }
    
    // 获取状态类型
    const getStatusType = (status) => {
      const types = {
        active: 'success',
        maintenance: 'warning',
        inactive: 'danger'
      }
      return types[status] || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        active: '正常',
        maintenance: '维护中',
        inactive: '停用'
      }
      return texts[status] || status
    }
    
    // 格式化时间
    const formatTime = (time) => {
      return dayjs(time).format('YYYY-MM-DD HH:mm')
    }
    
    onMounted(() => {
      fetchDevices()
    })
    
    return {
      loading,
      devices,
      searchForm,
      pagination,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      showCreateDialog,
      editDevice,
      deleteDevice,
      getStatusType,
      getStatusText,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.devices-page {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .search-bar {
    margin-bottom: 20px;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
