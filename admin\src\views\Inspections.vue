<template>
  <div class="inspections-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>巡检记录</span>
          <el-button type="primary" @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :model="searchForm" inline>
          <el-form-item label="设备ID">
            <el-input
              v-model="searchForm.device_id"
              placeholder="设备ID"
              clearable
            />
          </el-form-item>
          <el-form-item label="巡检类型">
            <el-select v-model="searchForm.inspection_type" placeholder="选择类型" clearable>
              <el-option label="三角巡检" value="triangle" />
              <el-option label="向上巡检" value="upward" />
              <el-option label="常规巡检" value="normal" />
            </el-select>
          </el-form-item>
          <el-form-item label="巡检状态">
            <el-select v-model="searchForm.inspection_status" placeholder="选择状态" clearable>
              <el-option label="正常" value="normal" />
              <el-option label="异常" value="abnormal" />
              <el-option label="警告" value="warning" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 巡检记录表格 -->
      <el-table
        v-loading="loading"
        :data="inspections"
        style="width: 100%"
      >
        <el-table-column prop="device_id" label="设备ID" width="120" />
        <el-table-column prop="user.display_name" label="巡检员" width="100" />
        <el-table-column prop="inspection_type" label="巡检类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.inspection_type)">
              {{ getTypeText(row.inspection_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="inspection_status" label="巡检状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.inspection_status)">
              {{ getStatusText(row.inspection_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="位置" width="150" />
        <el-table-column prop="notes" label="备注" min-width="200" />
        <el-table-column prop="inspection_time" label="巡检时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.inspection_time) }}
          </template>
        </el-table-column>
        <el-table-column label="图片" width="100">
          <template #default="{ row }">
            <el-button
              v-if="row.images && row.images.length > 0"
              type="primary"
              size="small"
              @click="viewImages(row)"
            >
              {{ row.images.length }}张图片
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDetail(row)">
              查看
            </el-button>
            <el-button type="danger" size="small" @click="deleteInspection(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 图片查看对话框 -->
    <el-dialog
      v-model="imageDialogVisible"
      title="巡检图片"
      width="80%"
      center
    >
      <div class="image-gallery">
        <div v-if="currentImages.length === 0" class="no-images">
          暂无图片
        </div>
        <div v-else class="images-grid">
          <div
            v-for="(image, index) in currentImages"
            :key="index"
            class="image-item"
          >
            <div class="image-type">{{ getImageTypeText(image.image_type) }}</div>
            <el-image
              :src="getImageUrl(image.image_url)"
              :preview-src-list="previewList"
              :initial-index="index"
              fit="cover"
              class="preview-image"
              :preview-teleported="true"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <div>加载失败</div>
                </div>
              </template>
            </el-image>
            <div class="image-info">
              <div>文件名: {{ image.image_name || '未知' }}</div>
              <div>大小: {{ formatFileSize(image.file_size) }}</div>
              <div>上传时间: {{ formatTime(image.upload_time) }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

export default {
  name: 'Inspections',
  setup() {
    const store = useStore()
    
    const loading = ref(false)
    const inspections = ref([])
    const dateRange = ref([])
    const imageDialogVisible = ref(false)
    const currentImages = ref([])
    const previewList = ref([])
    
    const searchForm = reactive({
      device_id: '',
      inspection_type: '',
      inspection_status: ''
    })
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })
    
    // 获取巡检记录列表
    const fetchInspections = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.page,
          limit: pagination.limit,
          ...searchForm
        }
        
        // 添加时间范围
        if (dateRange.value && dateRange.value.length === 2) {
          params.start_date = dateRange.value[0]
          params.end_date = dateRange.value[1]
        }
        
        const response = await store.dispatch('inspections/getInspections', params)
        inspections.value = response.data || []
        pagination.total = response.pagination?.total || 0
        
      } catch (error) {
        console.error('获取巡检记录失败:', error)
        ElMessage.error('获取巡检记录失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索
    const handleSearch = () => {
      pagination.page = 1
      fetchInspections()
    }
    
    // 重置搜索
    const resetSearch = () => {
      searchForm.device_id = ''
      searchForm.inspection_type = ''
      searchForm.inspection_status = ''
      dateRange.value = []
      pagination.page = 1
      fetchInspections()
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.limit = size
      pagination.page = 1
      fetchInspections()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      fetchInspections()
    }
    
    // 查看详情
    const viewDetail = (inspection) => {
      ElMessage.info('查看详情功能开发中...')
    }
    
    // 删除巡检记录
    const deleteInspection = async (inspection) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除这条巡检记录吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await store.dispatch('inspections/deleteInspection', inspection.id)
        ElMessage.success('删除成功')
        fetchInspections()
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除巡检记录失败:', error)
          ElMessage.error('删除巡检记录失败')
        }
      }
    }
    
    // 导出数据
    const exportData = () => {
      ElMessage.info('导出功能开发中...')
    }

    // 查看图片
    const viewImages = (inspection) => {
      currentImages.value = inspection.images || []
      previewList.value = currentImages.value.map(img => getImageUrl(img.image_url))
      imageDialogVisible.value = true
    }

    // 获取图片URL
    const getImageUrl = (url) => {
      if (url.startsWith('http')) {
        return url
      }
      return `http://localhost:3001${url}`
    }

    // 获取图片类型文本
    const getImageTypeText = (type) => {
      const types = {
        left: '左侧图片',
        center: '中心图片',
        right: '右侧图片',
        upward: '向上图片',
        single: '单张图片'
      }
      return types[type] || type
    }

    // 格式化文件大小
    const formatFileSize = (size) => {
      if (!size) return '未知'
      if (size < 1024) return size + ' B'
      if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
      return (size / 1024 / 1024).toFixed(1) + ' MB'
    }
    
    // 获取类型颜色
    const getTypeColor = (type) => {
      const colors = {
        triangle: 'warning',
        upward: 'info',
        normal: 'success'
      }
      return colors[type] || 'info'
    }
    
    // 获取类型文本
    const getTypeText = (type) => {
      const texts = {
        triangle: '三角巡检',
        upward: '向上巡检',
        normal: '常规巡检'
      }
      return texts[type] || type
    }
    
    // 获取状态颜色
    const getStatusColor = (status) => {
      const colors = {
        normal: 'success',
        abnormal: 'danger',
        warning: 'warning'
      }
      return colors[status] || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        normal: '正常',
        abnormal: '异常',
        warning: '警告'
      }
      return texts[status] || status
    }
    
    // 格式化时间
    const formatTime = (time) => {
      return dayjs(time).format('YYYY-MM-DD HH:mm')
    }
    
    onMounted(() => {
      fetchInspections()
    })
    
    return {
      loading,
      inspections,
      searchForm,
      dateRange,
      pagination,
      imageDialogVisible,
      currentImages,
      previewList,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      viewDetail,
      deleteInspection,
      exportData,
      viewImages,
      getImageUrl,
      getImageTypeText,
      formatFileSize,
      getTypeColor,
      getTypeText,
      getStatusColor,
      getStatusText,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.inspections-page {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .search-bar {
    margin-bottom: 20px;
  }

  .pagination {
    margin-top: 20px;
    text-align: right;
  }
}

.image-gallery {
  .no-images {
    text-align: center;
    color: #999;
    padding: 40px;
  }

  .images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
  }

  .image-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background: white;

    .image-type {
      background: #f5f5f5;
      padding: 8px 12px;
      font-size: 12px;
      color: #666;
      text-align: center;
      border-bottom: 1px solid #e0e0e0;
    }

    .preview-image {
      width: 100%;
      height: 150px;
      cursor: pointer;
    }

    .image-error {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 150px;
      color: #999;
      background: #f5f5f5;

      .el-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }
    }

    .image-info {
      padding: 8px 12px;
      font-size: 12px;
      color: #666;
      line-height: 1.4;

      div {
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
