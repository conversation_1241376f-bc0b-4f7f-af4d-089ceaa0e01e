<template>
  <div class="login-container">
    <div class="login-form">
      <h2 class="title">管道巡检系统</h2>
      <p class="subtitle">后台管理系统</p>
      
      <el-form
        ref="loginForm"
        :model="loginData"
        :rules="rules"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginData.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginData.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="tips">
        <p>默认账户：admin / admin123</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export default {
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const loginForm = ref(null)
    const loading = ref(false)
    
    const loginData = reactive({
      username: 'admin',
      password: 'admin123'
    })
    
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度至少6位', trigger: 'blur' }
      ]
    }
    
    const handleLogin = async () => {
      try {
        const valid = await loginForm.value.validate()
        if (!valid) return
        
        loading.value = true
        
        const result = await store.dispatch('login', loginData)
        
        if (result.success) {
          ElMessage.success('登录成功')
          router.push('/dashboard')
        } else {
          ElMessage.error(result.message || '登录失败')
        }
        
      } catch (error) {
        console.error('登录错误:', error)
        ElMessage.error('登录失败，请检查网络连接')
      } finally {
        loading.value = false
      }
    }
    
    return {
      loginForm,
      loginData,
      rules,
      loading,
      handleLogin
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .login-form {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    width: 400px;
    
    .title {
      text-align: center;
      margin-bottom: 8px;
      font-size: 24px;
      color: #333;
      font-weight: bold;
    }
    
    .subtitle {
      text-align: center;
      margin-bottom: 30px;
      color: #666;
      font-size: 14px;
    }
    
    .el-form-item {
      margin-bottom: 24px;
    }
    
    .login-button {
      width: 100%;
      height: 44px;
      font-size: 16px;
    }
    
    .tips {
      margin-top: 20px;
      text-align: center;
      
      p {
        color: #999;
        font-size: 12px;
      }
    }
  }
}
</style>
