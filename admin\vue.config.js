const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 开发服务器配置
  devServer: {
    host: '0.0.0.0', // 允许外部访问
    port: 8080,
    open: false,
    allowedHosts: 'all', // 允许所有主机访问
    client: {
      webSocketURL: 'auto://0.0.0.0:0/ws'
    }
  },
  
  // 生产环境配置
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  
  // 构建配置
  configureWebpack: {
    resolve: {
      alias: {
        '@': require('path').resolve(__dirname, 'src')
      }
    }
  }
})
