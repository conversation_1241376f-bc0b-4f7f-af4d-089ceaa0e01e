# 管道巡检系统后端API

基于Node.js + Express + MySQL的RESTful API服务

## 🚀 快速开始

### 1. 环境要求

- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **npm**: 8.0+

### 2. 安装依赖

```bash
# 进入后端目录
cd backend

# 安装依赖
npm install
```

### 3. 数据库配置

#### 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE pipeline_inspection DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 退出MySQL
exit
```

#### 导入数据库结构
```bash
# 导入数据库结构和初始数据
mysql -u root -p pipeline_inspection < database/schema.sql
```

### 4. 环境配置

复制并编辑环境配置文件：

```bash
# 编辑.env文件
# 设置数据库连接信息
DB_HOST=localhost
DB_PORT=3306
DB_NAME=pipeline_inspection
DB_USER=root
DB_PASSWORD=你的MySQL密码

# 设置JWT密钥（生产环境请使用强密钥）
JWT_SECRET=your_jwt_secret_key_change_this_in_production
```

### 5. 初始化数据库

```bash
# 运行数据库迁移脚本
npm run migrate
```

这将创建：
- 默认管理员用户 (admin/admin123)
- 系统配置
- 示例设备数据

### 6. 启动服务

```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

启动成功后访问：
- **API文档**: http://localhost:3001/api
- **健康检查**: http://localhost:3001/health

## 📚 API文档

### 认证接口

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>",
  "display_name": "新用户"
}
```

### 巡检记录接口

#### 获取巡检记录
```http
GET /api/inspections?page=1&limit=20
Authorization: Bearer <token>
```

#### 创建巡检记录
```http
POST /api/inspections
Authorization: Bearer <token>
Content-Type: application/json

{
  "device_id": "PIPE001",
  "inspection_type": "triangle",
  "inspection_status": "normal",
  "location": "厂区北侧",
  "notes": "巡检正常",
  "images": [
    {
      "type": "left",
      "url": "/uploads/image1.jpg",
      "name": "左侧图片",
      "size": 1024000
    }
  ]
}
```

### 设备管理接口

#### 获取设备列表
```http
GET /api/devices?page=1&limit=20
Authorization: Bearer <token>
```

#### 创建设备
```http
POST /api/devices
Authorization: Bearer <token>
Content-Type: application/json

{
  "device_id": "PIPE004",
  "device_name": "新管道",
  "device_type": "输送管道",
  "location": "厂区东侧",
  "description": "新增管道设备"
}
```

### 文件上传接口

#### 上传图片
```http
POST /api/upload/image
Authorization: Bearer <token>
Content-Type: multipart/form-data

image: <file>
compress: true
width: 1920
height: 1080
quality: 85
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 3306 |
| `DB_NAME` | 数据库名称 | pipeline_inspection |
| `DB_USER` | 数据库用户 | root |
| `DB_PASSWORD` | 数据库密码 | - |
| `JWT_SECRET` | JWT密钥 | - |
| `JWT_EXPIRES_IN` | JWT过期时间 | 7d |
| `PORT` | 服务端口 | 3001 |
| `NODE_ENV` | 运行环境 | development |
| `MAX_FILE_SIZE` | 最大文件大小 | 10485760 |

### 系统配置

系统配置存储在数据库中，可通过API动态修改：

```http
GET /api/system/config
PUT /api/system/config/app_name
```

## 🗂️ 项目结构

```
backend/
├── src/
│   ├── app.js              # 应用入口
│   ├── models/             # 数据模型
│   │   ├── index.js        # 模型索引
│   │   ├── User.js         # 用户模型
│   │   ├── Device.js       # 设备模型
│   │   ├── Inspection.js   # 巡检记录模型
│   │   └── ...
│   ├── routes/             # 路由控制器
│   │   ├── auth.js         # 认证路由
│   │   ├── users.js        # 用户管理
│   │   ├── devices.js      # 设备管理
│   │   ├── inspections.js  # 巡检记录
│   │   ├── upload.js       # 文件上传
│   │   ├── statistics.js   # 统计数据
│   │   └── system.js       # 系统管理
│   ├── middleware/         # 中间件
│   │   └── auth.js         # 认证中间件
│   ├── utils/              # 工具函数
│   │   └── logger.js       # 日志工具
│   └── database/           # 数据库相关
│       └── migrate.js      # 迁移脚本
├── database/
│   └── schema.sql          # 数据库结构
├── uploads/                # 上传文件目录
├── package.json
├── .env                    # 环境配置
└── README.md
```

## 🔒 安全特性

### 认证和授权
- JWT令牌认证
- 基于角色的权限控制
- 令牌自动刷新

### 数据安全
- 密码bcrypt加密
- SQL注入防护
- XSS防护
- 请求频率限制

### 文件安全
- 文件类型验证
- 文件大小限制
- 路径遍历防护
- 图片自动压缩

## 📊 监控和日志

### 系统监控
```http
GET /api/system/health    # 健康检查
GET /api/system/info      # 系统信息
```

### 操作日志
```http
GET /api/system/logs      # 系统日志
```

### 统计数据
```http
GET /api/statistics/dashboard  # 仪表板统计
GET /api/statistics/trend      # 趋势数据
```

## 🚀 部署

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
# 设置环境变量
export NODE_ENV=production

# 启动服务
npm start

# 或使用PM2
pm2 start src/app.js --name pipeline-api
```

### Docker部署
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证.env中的数据库配置
   - 确认数据库已创建

2. **JWT令牌错误**
   - 检查JWT_SECRET是否设置
   - 验证令牌格式和有效期

3. **文件上传失败**
   - 检查uploads目录权限
   - 验证文件大小和类型限制

4. **端口冲突**
   - 修改.env中的PORT配置
   - 检查端口是否被占用

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 📝 开发指南

### 添加新的API接口

1. 在`src/routes/`目录创建路由文件
2. 在`src/models/`目录创建数据模型
3. 在`src/app.js`中注册路由
4. 添加相应的测试用例

### 数据库迁移

```bash
# 创建新的迁移
npm run migrate

# 回滚迁移
npm run rollback
```

### 测试

```bash
# 运行测试
npm test

# 运行覆盖率测试
npm run test:coverage
```

## 📞 支持

如有问题，请联系开发团队或查看项目文档。

---

**管道巡检系统后端API v1.0.0**
