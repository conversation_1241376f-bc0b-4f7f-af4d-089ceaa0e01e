-- 宝塔面板数据库配置脚本
-- 在宝塔面板的phpMyAdmin中执行此脚本

-- 1. 创建数据库用户（如果通过宝塔面板创建则跳过）
-- CREATE USER 'pipeline_user'@'localhost' IDENTIFIED BY 'your-secure-password';
-- GRANT ALL PRIVILEGES ON pipeline_inspection.* TO 'pipeline_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 2. 使用数据库
USE pipeline_inspection;

-- 3. 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `display_name` varchar(100) DEFAULT NULL,
  `real_name` varchar(100) DEFAULT NULL,
  `employee_id` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `department` varchar(100) DEFAULT NULL,
  `role` enum('admin','manager','supervisor','inspector') DEFAULT 'inspector',
  `avatar` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `last_login_at` datetime DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 创建设备表
CREATE TABLE IF NOT EXISTS `devices` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL UNIQUE,
  `device_name` varchar(100) NOT NULL,
  `device_type` varchar(50) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `installation_date` date DEFAULT NULL,
  `last_maintenance` date DEFAULT NULL,
  `status` enum('normal','warning','error','maintenance') DEFAULT 'normal',
  `specifications` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. 创建巡检记录表
CREATE TABLE IF NOT EXISTS `inspections` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device_id` varchar(50) NOT NULL,
  `user_id` int NOT NULL,
  `inspection_type` enum('triangle','upward','routine','emergency') NOT NULL,
  `inspection_status` enum('normal','warning','abnormal') NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `notes` text,
  `abnormal_info` text,
  `inspection_time` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_inspection_time` (`inspection_time`),
  KEY `idx_status` (`inspection_status`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. 创建巡检图片表
CREATE TABLE IF NOT EXISTS `inspection_images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `inspection_id` int NOT NULL,
  `image_type` enum('left','center','right','upward','single') NOT NULL,
  `image_url` varchar(500) NOT NULL,
  `image_name` varchar(255) DEFAULT NULL,
  `file_size` int DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_inspection_id` (`inspection_id`),
  KEY `idx_image_type` (`image_type`),
  FOREIGN KEY (`inspection_id`) REFERENCES `inspections` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. 创建系统日志表
CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `resource` varchar(100) DEFAULT NULL,
  `resource_id` varchar(50) DEFAULT NULL,
  `details` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 8. 插入默认管理员用户（密码：admin123）
INSERT IGNORE INTO `users` (`username`, `password`, `display_name`, `real_name`, `role`, `status`) VALUES
('admin', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '管理员', 'admin', 'active');

-- 9. 插入示例设备数据
INSERT IGNORE INTO `devices` (`device_id`, `device_name`, `device_type`, `location`, `status`) VALUES
('PIPE001', '主管道-1号', '输送管道', '厂区A区', 'normal'),
('PIPE002', '主管道-2号', '输送管道', '厂区B区', 'normal'),
('PIPE003', '分支管道-1号', '分支管道', '厂区C区', 'normal'),
('VALVE001', '主控阀门-1号', '控制阀门', '厂区A区', 'normal'),
('VALVE002', '主控阀门-2号', '控制阀门', '厂区B区', 'normal');

-- 10. 创建性能优化索引
CREATE INDEX IF NOT EXISTS `idx_inspections_device_time` ON `inspections` (`device_id`, `inspection_time`);
CREATE INDEX IF NOT EXISTS `idx_inspections_user_time` ON `inspections` (`user_id`, `inspection_time`);
CREATE INDEX IF NOT EXISTS `idx_logs_user_time` ON `system_logs` (`user_id`, `created_at`);

-- 11. 设置数据库字符集（确保支持中文）
ALTER DATABASE pipeline_inspection CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
