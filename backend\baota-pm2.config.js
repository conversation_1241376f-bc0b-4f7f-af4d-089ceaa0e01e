// 宝塔面板PM2配置文件
module.exports = {
  apps: [{
    name: 'pipeline-inspection-api',
    script: './src/app.js',
    cwd: '/www/wwwroot/pipeline-backend/backend',
    instances: 1, // 宝塔面板建议单实例
    exec_mode: 'fork',
    
    // 环境变量
    env: {
      NODE_ENV: 'production',
      PORT: 3001,
      DB_HOST: 'localhost',
      DB_PORT: 3306,
      DB_NAME: 'pipeline_inspection',
      DB_USER: 'pipeline_user',
      DB_PASS: 'your-database-password', // 请修改为实际密码
      JWT_SECRET: 'your-super-secret-jwt-key-change-in-production',
      UPLOAD_PATH: '/www/wwwroot/pipeline-backend/backend/uploads'
    },
    
    // 日志配置
    error_file: '/www/wwwroot/pipeline-backend/backend/logs/err.log',
    out_file: '/www/wwwroot/pipeline-backend/backend/logs/out.log',
    log_file: '/www/wwwroot/pipeline-backend/backend/logs/combined.log',
    time: true,
    
    // 自动重启配置
    max_memory_restart: '500M',
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // 监听文件变化（生产环境建议关闭）
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    
    // 其他配置
    node_args: '--max_old_space_size=512',
    merge_logs: true,
    autorestart: true
  }]
};
