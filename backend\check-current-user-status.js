const axios = require('axios');

async function checkCurrentUserStatus() {
  console.log('🔍 检查当前用户状态...');
  
  try {
    // 1. 测试登录并查看返回的用户信息
    console.log('\n1. 测试登录API...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('✅ 登录成功');
    console.log('登录API返回的用户信息:');
    console.log(JSON.stringify(loginResponse.data.user, null, 2));
    
    if (!loginResponse.data.user.id) {
      console.error('❌ 登录API没有返回用户ID！');
      return;
    }
    
    const token = loginResponse.data.token;
    const userId = loginResponse.data.user.id;
    
    // 2. 测试获取当前用户信息
    console.log('\n2. 测试获取当前用户信息...');
    const currentUserResponse = await axios.get('http://localhost:3001/api/auth/me', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ 获取当前用户信息成功');
    console.log('当前用户详细信息:');
    console.log(JSON.stringify(currentUserResponse.data.user, null, 2));
    
    // 3. 测试用户信息更新（使用最简单的数据）
    console.log('\n3. 测试用户信息更新...');
    const simpleUpdateData = {
      display_name: '简单测试'
    };
    
    console.log('发送简单更新数据:', simpleUpdateData);
    
    const updateResponse = await axios.put(`http://localhost:3001/api/users/${userId}`, simpleUpdateData, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ 用户信息更新成功');
    console.log('更新响应:', JSON.stringify(updateResponse.data, null, 2));
    
    // 4. 验证更新
    console.log('\n4. 验证更新结果...');
    const verifyResponse = await axios.get('http://localhost:3001/api/auth/me', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('验证结果 - 显示名称:', verifyResponse.data.user.display_name);
    
    console.log('\n🎉 所有测试通过！');
    console.log('\n📋 前端需要做的事情:');
    console.log('1. 确保用户重新登录（清除旧的用户信息）');
    console.log('2. 确保本地存储包含用户ID');
    console.log('3. 确保API调用时传递正确的用户ID');
    
    console.log('\n🔧 调试步骤:');
    console.log('1. 在前端清除本地存储: localStorage.clear()');
    console.log('2. 重新登录');
    console.log('3. 检查本地存储的用户信息是否包含ID');
    console.log('4. 尝试编辑个人信息');
    
  } catch (error) {
    console.error('\n❌ 测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
      console.error('请求URL:', error.config?.url);
    } else {
      console.error('错误:', error.message);
    }
  }
}

checkCurrentUserStatus();
