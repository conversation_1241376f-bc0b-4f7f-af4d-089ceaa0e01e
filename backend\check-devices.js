const axios = require('axios');

async function checkDevices() {
  try {
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin', 
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const devicesResponse = await axios.get('http://localhost:3001/api/devices', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('现有设备:');
    devicesResponse.data.data.forEach(device => {
      console.log(`- ${device.device_id}: ${device.device_name}`);
    });
    
  } catch (error) {
    console.error('错误:', error.message);
  }
}

checkDevices();
