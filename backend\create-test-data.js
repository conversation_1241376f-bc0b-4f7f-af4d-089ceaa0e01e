const axios = require('axios');

async function createTestData() {
  console.log('🧪 创建测试数据...');
  
  try {
    // 1. 登录获取token
    console.log('\n1. 登录系统...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 2. 创建测试巡检记录
    console.log('\n2. 创建测试巡检记录...');
    
    const testInspections = [
      {
        device_id: 'PIPE001',
        inspection_type: 'triangle',
        inspection_status: 'normal',
        location: '厂区北侧',
        notes: '设备运行正常，无异常发现',
        inspection_time: new Date().toISOString()
      },
      {
        device_id: 'PIPE002',
        inspection_type: 'upward',
        inspection_status: 'warning',
        location: '厂区中央',
        notes: '发现轻微渗漏，需要关注',
        abnormal_info: '管道接口处有轻微渗漏现象',
        inspection_time: new Date(Date.now() - 3600000).toISOString() // 1小时前
      },
      {
        device_id: 'VALVE001',
        inspection_type: 'normal',
        inspection_status: 'abnormal',
        location: '控制室',
        notes: '阀门操作异常',
        abnormal_info: '阀门无法完全关闭，需要维修',
        inspection_time: new Date(Date.now() - 7200000).toISOString() // 2小时前
      }
    ];
    
    for (let i = 0; i < testInspections.length; i++) {
      const inspection = testInspections[i];
      try {
        const response = await axios.post('http://localhost:3001/api/inspections', inspection, {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log(`✅ 创建巡检记录 ${i + 1}: ${inspection.device_id} - ${inspection.inspection_status}`);
      } catch (error) {
        console.log(`❌ 创建巡检记录 ${i + 1} 失败:`, error.response?.data?.error || error.message);
      }
    }
    
    // 3. 验证创建结果
    console.log('\n3. 验证创建结果...');
    const inspectionsResponse = await axios.get('http://localhost:3001/api/inspections', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log(`✅ 当前巡检记录总数: ${inspectionsResponse.data.pagination.total}`);
    
    if (inspectionsResponse.data.data.length > 0) {
      console.log('\n📋 最新巡检记录:');
      inspectionsResponse.data.data.forEach((record, index) => {
        console.log(`${index + 1}. ${record.device_id} - ${record.inspection_status} - ${record.inspection_time}`);
      });
    }
    
    console.log('\n🎉 测试数据创建完成！');
    console.log('\n📝 现在您可以：');
    console.log('1. 刷新后台管理系统的巡检记录页面');
    console.log('2. 查看新创建的测试数据');
    console.log('3. 测试搜索和筛选功能');
    
  } catch (error) {
    console.error('\n❌ 创建测试数据失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

createTestData();
