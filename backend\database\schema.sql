-- 管道巡检系统数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS pipeline_inspection DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE pipeline_inspection;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    display_name VARCHAR(100) COMMENT '显示名称',
    real_name VARCHAR(100) COMMENT '真实姓名',
    employee_id VARCHAR(50) COMMENT '工号',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    department VARCHAR(100) COMMENT '部门',
    role ENUM('admin', 'manager', 'supervisor', 'inspector') DEFAULT 'inspector' COMMENT '角色',
    avatar VARCHAR(255) COMMENT '头像URL',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '用户表';

-- 设备表
CREATE TABLE devices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(100) UNIQUE NOT NULL COMMENT '设备ID',
    device_name VARCHAR(200) COMMENT '设备名称',
    device_type VARCHAR(100) COMMENT '设备类型',
    location VARCHAR(255) COMMENT '设备位置',
    description TEXT COMMENT '设备描述',
    status ENUM('active', 'maintenance', 'inactive') DEFAULT 'active' COMMENT '设备状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '设备表';

-- 巡检记录表
CREATE TABLE inspections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '巡检员ID',
    device_id VARCHAR(100) NOT NULL COMMENT '设备ID',
    inspection_type ENUM('triangle', 'upward', 'normal') NOT NULL COMMENT '巡检类型',
    inspection_status ENUM('normal', 'abnormal', 'warning') NOT NULL COMMENT '巡检状态',
    location VARCHAR(255) COMMENT '巡检位置',
    notes TEXT COMMENT '巡检备注',
    abnormal_info TEXT COMMENT '异常描述',
    inspection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '巡检时间',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_device_id (device_id),
    INDEX idx_inspection_time (inspection_time),
    INDEX idx_inspection_status (inspection_status)
) COMMENT '巡检记录表';

-- 巡检图片表
CREATE TABLE inspection_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    inspection_id INT NOT NULL COMMENT '巡检记录ID',
    image_type ENUM('left', 'center', 'right', 'upward', 'single') NOT NULL COMMENT '图片类型',
    image_url VARCHAR(500) NOT NULL COMMENT '图片URL',
    image_name VARCHAR(255) COMMENT '图片名称',
    file_size INT COMMENT '文件大小(字节)',
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inspection_id) REFERENCES inspections(id) ON DELETE CASCADE,
    INDEX idx_inspection_id (inspection_id),
    INDEX idx_image_type (image_type)
) COMMENT '巡检图片表';

-- 系统日志表
CREATE TABLE system_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '操作用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id VARCHAR(100) COMMENT '目标ID',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) COMMENT '系统日志表';

-- 统计数据表
CREATE TABLE statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '用户ID(NULL表示全局统计)',
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_inspections INT DEFAULT 0 COMMENT '总巡检次数',
    normal_inspections INT DEFAULT 0 COMMENT '正常巡检次数',
    abnormal_inspections INT DEFAULT 0 COMMENT '异常巡检次数',
    warning_inspections INT DEFAULT 0 COMMENT '警告巡检次数',
    triangle_inspections INT DEFAULT 0 COMMENT '三角拍次数',
    upward_inspections INT DEFAULT 0 COMMENT '仰拍次数',
    normal_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '正常率',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, stat_date),
    INDEX idx_stat_date (stat_date),
    INDEX idx_user_id (user_id)
) COMMENT '统计数据表';

-- 系统配置表
CREATE TABLE system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(255) COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开(前端可访问)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '系统配置表';

-- 插入默认管理员用户
INSERT INTO users (username, password, display_name, real_name, role, status) VALUES 
('admin', '$2b$10$rOzJqQjQjQjQjQjQjQjQjOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '系统管理员', '管理员', 'admin', 'active');
-- 密码: admin123 (需要在后端用bcrypt加密)

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, description, is_public) VALUES 
('app_name', '管道巡检系统', 'string', '应用名称', true),
('app_version', '1.0.0', 'string', '应用版本', true),
('max_upload_size', '10485760', 'number', '最大上传文件大小(字节)', false),
('allowed_image_types', '["jpg", "jpeg", "png", "gif"]', 'json', '允许的图片类型', false),
('inspection_reminder_days', '7', 'number', '巡检提醒天数', false),
('auto_backup_enabled', 'true', 'boolean', '自动备份启用', false);

-- 插入示例设备数据
INSERT INTO devices (device_id, device_name, device_type, location, description) VALUES 
('PIPE001', '主管道A段', '输送管道', '厂区北侧', '主要输送管道第一段'),
('PIPE002', '主管道B段', '输送管道', '厂区中央', '主要输送管道第二段'),
('PIPE003', '主管道C段', '输送管道', '厂区南侧', '主要输送管道第三段'),
('VALVE001', '主控阀门1号', '控制阀门', '控制室', '主要控制阀门'),
('PUMP001', '增压泵1号', '增压设备', '泵房A', '主要增压设备'),
('SENSOR001', '压力传感器1号', '监测设备', '管道节点A', '压力监测传感器');

-- 创建视图：用户巡检统计
CREATE VIEW user_inspection_stats AS
SELECT 
    u.id as user_id,
    u.username,
    u.display_name,
    u.department,
    COUNT(i.id) as total_inspections,
    COUNT(CASE WHEN i.inspection_status = 'normal' THEN 1 END) as normal_count,
    COUNT(CASE WHEN i.inspection_status = 'abnormal' THEN 1 END) as abnormal_count,
    COUNT(CASE WHEN i.inspection_status = 'warning' THEN 1 END) as warning_count,
    ROUND(COUNT(CASE WHEN i.inspection_status = 'normal' THEN 1 END) * 100.0 / COUNT(i.id), 2) as normal_rate,
    MAX(i.inspection_time) as last_inspection_time
FROM users u
LEFT JOIN inspections i ON u.id = i.user_id
WHERE u.status = 'active'
GROUP BY u.id, u.username, u.display_name, u.department;

-- 创建视图：设备巡检统计
CREATE VIEW device_inspection_stats AS
SELECT 
    d.device_id,
    d.device_name,
    d.device_type,
    d.location,
    COUNT(i.id) as total_inspections,
    COUNT(CASE WHEN i.inspection_status = 'normal' THEN 1 END) as normal_count,
    COUNT(CASE WHEN i.inspection_status = 'abnormal' THEN 1 END) as abnormal_count,
    COUNT(CASE WHEN i.inspection_status = 'warning' THEN 1 END) as warning_count,
    MAX(i.inspection_time) as last_inspection_time,
    DATEDIFF(NOW(), MAX(i.inspection_time)) as days_since_last_inspection
FROM devices d
LEFT JOIN inspections i ON d.device_id = i.device_id
WHERE d.status = 'active'
GROUP BY d.device_id, d.device_name, d.device_type, d.location;

-- 创建索引优化查询性能
CREATE INDEX idx_inspections_time_status ON inspections(inspection_time, inspection_status);
CREATE INDEX idx_inspections_user_time ON inspections(user_id, inspection_time);
CREATE INDEX idx_images_inspection_type ON inspection_images(inspection_id, image_type);
