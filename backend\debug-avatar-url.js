const axios = require('axios');

async function debugAvatarUrl() {
  console.log('🔍 调试头像URL问题...');
  
  try {
    // 1. 登录获取用户信息
    console.log('\n1. 登录获取用户信息...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const userId = loginResponse.data.user.id;
    
    console.log('登录成功，用户ID:', userId);
    console.log('登录返回的头像URL:', loginResponse.data.user.avatar);
    
    // 2. 获取当前用户详细信息
    console.log('\n2. 获取当前用户详细信息...');
    const currentUserResponse = await axios.get('http://localhost:3001/api/auth/me', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const currentUser = currentUserResponse.data.user;
    console.log('当前用户头像URL:', currentUser.avatar);
    
    // 3. 测试头像URL是否可访问
    if (currentUser.avatar) {
      console.log('\n3. 测试头像URL访问...');
      
      // 测试相对路径
      const relativeUrl = `http://localhost:3001${currentUser.avatar}`;
      console.log('测试相对路径URL:', relativeUrl);
      
      try {
        const imageResponse = await axios.get(relativeUrl);
        console.log('✅ 相对路径可访问');
        console.log('- 状态码:', imageResponse.status);
        console.log('- 内容类型:', imageResponse.headers['content-type']);
        console.log('- 文件大小:', imageResponse.headers['content-length'], 'bytes');
      } catch (error) {
        console.log('❌ 相对路径无法访问:', error.message);
      }
      
      // 如果头像URL已经是完整URL，也测试一下
      if (currentUser.avatar.startsWith('http')) {
        console.log('测试完整URL:', currentUser.avatar);
        try {
          const imageResponse = await axios.get(currentUser.avatar);
          console.log('✅ 完整URL可访问');
        } catch (error) {
          console.log('❌ 完整URL无法访问:', error.message);
        }
      }
    } else {
      console.log('\n3. 用户没有设置头像');
    }
    
    // 4. 检查数据库中的头像字段
    console.log('\n4. 数据库中的头像字段格式...');
    console.log('数据库avatar字段值:', currentUser.avatar);
    console.log('字段类型:', typeof currentUser.avatar);
    console.log('是否为空:', !currentUser.avatar);
    console.log('是否为相对路径:', currentUser.avatar && currentUser.avatar.startsWith('/'));
    console.log('是否为完整URL:', currentUser.avatar && currentUser.avatar.startsWith('http'));
    
    // 5. 模拟前端的URL处理
    console.log('\n5. 模拟前端URL处理...');
    let frontendAvatarUrl;
    
    if (currentUser.avatar) {
      if (currentUser.avatar.startsWith('http')) {
        // 已经是完整URL
        frontendAvatarUrl = currentUser.avatar;
      } else if (currentUser.avatar.startsWith('/')) {
        // 相对路径，添加域名
        frontendAvatarUrl = `http://localhost:3001${currentUser.avatar}`;
      } else {
        // 其他情况，使用默认头像
        frontendAvatarUrl = '/static/logo.png';
      }
    } else {
      frontendAvatarUrl = '/static/logo.png';
    }
    
    console.log('前端应该使用的头像URL:', frontendAvatarUrl);
    
    // 6. 测试前端URL
    if (frontendAvatarUrl.startsWith('http')) {
      console.log('\n6. 测试前端URL...');
      try {
        const testResponse = await axios.get(frontendAvatarUrl);
        console.log('✅ 前端URL可访问');
        console.log('- 状态码:', testResponse.status);
        console.log('- 内容类型:', testResponse.headers['content-type']);
      } catch (error) {
        console.log('❌ 前端URL无法访问:', error.message);
      }
    }
    
    console.log('\n🎉 头像URL调试完成！');
    console.log('\n📋 调试结果总结:');
    console.log('- 数据库头像字段:', currentUser.avatar);
    console.log('- 前端应使用URL:', frontendAvatarUrl);
    
    console.log('\n🔧 可能的问题:');
    console.log('1. 数据库中存储的是完整URL，但前端又添加了域名');
    console.log('2. 头像文件不存在或路径错误');
    console.log('3. 前端缓存了旧的头像URL');
    
  } catch (error) {
    console.error('\n❌ 调试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

debugAvatarUrl();
