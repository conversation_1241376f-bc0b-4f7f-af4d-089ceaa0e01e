const axios = require('axios');

async function debugInspectionRequest() {
  console.log('🔍 调试前端巡检记录请求...');
  
  try {
    // 1. 登录获取token
    console.log('\n1. 登录系统...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 2. 模拟前端发送的数据（可能有问题的数据）
    console.log('\n2. 测试可能有问题的数据格式...');
    
    const problematicData = {
      device_id: 'DEVICE001',
      inspection_type: 'triangle',
      inspection_status: '正常', // 这可能是问题！应该是 'normal'
      location: '',
      notes: '',
      abnormal_info: '',
      inspection_time: new Date().toISOString(),
      images: []
    };
    
    console.log('发送的数据:', JSON.stringify(problematicData, null, 2));
    
    try {
      const response = await axios.post('http://localhost:3001/api/inspections', problematicData, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('❌ 意外成功:', response.data);
    } catch (error) {
      console.log('✅ 预期的错误:', error.response?.status, error.response?.data);
    }
    
    // 3. 测试正确的数据格式
    console.log('\n3. 测试正确的数据格式...');
    
    const correctData = {
      device_id: 'DEVICE001',
      inspection_type: 'triangle',
      inspection_status: 'normal', // 正确的值
      location: '',
      notes: '',
      abnormal_info: '',
      inspection_time: new Date().toISOString(),
      images: []
    };
    
    console.log('发送的数据:', JSON.stringify(correctData, null, 2));
    
    try {
      const response = await axios.post('http://localhost:3001/api/inspections', correctData, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ 成功创建:', response.data.inspection.id);
    } catch (error) {
      console.log('❌ 意外错误:', error.response?.status, error.response?.data);
    }
    
    // 4. 测试其他可能的问题
    console.log('\n4. 测试其他可能的问题...');
    
    const testCases = [
      {
        name: '缺少必需字段',
        data: {
          inspection_type: 'triangle',
          inspection_status: 'normal'
        }
      },
      {
        name: '无效的inspection_type',
        data: {
          device_id: 'DEVICE001',
          inspection_type: 'invalid',
          inspection_status: 'normal'
        }
      },
      {
        name: '无效的inspection_status',
        data: {
          device_id: 'DEVICE001',
          inspection_type: 'triangle',
          inspection_status: 'invalid'
        }
      },
      {
        name: '无效的时间格式',
        data: {
          device_id: 'DEVICE001',
          inspection_type: 'triangle',
          inspection_status: 'normal',
          inspection_time: 'invalid-date'
        }
      }
    ];
    
    for (const testCase of testCases) {
      console.log(`\n测试: ${testCase.name}`);
      try {
        const response = await axios.post('http://localhost:3001/api/inspections', testCase.data, {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('❌ 意外成功:', response.status);
      } catch (error) {
        console.log('✅ 预期错误:', error.response?.status, error.response?.data?.error);
        if (error.response?.data?.details) {
          console.log('详细错误:', error.response.data.details);
        }
      }
    }
    
    console.log('\n🎉 调试完成！');
    
  } catch (error) {
    console.error('\n❌ 调试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

debugInspectionRequest();
