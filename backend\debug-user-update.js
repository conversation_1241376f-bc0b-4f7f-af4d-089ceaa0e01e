const axios = require('axios');

async function debugUserUpdate() {
  console.log('🔍 调试用户信息更新...');
  
  try {
    // 1. 登录获取token和用户信息
    console.log('\n1. 登录获取用户信息...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    
    console.log('✅ 登录成功');
    console.log('用户信息:', {
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      email: user.email
    });
    
    // 2. 获取当前用户详细信息
    console.log('\n2. 获取当前用户详细信息...');
    const currentUserResponse = await axios.get('http://localhost:3001/api/auth/me', {
      headers: { 'Authorization': `Bear<PERSON> ${token}` }
    });
    
    const currentUser = currentUserResponse.data.user;
    console.log('当前用户详细信息:', {
      id: currentUser.id,
      username: currentUser.username,
      display_name: currentUser.display_name,
      real_name: currentUser.real_name,
      email: currentUser.email,
      phone: currentUser.phone,
      department: currentUser.department,
      avatar: currentUser.avatar
    });
    
    // 3. 模拟前端发送的更新数据
    console.log('\n3. 模拟前端更新请求...');
    const updateData = {
      display_name: '测试显示名称',
      real_name: '测试真实姓名',
      employee_id: 'EMP001',
      email: '<EMAIL>',
      phone: '13800138000',
      department: '技术部',
      avatar: currentUser.avatar // 使用当前头像
    };
    
    console.log('发送的更新数据:', updateData);
    console.log('请求URL:', `http://localhost:3001/api/users/${currentUser.id}`);
    console.log('Authorization:', `Bearer ${token.substring(0, 20)}...`);
    
    // 4. 发送更新请求
    const updateResponse = await axios.put(`http://localhost:3001/api/users/${currentUser.id}`, updateData, {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 更新成功!');
    console.log('更新后的用户信息:', {
      id: updateResponse.data.user.id,
      display_name: updateResponse.data.user.display_name,
      real_name: updateResponse.data.user.real_name,
      email: updateResponse.data.user.email,
      phone: updateResponse.data.user.phone,
      department: updateResponse.data.user.department
    });
    
    // 5. 验证更新是否生效
    console.log('\n5. 验证更新是否生效...');
    const verifyResponse = await axios.get('http://localhost:3001/api/auth/me', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const verifiedUser = verifyResponse.data.user;
    console.log('验证结果:', {
      display_name: verifiedUser.display_name,
      real_name: verifiedUser.real_name,
      email: verifiedUser.email,
      phone: verifiedUser.phone,
      department: verifiedUser.department
    });
    
    console.log('\n🎉 用户信息更新测试成功！');
    
  } catch (error) {
    console.error('\n❌ 用户信息更新失败:');
    
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
      console.error('请求URL:', error.config?.url);
      console.error('请求方法:', error.config?.method);
      console.error('请求头:', error.config?.headers);
      console.error('请求数据:', error.config?.data);
    } else {
      console.error('网络错误:', error.message);
    }
    
    // 如果是验证错误，显示详细信息
    if (error.response?.data?.errors) {
      console.error('\n验证错误详情:');
      error.response.data.errors.forEach((err, index) => {
        console.error(`${index + 1}. ${err.message} (字段: ${err.path})`);
      });
    }
  }
}

debugUserUpdate();
