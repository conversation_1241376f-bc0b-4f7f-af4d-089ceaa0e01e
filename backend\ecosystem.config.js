// PM2 部署配置
module.exports = {
  apps: [{
    name: 'pipeline-inspection-api',
    script: './src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3001,
      DB_HOST: 'localhost',
      DB_PORT: 3306,
      DB_NAME: 'pipeline_inspection',
      DB_USER: 'root',
      DB_PASS: '',
      JWT_SECRET: 'your-super-secret-jwt-key-change-in-production',
      UPLOAD_PATH: './uploads'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001,
      DB_HOST: 'localhost', // 生产环境数据库地址
      DB_PORT: 3306,
      DB_NAME: 'pipeline_inspection',
      DB_USER: 'pipeline_user', // 生产环境数据库用户
      DB_PASS: 'your-secure-password', // 生产环境数据库密码
      JWT_SECRET: 'your-production-jwt-secret-key-very-long-and-secure',
      UPLOAD_PATH: '/var/www/pipeline-inspection/uploads'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }],

  deploy: {
    production: {
      user: 'root', // 服务器用户名
      host: 'your-server-ip', // 服务器IP地址
      ref: 'origin/main',
      repo: 'https://github.com/your-username/pipeline-inspection.git', // Git仓库地址
      path: '/var/www/pipeline-inspection',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
