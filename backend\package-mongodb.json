{"name": "pipeline-inspection-backend", "version": "1.0.0", "description": "石化管巡2.0 后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["pipeline", "inspection", "backend", "api"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}