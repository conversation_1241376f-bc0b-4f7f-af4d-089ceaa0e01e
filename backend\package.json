{"name": "pipeline-inspection-backend", "version": "1.0.0", "description": "管道巡检系统后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "test": "jest"}, "keywords": ["pipeline", "inspection", "backend", "api", "mysql"], "author": "Pipeline Inspection Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "joi": "^17.9.2", "moment": "^2.29.4", "node-cron": "^3.0.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}