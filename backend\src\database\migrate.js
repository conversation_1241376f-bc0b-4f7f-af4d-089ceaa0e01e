const { sequelize, User, SystemConfig } = require('../models');
const bcrypt = require('bcryptjs');

/**
 * 数据库迁移脚本
 * 用于初始化数据库结构和基础数据
 */

async function migrate() {
  try {
    console.log('🔄 开始数据库迁移...');

    // 1. 测试数据库连接
    console.log('📡 测试数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 2. 同步数据库模型
    console.log('🔧 同步数据库模型...');
    await sequelize.sync({ force: false, alter: true });
    console.log('✅ 数据库模型同步完成');

    // 3. 创建默认管理员用户
    console.log('👤 创建默认管理员用户...');
    await createDefaultAdmin();

    // 4. 初始化系统配置
    console.log('⚙️ 初始化系统配置...');
    await initializeSystemConfig();

    // 5. 创建示例设备数据
    console.log('🔧 创建示例设备数据...');
    await createSampleDevices();

    console.log('🎉 数据库迁移完成！');

  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  }
}

/**
 * 创建默认管理员用户
 */
async function createDefaultAdmin() {
  try {
    // 检查是否已存在管理员用户
    const existingAdmin = await User.findOne({
      where: { role: 'admin' }
    });

    if (existingAdmin) {
      console.log('ℹ️ 管理员用户已存在，跳过创建');
      return;
    }

    // 创建默认管理员
    const adminUser = await User.create({
      username: 'admin',
      password: 'admin123', // 将被自动加密
      display_name: '系统管理员',
      real_name: '管理员',
      role: 'admin',
      status: 'active',
      email: '<EMAIL>'
    });

    console.log('✅ 默认管理员用户创建成功');
    console.log(`   用户名: ${adminUser.username}`);
    console.log(`   密码: admin123`);
    console.log('⚠️ 请在生产环境中立即修改默认密码！');

  } catch (error) {
    console.error('❌ 创建默认管理员失败:', error);
    throw error;
  }
}

/**
 * 初始化系统配置
 */
async function initializeSystemConfig() {
  try {
    // 检查是否已有配置
    const configCount = await SystemConfig.count();
    if (configCount > 0) {
      console.log('ℹ️ 系统配置已存在，跳过初始化');
      return;
    }

    // 初始化默认配置
    await SystemConfig.initializeDefaults();
    console.log('✅ 系统配置初始化完成');

  } catch (error) {
    console.error('❌ 初始化系统配置失败:', error);
    throw error;
  }
}

/**
 * 创建示例设备数据
 */
async function createSampleDevices() {
  try {
    const { Device } = require('../models');
    
    // 检查是否已有设备数据
    const deviceCount = await Device.count();
    if (deviceCount > 0) {
      console.log('ℹ️ 设备数据已存在，跳过创建');
      return;
    }

    // 创建示例设备
    const sampleDevices = [
      {
        device_id: 'PIPE001',
        device_name: '主管道A段',
        device_type: '输送管道',
        location: '厂区北侧',
        description: '主要输送管道第一段',
        status: 'active'
      },
      {
        device_id: 'PIPE002',
        device_name: '主管道B段',
        device_type: '输送管道',
        location: '厂区中央',
        description: '主要输送管道第二段',
        status: 'active'
      },
      {
        device_id: 'PIPE003',
        device_name: '主管道C段',
        device_type: '输送管道',
        location: '厂区南侧',
        description: '主要输送管道第三段',
        status: 'active'
      },
      {
        device_id: 'VALVE001',
        device_name: '主控阀门1号',
        device_type: '控制阀门',
        location: '控制室',
        description: '主要控制阀门',
        status: 'active'
      },
      {
        device_id: 'PUMP001',
        device_name: '增压泵1号',
        device_type: '增压设备',
        location: '泵房A',
        description: '主要增压设备',
        status: 'active'
      },
      {
        device_id: 'SENSOR001',
        device_name: '压力传感器1号',
        device_type: '监测设备',
        location: '管道节点A',
        description: '压力监测传感器',
        status: 'active'
      }
    ];

    await Device.bulkCreate(sampleDevices);
    console.log(`✅ 创建了 ${sampleDevices.length} 个示例设备`);

  } catch (error) {
    console.error('❌ 创建示例设备失败:', error);
    throw error;
  }
}

/**
 * 创建示例用户数据
 */
async function createSampleUsers() {
  try {
    // 检查是否已有普通用户
    const userCount = await User.count({
      where: { role: { [sequelize.Sequelize.Op.ne]: 'admin' } }
    });

    if (userCount > 0) {
      console.log('ℹ️ 示例用户已存在，跳过创建');
      return;
    }

    // 创建示例用户
    const sampleUsers = [
      {
        username: 'inspector1',
        password: 'password123',
        display_name: '张三',
        real_name: '张三',
        employee_id: 'EMP001',
        role: 'inspector',
        department: '管道维护部',
        email: '<EMAIL>',
        phone: '13800138001',
        status: 'active'
      },
      {
        username: 'inspector2',
        password: 'password123',
        display_name: '李四',
        real_name: '李四',
        employee_id: 'EMP002',
        role: 'inspector',
        department: '管道维护部',
        email: '<EMAIL>',
        phone: '13800138002',
        status: 'active'
      },
      {
        username: 'supervisor1',
        password: 'password123',
        display_name: '王五',
        real_name: '王五',
        employee_id: 'SUP001',
        role: 'supervisor',
        department: '管道维护部',
        email: '<EMAIL>',
        phone: '13800138003',
        status: 'active'
      },
      {
        username: 'manager1',
        password: 'password123',
        display_name: '赵六',
        real_name: '赵六',
        employee_id: 'MGR001',
        role: 'manager',
        department: '技术部',
        email: '<EMAIL>',
        phone: '13800138004',
        status: 'active'
      }
    ];

    await User.bulkCreate(sampleUsers);
    console.log(`✅ 创建了 ${sampleUsers.length} 个示例用户`);

  } catch (error) {
    console.error('❌ 创建示例用户失败:', error);
    throw error;
  }
}

/**
 * 验证迁移结果
 */
async function validateMigration() {
  try {
    console.log('🔍 验证迁移结果...');

    // 检查用户表
    const userCount = await User.count();
    console.log(`✅ 用户表: ${userCount} 条记录`);

    // 检查设备表
    const { Device } = require('../models');
    const deviceCount = await Device.count();
    console.log(`✅ 设备表: ${deviceCount} 条记录`);

    // 检查配置表
    const configCount = await SystemConfig.count();
    console.log(`✅ 配置表: ${configCount} 条记录`);

    console.log('✅ 迁移验证完成');

  } catch (error) {
    console.error('❌ 迁移验证失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrate()
    .then(() => validateMigration())
    .then(() => {
      console.log('🎉 数据库迁移和验证完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 迁移过程失败:', error);
      process.exit(1);
    });
}

module.exports = {
  migrate,
  createDefaultAdmin,
  initializeSystemConfig,
  createSampleDevices,
  createSampleUsers,
  validateMigration
};
