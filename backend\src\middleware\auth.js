const jwt = require('jsonwebtoken');
const { User } = require('../models');

/**
 * 验证JWT令牌中间件
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: '缺少访问令牌'
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // 查找用户
    const user = await User.findByPk(decoded.id);
    if (!user) {
      return res.status(401).json({
        error: '用户不存在'
      });
    }

    if (user.status !== 'active') {
      return res.status(401).json({
        error: '账户已被禁用'
      });
    }

    // 将用户信息添加到请求对象
    req.user = user;
    req.userId = user.id;
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: '无效的访问令牌'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: '访问令牌已过期'
      });
    }

    console.error('认证中间件错误:', error);
    return res.status(500).json({
      error: '认证失败'
    });
  }
};

/**
 * 可选认证中间件（令牌存在时验证，不存在时继续）
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    const user = await User.findByPk(decoded.id);
    
    if (user && user.status === 'active') {
      req.user = user;
      req.userId = user.id;
    }
    
    next();
  } catch (error) {
    // 可选认证失败时继续执行
    next();
  }
};

/**
 * 角色权限验证中间件
 * @param {string|Array} roles - 允许的角色
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: '需要登录'
      });
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: '权限不足'
      });
    }

    next();
  };
};

/**
 * 管理员权限验证中间件
 */
const requireAdmin = requireRole(['admin']);

/**
 * 管理员或经理权限验证中间件
 */
const requireManager = requireRole(['admin', 'manager']);

/**
 * 主管及以上权限验证中间件
 */
const requireSupervisor = requireRole(['admin', 'manager', 'supervisor']);

/**
 * 用户自己或管理员权限验证中间件
 * @param {string} userIdParam - 请求参数中用户ID的字段名
 */
const requireSelfOrAdmin = (userIdParam = 'id') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: '需要登录'
      });
    }

    const targetUserId = parseInt(req.params[userIdParam]);
    const currentUserId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (targetUserId !== currentUserId && !isAdmin) {
      return res.status(403).json({
        error: '只能访问自己的数据或需要管理员权限'
      });
    }

    next();
  };
};

/**
 * API密钥验证中间件（用于内部服务调用）
 */
const authenticateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  const validApiKey = process.env.API_KEY;

  if (!validApiKey) {
    return next(); // 如果没有配置API密钥，跳过验证
  }

  if (!apiKey || apiKey !== validApiKey) {
    return res.status(401).json({
      error: '无效的API密钥'
    });
  }

  next();
};

/**
 * 限制访问频率中间件
 * @param {number} maxRequests - 最大请求次数
 * @param {number} windowMs - 时间窗口（毫秒）
 */
const createRateLimit = (maxRequests, windowMs) => {
  const requests = new Map();

  return (req, res, next) => {
    const key = req.ip + (req.user ? `:${req.user.id}` : '');
    const now = Date.now();
    
    if (!requests.has(key)) {
      requests.set(key, []);
    }

    const userRequests = requests.get(key);
    
    // 清理过期的请求记录
    const validRequests = userRequests.filter(time => now - time < windowMs);
    requests.set(key, validRequests);

    if (validRequests.length >= maxRequests) {
      return res.status(429).json({
        error: '请求过于频繁，请稍后再试'
      });
    }

    validRequests.push(now);
    next();
  };
};

module.exports = {
  authenticateToken,
  optionalAuth,
  requireRole,
  requireAdmin,
  requireManager,
  requireSupervisor,
  requireSelfOrAdmin,
  authenticateApiKey,
  createRateLimit
};
