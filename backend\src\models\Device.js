const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Device = sequelize.define('Device', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    device_id: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        len: [1, 100]
      }
    },
    device_name: {
      type: DataTypes.STRING(200),
      allowNull: true,
      validate: {
        len: [1, 200]
      }
    },
    device_type: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [1, 100]
      }
    },
    location: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        len: [1, 255]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('active', 'maintenance', 'inactive'),
      defaultValue: 'active',
      allowNull: false
    }
  }, {
    tableName: 'devices',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  // 类方法：获取设备统计信息
  Device.getStatistics = async function() {
    const total = await this.count();
    const active = await this.count({ where: { status: 'active' } });
    const maintenance = await this.count({ where: { status: 'maintenance' } });
    const inactive = await this.count({ where: { status: 'inactive' } });
    
    const typeStats = await this.findAll({
      attributes: [
        'device_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        device_type: {
          [sequelize.Sequelize.Op.not]: null
        }
      },
      group: ['device_type'],
      raw: true
    });

    return {
      total,
      active,
      maintenance,
      inactive,
      types: typeStats.reduce((acc, item) => {
        acc[item.device_type] = parseInt(item.count);
        return acc;
      }, {})
    };
  };

  // 类方法：根据设备ID查找
  Device.findByDeviceId = function(deviceId) {
    return this.findOne({
      where: { device_id: deviceId }
    });
  };

  return Device;
};
