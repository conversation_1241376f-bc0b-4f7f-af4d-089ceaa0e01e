const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Inspection = sequelize.define('Inspection', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    device_id: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    inspection_type: {
      type: DataTypes.ENUM('triangle', 'upward', 'normal'),
      allowNull: false
    },
    inspection_status: {
      type: DataTypes.ENUM('normal', 'abnormal', 'warning'),
      allowNull: false
    },
    location: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        len: [0, 255]  // 允许空字符串
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    abnormal_info: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    inspection_time: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    upload_time: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'inspections',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['device_id']
      },
      {
        fields: ['inspection_time']
      },
      {
        fields: ['inspection_status']
      },
      {
        fields: ['inspection_time', 'inspection_status']
      }
    ]
  });

  // 类方法：获取巡检统计信息
  Inspection.getStatistics = async function(options = {}) {
    const { userId, startDate, endDate } = options;
    
    const whereClause = {};
    if (userId) whereClause.user_id = userId;
    if (startDate && endDate) {
      whereClause.inspection_time = {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    }

    const total = await this.count({ where: whereClause });
    const normal = await this.count({ 
      where: { ...whereClause, inspection_status: 'normal' } 
    });
    const abnormal = await this.count({ 
      where: { ...whereClause, inspection_status: 'abnormal' } 
    });
    const warning = await this.count({ 
      where: { ...whereClause, inspection_status: 'warning' } 
    });

    const normalRate = total > 0 ? ((normal / total) * 100).toFixed(2) : 0;

    const typeStats = await this.findAll({
      attributes: [
        'inspection_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: whereClause,
      group: ['inspection_type'],
      raw: true
    });

    return {
      total,
      normal,
      abnormal,
      warning,
      normalRate: parseFloat(normalRate),
      types: typeStats.reduce((acc, item) => {
        acc[item.inspection_type] = parseInt(item.count);
        return acc;
      }, {})
    };
  };

  // 类方法：获取趋势数据
  Inspection.getTrendData = async function(days = 7) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    const trendData = await this.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('inspection_time')), 'date'],
        'inspection_status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        inspection_time: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        }
      },
      group: [
        sequelize.fn('DATE', sequelize.col('inspection_time')),
        'inspection_status'
      ],
      order: [
        [sequelize.fn('DATE', sequelize.col('inspection_time')), 'ASC']
      ],
      raw: true
    });

    // 格式化数据
    const dates = [];
    const normal = [];
    const abnormal = [];
    const warning = [];

    // 生成日期范围
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];
      dates.push(dateStr);
      
      // 初始化数据
      const dayData = trendData.filter(item => item.date === dateStr);
      normal.push(dayData.find(item => item.inspection_status === 'normal')?.count || 0);
      abnormal.push(dayData.find(item => item.inspection_status === 'abnormal')?.count || 0);
      warning.push(dayData.find(item => item.inspection_status === 'warning')?.count || 0);
    }

    return {
      dates,
      normal,
      abnormal,
      warning
    };
  };

  return Inspection;
};
