const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InspectionImage = sequelize.define('InspectionImage', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    inspection_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'inspections',
        key: 'id'
      }
    },
    image_type: {
      type: DataTypes.ENUM('left', 'center', 'right', 'upward', 'single'),
      allowNull: false
    },
    image_url: {
      type: DataTypes.STRING(500),
      allowNull: false,
      validate: {
        len: [1, 500]
      }
    },
    image_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        len: [1, 255]
      }
    },
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0
      }
    },
    upload_time: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'inspection_images',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        fields: ['inspection_id']
      },
      {
        fields: ['image_type']
      },
      {
        fields: ['inspection_id', 'image_type']
      }
    ]
  });

  // 类方法：获取图片统计信息
  InspectionImage.getStatistics = async function() {
    const total = await this.count();
    const totalSize = await this.sum('file_size') || 0;
    
    const typeStats = await this.findAll({
      attributes: [
        'image_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('file_size')), 'total_size']
      ],
      group: ['image_type'],
      raw: true
    });

    return {
      total,
      totalSize,
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
      types: typeStats.reduce((acc, item) => {
        acc[item.image_type] = {
          count: parseInt(item.count),
          size: parseInt(item.total_size || 0),
          sizeMB: ((item.total_size || 0) / 1024 / 1024).toFixed(2)
        };
        return acc;
      }, {})
    };
  };

  // 类方法：根据巡检记录ID获取图片
  InspectionImage.findByInspectionId = function(inspectionId) {
    return this.findAll({
      where: { inspection_id: inspectionId },
      order: [['image_type', 'ASC']]
    });
  };

  // 类方法：清理孤立的图片记录
  InspectionImage.cleanOrphanedImages = async function() {
    const orphanedImages = await this.findAll({
      include: [{
        model: sequelize.models.Inspection,
        as: 'inspection',
        required: false
      }],
      where: {
        '$inspection.id$': null
      }
    });

    if (orphanedImages.length > 0) {
      const orphanedIds = orphanedImages.map(img => img.id);
      await this.destroy({
        where: {
          id: orphanedIds
        }
      });
    }

    return orphanedImages.length;
  };

  return InspectionImage;
};
