const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Statistics = sequelize.define('Statistics', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    stat_date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    total_inspections: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    normal_inspections: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    abnormal_inspections: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    warning_inspections: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    triangle_inspections: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    upward_inspections: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    normal_rate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0.00,
      validate: {
        min: 0,
        max: 100
      }
    }
  }, {
    tableName: 'statistics',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'stat_date']
      },
      {
        fields: ['stat_date']
      },
      {
        fields: ['user_id']
      }
    ]
  });

  // 类方法：更新或创建统计数据
  Statistics.updateOrCreateStats = async function(userId, date, statsData) {
    const [stats, created] = await this.findOrCreate({
      where: {
        user_id: userId,
        stat_date: date
      },
      defaults: {
        user_id: userId,
        stat_date: date,
        ...statsData
      }
    });

    if (!created) {
      await stats.update(statsData);
    }

    return stats;
  };

  // 类方法：计算并更新统计数据
  Statistics.calculateAndUpdateStats = async function(userId = null, date = null) {
    if (!date) {
      date = new Date().toISOString().split('T')[0];
    }

    const startDate = new Date(date);
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1);

    const whereClause = {
      inspection_time: {
        [sequelize.Sequelize.Op.gte]: startDate,
        [sequelize.Sequelize.Op.lt]: endDate
      }
    };

    if (userId) {
      whereClause.user_id = userId;
    }

    // 获取巡检统计数据
    const inspectionStats = await sequelize.models.Inspection.findAll({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'total'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_status = 'normal' THEN 1 ELSE 0 END")), 'normal'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_status = 'abnormal' THEN 1 ELSE 0 END")), 'abnormal'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_status = 'warning' THEN 1 ELSE 0 END")), 'warning'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_type = 'triangle' THEN 1 ELSE 0 END")), 'triangle'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_type = 'upward' THEN 1 ELSE 0 END")), 'upward']
      ],
      where: whereClause,
      raw: true
    });

    const stats = inspectionStats[0];
    const total = parseInt(stats.total) || 0;
    const normal = parseInt(stats.normal) || 0;
    const abnormal = parseInt(stats.abnormal) || 0;
    const warning = parseInt(stats.warning) || 0;
    const triangle = parseInt(stats.triangle) || 0;
    const upward = parseInt(stats.upward) || 0;
    const normalRate = total > 0 ? ((normal / total) * 100).toFixed(2) : 0;

    const statsData = {
      total_inspections: total,
      normal_inspections: normal,
      abnormal_inspections: abnormal,
      warning_inspections: warning,
      triangle_inspections: triangle,
      upward_inspections: upward,
      normal_rate: parseFloat(normalRate)
    };

    return await this.updateOrCreateStats(userId, date, statsData);
  };

  // 类方法：获取用户统计数据
  Statistics.getUserStats = function(userId, options = {}) {
    const { startDate, endDate, limit = 30 } = options;
    
    const whereClause = { user_id: userId };
    if (startDate && endDate) {
      whereClause.stat_date = {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    }

    return this.findAll({
      where: whereClause,
      order: [['stat_date', 'DESC']],
      limit,
      include: [{
        model: sequelize.models.User,
        as: 'user',
        attributes: ['username', 'display_name', 'department']
      }]
    });
  };

  // 类方法：获取全局统计数据
  Statistics.getGlobalStats = function(options = {}) {
    const { startDate, endDate, limit = 30 } = options;
    
    const whereClause = { user_id: null };
    if (startDate && endDate) {
      whereClause.stat_date = {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    }

    return this.findAll({
      where: whereClause,
      order: [['stat_date', 'DESC']],
      limit
    });
  };

  return Statistics;
};
