const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const SystemConfig = sequelize.define('SystemConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    config_key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        len: [1, 100]
      }
    },
    config_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    config_type: {
      type: DataTypes.ENUM('string', 'number', 'boolean', 'json'),
      defaultValue: 'string',
      allowNull: false
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        len: [1, 255]
      }
    },
    is_public: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    }
  }, {
    tableName: 'system_config',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  // 实例方法：获取解析后的值
  SystemConfig.prototype.getParsedValue = function() {
    if (!this.config_value) return null;

    try {
      switch (this.config_type) {
        case 'number':
          return parseFloat(this.config_value);
        case 'boolean':
          return this.config_value.toLowerCase() === 'true';
        case 'json':
          return JSON.parse(this.config_value);
        case 'string':
        default:
          return this.config_value;
      }
    } catch (error) {
      console.error(`解析配置值失败 ${this.config_key}:`, error);
      return this.config_value;
    }
  };

  // 类方法：获取配置值
  SystemConfig.getValue = async function(key, defaultValue = null) {
    try {
      const config = await this.findOne({
        where: { config_key: key }
      });

      if (!config) return defaultValue;
      return config.getParsedValue();
    } catch (error) {
      console.error(`获取配置失败 ${key}:`, error);
      return defaultValue;
    }
  };

  // 类方法：设置配置值
  SystemConfig.setValue = async function(key, value, type = 'string', description = null, isPublic = false) {
    try {
      let configValue = value;
      
      // 根据类型转换值
      if (type === 'json' && typeof value === 'object') {
        configValue = JSON.stringify(value);
      } else if (type === 'boolean') {
        configValue = value ? 'true' : 'false';
      } else if (type === 'number') {
        configValue = value.toString();
      } else {
        configValue = value.toString();
      }

      const [config, created] = await this.findOrCreate({
        where: { config_key: key },
        defaults: {
          config_key: key,
          config_value: configValue,
          config_type: type,
          description,
          is_public: isPublic
        }
      });

      if (!created) {
        await config.update({
          config_value: configValue,
          config_type: type,
          description: description || config.description,
          is_public: isPublic
        });
      }

      return config;
    } catch (error) {
      console.error(`设置配置失败 ${key}:`, error);
      throw error;
    }
  };

  // 类方法：获取公开配置
  SystemConfig.getPublicConfigs = async function() {
    try {
      const configs = await this.findAll({
        where: { is_public: true },
        attributes: ['config_key', 'config_value', 'config_type']
      });

      const result = {};
      configs.forEach(config => {
        result[config.config_key] = config.getParsedValue();
      });

      return result;
    } catch (error) {
      console.error('获取公开配置失败:', error);
      return {};
    }
  };

  // 类方法：批量设置配置
  SystemConfig.setMultiple = async function(configs) {
    const results = [];
    
    for (const [key, configData] of Object.entries(configs)) {
      try {
        const { value, type = 'string', description = null, isPublic = false } = configData;
        const result = await this.setValue(key, value, type, description, isPublic);
        results.push(result);
      } catch (error) {
        console.error(`批量设置配置失败 ${key}:`, error);
      }
    }

    return results;
  };

  // 类方法：初始化默认配置
  SystemConfig.initializeDefaults = async function() {
    const defaultConfigs = {
      app_name: {
        value: '管道巡检系统',
        type: 'string',
        description: '应用名称',
        isPublic: true
      },
      app_version: {
        value: '1.0.0',
        type: 'string',
        description: '应用版本',
        isPublic: true
      },
      max_upload_size: {
        value: 10485760,
        type: 'number',
        description: '最大上传文件大小(字节)',
        isPublic: false
      },
      allowed_image_types: {
        value: ['jpg', 'jpeg', 'png', 'gif'],
        type: 'json',
        description: '允许的图片类型',
        isPublic: false
      },
      inspection_reminder_days: {
        value: 7,
        type: 'number',
        description: '巡检提醒天数',
        isPublic: false
      },
      auto_backup_enabled: {
        value: true,
        type: 'boolean',
        description: '自动备份启用',
        isPublic: false
      }
    };

    return await this.setMultiple(defaultConfigs);
  };

  return SystemConfig;
};
