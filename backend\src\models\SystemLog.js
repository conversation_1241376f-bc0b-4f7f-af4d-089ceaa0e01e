const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const SystemLog = sequelize.define('SystemLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    action: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    target_type: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [1, 50]
      }
    },
    target_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: [1, 100]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    ip_address: {
      type: DataTypes.STRING(45),
      allowNull: true,
      validate: {
        isIP: true
      }
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'system_logs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: false,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['action']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['user_id', 'created_at']
      }
    ]
  });

  // 类方法：记录日志
  SystemLog.logActivity = async function(userId, action, targetType, targetId, description, ipAddress, userAgent) {
    try {
      return await this.create({
        user_id: userId,
        action,
        target_type: targetType,
        target_id: targetId,
        description,
        ip_address: ipAddress,
        user_agent: userAgent
      });
    } catch (error) {
      console.error('记录系统日志失败:', error);
      return null;
    }
  };

  // 类方法：获取用户活动日志
  SystemLog.getUserActivity = function(userId, options = {}) {
    const { limit = 50, offset = 0, startDate, endDate } = options;
    
    const whereClause = { user_id: userId };
    if (startDate && endDate) {
      whereClause.created_at = {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    }

    return this.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit,
      offset,
      include: [{
        model: sequelize.models.User,
        as: 'user',
        attributes: ['username', 'display_name']
      }]
    });
  };

  // 类方法：获取系统活动统计
  SystemLog.getActivityStats = async function(days = 7) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    const stats = await this.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
        'action',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        created_at: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        }
      },
      group: [
        sequelize.fn('DATE', sequelize.col('created_at')),
        'action'
      ],
      order: [
        [sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']
      ],
      raw: true
    });

    return stats;
  };

  // 类方法：清理旧日志
  SystemLog.cleanOldLogs = async function(daysToKeep = 90) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const deletedCount = await this.destroy({
      where: {
        created_at: {
          [sequelize.Sequelize.Op.lt]: cutoffDate
        }
      }
    });

    return deletedCount;
  };

  return SystemLog;
};
