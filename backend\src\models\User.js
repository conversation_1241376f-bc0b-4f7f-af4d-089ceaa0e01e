const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 50],
        is: /^[a-zA-Z0-9_]+$/
      }
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: [6, 255]
      }
    },
    display_name: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: '显示名称长度不能超过100个字符'
        }
      }
    },
    real_name: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: '真实姓名长度不能超过100个字符'
        }
      }
    },
    employee_id: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: {
          args: [0, 50],
          msg: '员工ID长度不能超过50个字符'
        }
      }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        isEmail: {
          msg: '邮箱格式不正确'
        },
        // 允许空值
        notEmpty: false
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: /^[0-9+\-\s()]+$/
      }
    },
    department: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: '部门名称长度不能超过100个字符'
        }
      }
    },
    role: {
      type: DataTypes.ENUM('admin', 'manager', 'supervisor', 'inspector'),
      defaultValue: 'inspector',
      allowNull: false
    },
    avatar: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        // 自定义验证：允许相对路径和完整URL
        isValidAvatarPath(value) {
          if (!value) return; // 允许空值

          // 允许相对路径（以/开头）或完整URL
          const isRelativePath = /^\//.test(value);
          const isFullUrl = /^https?:\/\//.test(value);

          if (!isRelativePath && !isFullUrl) {
            throw new Error('头像必须是有效的URL或相对路径');
          }
        }
      }
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'active',
      allowNull: false
    },
    last_login_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_login_ip: {
      type: DataTypes.STRING(45),
      allowNull: true
    }
  }, {
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
      // 创建用户前加密密码
      beforeCreate: async (user) => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      },
      // 更新用户前加密密码
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      }
    }
  });

  // 实例方法：验证密码
  User.prototype.validatePassword = async function(password) {
    return await bcrypt.compare(password, this.password);
  };

  // 实例方法：获取安全的用户信息（不包含密码）
  User.prototype.toSafeObject = function() {
    const { password, ...safeUser } = this.toJSON();
    return safeUser;
  };

  // 类方法：根据用户名或邮箱查找用户
  User.findByUsernameOrEmail = function(identifier) {
    return this.findOne({
      where: {
        [sequelize.Sequelize.Op.or]: [
          { username: identifier },
          { email: identifier }
        ]
      }
    });
  };

  // 类方法：获取用户统计信息
  User.getStatistics = async function() {
    const total = await this.count();
    const active = await this.count({ where: { status: 'active' } });
    const inactive = await this.count({ where: { status: 'inactive' } });
    
    const roleStats = await this.findAll({
      attributes: [
        'role',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['role'],
      raw: true
    });

    const departmentStats = await this.findAll({
      attributes: [
        'department',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        department: {
          [sequelize.Sequelize.Op.not]: null
        }
      },
      group: ['department'],
      raw: true
    });

    return {
      total,
      active,
      inactive,
      roles: roleStats.reduce((acc, item) => {
        acc[item.role] = parseInt(item.count);
        return acc;
      }, {}),
      departments: departmentStats.reduce((acc, item) => {
        acc[item.department] = parseInt(item.count);
        return acc;
      }, {})
    };
  };

  return User;
};
