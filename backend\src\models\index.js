const { Sequelize } = require('sequelize');
require('dotenv').config();

// 数据库配置
const sequelize = new Sequelize(
  process.env.DB_NAME || 'pipeline_inspection',
  process.env.DB_USER || 'root',
  process.env.DB_PASSWORD || '',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    dialect: 'mysql',
    timezone: '+08:00',
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true
    }
  }
);

// 导入模型
const User = require('./User')(sequelize);
const Device = require('./Device')(sequelize);
const Inspection = require('./Inspection')(sequelize);
const InspectionImage = require('./InspectionImage')(sequelize);
const SystemLog = require('./SystemLog')(sequelize);
const Statistics = require('./Statistics')(sequelize);
const SystemConfig = require('./SystemConfig')(sequelize);

// 定义关联关系
// 用户与巡检记录 (一对多)
User.hasMany(Inspection, {
  foreignKey: 'user_id',
  as: 'inspections'
});
Inspection.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 巡检记录与图片 (一对多)
Inspection.hasMany(InspectionImage, {
  foreignKey: 'inspection_id',
  as: 'images'
});
InspectionImage.belongsTo(Inspection, {
  foreignKey: 'inspection_id',
  as: 'inspection'
});

// 用户与系统日志 (一对多)
User.hasMany(SystemLog, {
  foreignKey: 'user_id',
  as: 'logs'
});
SystemLog.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 用户与统计数据 (一对多)
User.hasMany(Statistics, {
  foreignKey: 'user_id',
  as: 'statistics'
});
Statistics.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 设备与巡检记录的关联 (通过device_id字段)
// 注意：这里不使用外键约束，因为device_id是字符串类型
Device.hasMany(Inspection, {
  foreignKey: 'device_id',
  sourceKey: 'device_id',
  as: 'device_inspections'
});
Inspection.belongsTo(Device, {
  foreignKey: 'device_id',
  targetKey: 'device_id',
  as: 'device'
});

// 导出模型和数据库连接
module.exports = {
  sequelize,
  User,
  Device,
  Inspection,
  InspectionImage,
  SystemLog,
  Statistics,
  SystemConfig
};
