const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const { User, SystemLog } = require('../models');
const { logActivity } = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 登录限制：每15分钟最多5次尝试
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5,
  message: {
    error: '登录尝试次数过多，请15分钟后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// 注册验证规则
const registerValidation = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确'),
  body('display_name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('显示名称长度必须在1-100个字符之间')
];

// 登录验证规则
const loginValidation = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
];

// 生成JWT令牌
const generateToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      username: user.username,
      role: user.role
    },
    process.env.JWT_SECRET || 'your-secret-key',
    {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    }
  );
};

// 用户注册
router.post('/register', registerValidation, async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const { username, password, email, display_name, real_name, employee_id, department, role } = req.body;

    // 检查用户名是否已存在
    const existingUser = await User.findOne({ where: { username } });
    if (existingUser) {
      return res.status(400).json({
        error: '用户名已存在'
      });
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(400).json({
          error: '邮箱已被使用'
        });
      }
    }

    // 创建用户
    const user = await User.create({
      username,
      password,
      email,
      display_name,
      real_name,
      employee_id,
      department,
      role: role || 'inspector'
    });

    // 记录日志
    await logActivity(user.id, 'user_register', 'user', user.id, '用户注册', req.ip, req.get('User-Agent'));

    // 生成令牌
    const token = generateToken(user);

    res.status(201).json({
      message: '注册成功',
      token,
      user: user.toSafeObject()
    });

  } catch (error) {
    console.error('注册失败:', error);
    res.status(500).json({
      error: '注册失败，请稍后重试'
    });
  }
});

// 用户登录
router.post('/login', loginLimiter, loginValidation, async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const { username, password } = req.body;

    // 查找用户
    const user = await User.findByUsernameOrEmail(username);
    if (!user) {
      return res.status(401).json({
        error: '用户名或密码错误'
      });
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        error: '账户已被禁用，请联系管理员'
      });
    }

    // 验证密码
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({
        error: '用户名或密码错误'
      });
    }

    // 更新最后登录信息
    await user.update({
      last_login_at: new Date(),
      last_login_ip: req.ip
    });

    // 记录日志
    await logActivity(user.id, 'user_login', 'user', user.id, '用户登录', req.ip, req.get('User-Agent'));

    // 生成令牌
    const token = generateToken(user);

    res.json({
      message: '登录成功',
      token,
      user: user.toSafeObject()
    });

  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      error: '登录失败，请稍后重试'
    });
  }
});

// 刷新令牌
router.post('/refresh', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(401).json({
        error: '缺少访问令牌'
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // 查找用户
    const user = await User.findByPk(decoded.id);
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        error: '用户不存在或已被禁用'
      });
    }

    // 生成新令牌
    const newToken = generateToken(user);

    res.json({
      message: '令牌刷新成功',
      token: newToken,
      user: user.toSafeObject()
    });

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: '无效的访问令牌'
      });
    }

    console.error('刷新令牌失败:', error);
    res.status(500).json({
      error: '刷新令牌失败，请重新登录'
    });
  }
});

// 验证令牌
router.get('/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        error: '缺少访问令牌'
      });
    }

    // 验证令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // 查找用户
    const user = await User.findByPk(decoded.id);
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        error: '用户不存在或已被禁用'
      });
    }

    res.json({
      valid: true,
      user: user.toSafeObject()
    });

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        valid: false,
        error: '无效的访问令牌'
      });
    }

    console.error('验证令牌失败:', error);
    res.status(500).json({
      valid: false,
      error: '验证失败'
    });
  }
});

// 用户登出
router.post('/logout', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        // 记录登出日志
        await logActivity(decoded.id, 'user_logout', 'user', decoded.id, '用户登出', req.ip, req.get('User-Agent'));
      } catch (error) {
        // 忽略令牌验证错误
      }
    }

    res.json({
      message: '登出成功'
    });

  } catch (error) {
    console.error('登出失败:', error);
    res.status(500).json({
      error: '登出失败'
    });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.userId, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }

    res.json({
      user: user
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      error: '获取用户信息失败'
    });
  }
});

module.exports = router;
