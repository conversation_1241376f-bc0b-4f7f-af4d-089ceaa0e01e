const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Device, Inspection, User } = require('../models');
const { authenticateToken, requireManager } = require('../middleware/auth');
const { logDevice } = require('../utils/logger');

const router = express.Router();

// 所有设备路由都需要认证
router.use(authenticateToken);

// 获取设备列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('search').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('搜索关键词长度不能超过100'),
  query('type').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('设备类型长度不能超过100'),
  query('status').optional({ checkFalsy: true }).isIn(['active', 'maintenance', 'inactive']).withMessage('状态值无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      type,
      status,
      location
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // 搜索条件
    if (search) {
      whereClause[sequelize.Sequelize.Op.or] = [
        { device_id: { [sequelize.Sequelize.Op.like]: `%${search}%` } },
        { device_name: { [sequelize.Sequelize.Op.like]: `%${search}%` } },
        { location: { [sequelize.Sequelize.Op.like]: `%${search}%` } }
      ];
    }

    // 筛选条件
    if (type) whereClause.device_type = type;
    if (status) whereClause.status = status;
    if (location) whereClause.location = { [sequelize.Sequelize.Op.like]: `%${location}%` };

    const { rows: devices, count: total } = await Device.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      data: devices,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('获取设备列表失败:', error);
    res.status(500).json({
      error: '获取设备列表失败'
    });
  }
});

// 获取设备详情
router.get('/:deviceId', async (req, res) => {
  try {
    const { deviceId } = req.params;
    
    const device = await Device.findOne({
      where: { device_id: deviceId },
      include: [
        {
          model: Inspection,
          as: 'inspections',
          limit: 10,
          order: [['inspection_time', 'DESC']],
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['display_name', 'username']
            }
          ]
        }
      ]
    });

    if (!device) {
      return res.status(404).json({
        error: '设备不存在'
      });
    }

    // 获取设备巡检统计
    const stats = await Inspection.getStatistics({
      deviceId: device.device_id
    });

    res.json({
      device: device.toJSON(),
      stats
    });

  } catch (error) {
    console.error('获取设备详情失败:', error);
    res.status(500).json({
      error: '获取设备详情失败'
    });
  }
});

// 创建设备（管理员权限）
router.post('/', requireManager, [
  body('device_id')
    .isLength({ min: 1, max: 100 })
    .withMessage('设备ID长度必须在1-100个字符之间')
    .matches(/^[A-Za-z0-9_-]+$/)
    .withMessage('设备ID只能包含字母、数字、下划线和连字符'),
  body('device_name')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('设备名称长度必须在1-200个字符之间'),
  body('device_type')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('设备类型长度必须在1-100个字符之间'),
  body('location')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('设备位置长度必须在1-255个字符之间'),
  body('status')
    .optional()
    .isIn(['active', 'maintenance', 'inactive'])
    .withMessage('状态值无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const {
      device_id,
      device_name,
      device_type,
      location,
      description,
      status = 'active'
    } = req.body;

    // 检查设备ID是否已存在
    const existingDevice = await Device.findOne({ where: { device_id } });
    if (existingDevice) {
      return res.status(400).json({
        error: '设备ID已存在'
      });
    }

    // 创建设备
    const device = await Device.create({
      device_id,
      device_name,
      device_type,
      location,
      description,
      status
    });

    // 记录日志
    await logDevice(
      req.userId,
      'create_device',
      device_id,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: '设备创建成功',
      device: device.toJSON()
    });

  } catch (error) {
    console.error('创建设备失败:', error);
    res.status(500).json({
      error: '创建设备失败'
    });
  }
});

// 更新设备信息（管理员权限）
router.put('/:deviceId', requireManager, [
  body('device_name')
    .optional()
    .isLength({ min: 1, max: 200 })
    .withMessage('设备名称长度必须在1-200个字符之间'),
  body('device_type')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('设备类型长度必须在1-100个字符之间'),
  body('location')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('设备位置长度必须在1-255个字符之间'),
  body('status')
    .optional()
    .isIn(['active', 'maintenance', 'inactive'])
    .withMessage('状态值无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const { deviceId } = req.params;
    const device = await Device.findOne({ where: { device_id: deviceId } });

    if (!device) {
      return res.status(404).json({
        error: '设备不存在'
      });
    }

    const {
      device_name,
      device_type,
      location,
      description,
      status
    } = req.body;

    const updateData = {};
    if (device_name !== undefined) updateData.device_name = device_name;
    if (device_type !== undefined) updateData.device_type = device_type;
    if (location !== undefined) updateData.location = location;
    if (description !== undefined) updateData.description = description;
    if (status !== undefined) updateData.status = status;

    await device.update(updateData);

    // 记录日志
    await logDevice(
      req.userId,
      'update_device',
      deviceId,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '设备信息更新成功',
      device: device.toJSON()
    });

  } catch (error) {
    console.error('更新设备信息失败:', error);
    res.status(500).json({
      error: '更新设备信息失败'
    });
  }
});

// 删除设备（管理员权限）
router.delete('/:deviceId', requireManager, async (req, res) => {
  try {
    const { deviceId } = req.params;
    
    const device = await Device.findOne({ where: { device_id: deviceId } });
    if (!device) {
      return res.status(404).json({
        error: '设备不存在'
      });
    }

    // 检查是否有关联的巡检记录
    const inspectionCount = await Inspection.count({
      where: { device_id: deviceId }
    });

    if (inspectionCount > 0) {
      return res.status(400).json({
        error: '该设备存在巡检记录，无法删除'
      });
    }

    await device.destroy();

    // 记录日志
    await logDevice(
      req.userId,
      'delete_device',
      deviceId,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '设备删除成功'
    });

  } catch (error) {
    console.error('删除设备失败:', error);
    res.status(500).json({
      error: '删除设备失败'
    });
  }
});

// 获取设备统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const stats = await Device.getStatistics();
    res.json(stats);
  } catch (error) {
    console.error('获取设备统计失败:', error);
    res.status(500).json({
      error: '获取设备统计失败'
    });
  }
});

// 获取设备类型列表
router.get('/types/list', async (req, res) => {
  try {
    const types = await Device.findAll({
      attributes: [
        'device_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        device_type: {
          [sequelize.Sequelize.Op.not]: null
        }
      },
      group: ['device_type'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
      raw: true
    });

    res.json({
      types: types.map(type => ({
        name: type.device_type,
        count: parseInt(type.count)
      }))
    });

  } catch (error) {
    console.error('获取设备类型失败:', error);
    res.status(500).json({
      error: '获取设备类型失败'
    });
  }
});

// 批量导入设备（管理员权限）
router.post('/batch/import', requireManager, [
  body('devices')
    .isArray({ min: 1 })
    .withMessage('设备列表不能为空'),
  body('devices.*.device_id')
    .isLength({ min: 1, max: 100 })
    .withMessage('设备ID长度必须在1-100个字符之间')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const { devices } = req.body;
    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    for (const deviceData of devices) {
      try {
        // 检查设备ID是否已存在
        const existing = await Device.findOne({
          where: { device_id: deviceData.device_id }
        });

        if (existing) {
          results.failed++;
          results.errors.push({
            device_id: deviceData.device_id,
            error: '设备ID已存在'
          });
          continue;
        }

        await Device.create(deviceData);
        results.success++;

      } catch (error) {
        results.failed++;
        results.errors.push({
          device_id: deviceData.device_id,
          error: error.message
        });
      }
    }

    // 记录日志
    await logDevice(
      req.userId,
      'batch_import_devices',
      `${results.success}/${devices.length}`,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '批量导入完成',
      results
    });

  } catch (error) {
    console.error('批量导入设备失败:', error);
    res.status(500).json({
      error: '批量导入设备失败'
    });
  }
});

module.exports = router;
