const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { Inspection, InspectionImage, User, Device } = require('../models');
const { authenticateToken, requireSelfOrAdmin } = require('../middleware/auth');
const { logInspection } = require('../utils/logger');

const router = express.Router();

// 所有巡检路由都需要认证
router.use(authenticateToken);

// 获取巡检记录列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('user_id').optional({ checkFalsy: true }).isInt({ min: 1 }).withMessage('用户ID必须是正整数'),
  query('device_id').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('设备ID长度不能超过100'),
  query('inspection_type').optional({ checkFalsy: true }).isIn(['triangle', 'upward', 'normal']).withMessage('巡检类型无效'),
  query('inspection_status').optional({ checkFalsy: true }).isIn(['normal', 'abnormal', 'warning']).withMessage('巡检状态无效'),
  query('start_date').optional().isISO8601().withMessage('开始日期格式无效'),
  query('end_date').optional().isISO8601().withMessage('结束日期格式无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      user_id,
      device_id,
      inspection_type,
      inspection_status,
      start_date,
      end_date,
      location
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // 筛选条件
    if (user_id) whereClause.user_id = user_id;
    if (device_id) whereClause.device_id = device_id;
    if (inspection_type) whereClause.inspection_type = inspection_type;
    if (inspection_status) whereClause.inspection_status = inspection_status;
    if (location) whereClause.location = { [sequelize.Sequelize.Op.like]: `%${location}%` };

    // 时间范围筛选
    if (start_date && end_date) {
      whereClause.inspection_time = {
        [sequelize.Sequelize.Op.between]: [start_date, end_date]
      };
    } else if (start_date) {
      whereClause.inspection_time = {
        [sequelize.Sequelize.Op.gte]: start_date
      };
    } else if (end_date) {
      whereClause.inspection_time = {
        [sequelize.Sequelize.Op.lte]: end_date
      };
    }

    // 非管理员只能查看自己的记录
    if (req.user.role === 'inspector') {
      whereClause.user_id = req.userId;
    }

    const { rows: inspections, count: total } = await Inspection.findAndCountAll({
      where: whereClause,
      order: [['inspection_time', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'display_name', 'department']
        },
        {
          model: InspectionImage,
          as: 'images',
          attributes: ['id', 'image_type', 'image_url', 'file_size']
        }
      ]
    });

    res.json({
      data: inspections,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('获取巡检记录失败:', error);
    res.status(500).json({
      error: '获取巡检记录失败'
    });
  }
});

// 获取巡检记录详情
router.get('/:id', async (req, res) => {
  try {
    const inspectionId = parseInt(req.params.id);
    
    const inspection = await Inspection.findByPk(inspectionId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'display_name', 'department']
        },
        {
          model: InspectionImage,
          as: 'images',
          order: [['image_type', 'ASC']]
        }
      ]
    });

    if (!inspection) {
      return res.status(404).json({
        error: '巡检记录不存在'
      });
    }

    // 非管理员只能查看自己的记录
    if (req.user.role === 'inspector' && inspection.user_id !== req.userId) {
      return res.status(403).json({
        error: '无权访问此记录'
      });
    }

    res.json({
      inspection: inspection.toJSON()
    });

  } catch (error) {
    console.error('获取巡检记录详情失败:', error);
    res.status(500).json({
      error: '获取巡检记录详情失败'
    });
  }
});

// 创建巡检记录
router.post('/', [
  body('device_id')
    .isLength({ min: 1, max: 100 })
    .withMessage('设备ID长度必须在1-100个字符之间'),
  body('inspection_type')
    .isIn(['triangle', 'upward', 'normal'])
    .withMessage('巡检类型无效'),
  body('inspection_status')
    .isIn(['normal', 'abnormal', 'warning'])
    .withMessage('巡检状态无效'),
  body('inspection_time')
    .optional()
    .isISO8601()
    .withMessage('巡检时间格式无效'),
  body('images')
    .optional()
    .isArray()
    .withMessage('图片列表必须是数组')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const {
      device_id,
      inspection_type,
      inspection_status,
      location,
      notes,
      abnormal_info,
      inspection_time,
      images = []
    } = req.body;

    // 创建巡检记录
    const inspection = await Inspection.create({
      user_id: req.userId,
      device_id,
      inspection_type,
      inspection_status,
      location,
      notes,
      abnormal_info,
      inspection_time: inspection_time || new Date(),
      upload_time: new Date()
    });

    // 创建图片记录
    if (images.length > 0) {
      const imageRecords = images.map(image => ({
        inspection_id: inspection.id,
        image_type: image.type,
        image_url: image.url,
        image_name: image.name,
        file_size: image.size
      }));

      await InspectionImage.bulkCreate(imageRecords);
    }

    // 重新查询包含图片的完整记录
    const fullInspection = await Inspection.findByPk(inspection.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'display_name']
        },
        {
          model: InspectionImage,
          as: 'images'
        }
      ]
    });

    // 记录日志
    await logInspection(
      req.userId,
      'create_inspection',
      inspection.id,
      device_id,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: '巡检记录创建成功',
      inspection: fullInspection.toJSON()
    });

  } catch (error) {
    console.error('创建巡检记录失败:', error);
    console.error('错误详情:', error.message);
    console.error('错误堆栈:', error.stack);
    res.status(500).json({
      error: '创建巡检记录失败',
      details: error.message
    });
  }
});

// 更新巡检记录
router.put('/:id', [
  body('inspection_status')
    .optional()
    .isIn(['normal', 'abnormal', 'warning'])
    .withMessage('巡检状态无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const inspectionId = parseInt(req.params.id);
    const inspection = await Inspection.findByPk(inspectionId);

    if (!inspection) {
      return res.status(404).json({
        error: '巡检记录不存在'
      });
    }

    // 只能修改自己的记录或管理员权限
    if (inspection.user_id !== req.userId && !['admin', 'manager', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        error: '无权修改此记录'
      });
    }

    const {
      inspection_status,
      location,
      notes,
      abnormal_info
    } = req.body;

    const updateData = {};
    if (inspection_status !== undefined) updateData.inspection_status = inspection_status;
    if (location !== undefined) updateData.location = location;
    if (notes !== undefined) updateData.notes = notes;
    if (abnormal_info !== undefined) updateData.abnormal_info = abnormal_info;

    await inspection.update(updateData);

    // 记录日志
    await logInspection(
      req.userId,
      'update_inspection',
      inspectionId,
      inspection.device_id,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '巡检记录更新成功',
      inspection: inspection.toJSON()
    });

  } catch (error) {
    console.error('更新巡检记录失败:', error);
    res.status(500).json({
      error: '更新巡检记录失败'
    });
  }
});

// 删除巡检记录
router.delete('/:id', async (req, res) => {
  try {
    const inspectionId = parseInt(req.params.id);
    const inspection = await Inspection.findByPk(inspectionId);

    if (!inspection) {
      return res.status(404).json({
        error: '巡检记录不存在'
      });
    }

    // 只能删除自己的记录或管理员权限
    if (inspection.user_id !== req.userId && !['admin', 'manager'].includes(req.user.role)) {
      return res.status(403).json({
        error: '无权删除此记录'
      });
    }

    await inspection.destroy();

    // 记录日志
    await logInspection(
      req.userId,
      'delete_inspection',
      inspectionId,
      inspection.device_id,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '巡检记录删除成功'
    });

  } catch (error) {
    console.error('删除巡检记录失败:', error);
    res.status(500).json({
      error: '删除巡检记录失败'
    });
  }
});

// 获取巡检统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const { user_id, start_date, end_date } = req.query;
    
    // 非管理员只能查看自己的统计
    const userId = req.user.role === 'inspector' ? req.userId : user_id;
    
    const stats = await Inspection.getStatistics({
      userId,
      startDate: start_date,
      endDate: end_date
    });

    res.json(stats);

  } catch (error) {
    console.error('获取巡检统计失败:', error);
    res.status(500).json({
      error: '获取巡检统计失败'
    });
  }
});

// 获取巡检趋势数据
router.get('/stats/trend', async (req, res) => {
  try {
    const { days = 7 } = req.query;
    const trendData = await Inspection.getTrendData(parseInt(days));
    res.json(trendData);
  } catch (error) {
    console.error('获取巡检趋势失败:', error);
    res.status(500).json({
      error: '获取巡检趋势失败'
    });
  }
});

// 批量导出巡检记录
router.post('/export', async (req, res) => {
  try {
    const {
      user_id,
      device_id,
      inspection_status,
      start_date,
      end_date,
      format = 'json'
    } = req.body;

    const whereClause = {};
    if (user_id) whereClause.user_id = user_id;
    if (device_id) whereClause.device_id = device_id;
    if (inspection_status) whereClause.inspection_status = inspection_status;
    
    if (start_date && end_date) {
      whereClause.inspection_time = {
        [sequelize.Sequelize.Op.between]: [start_date, end_date]
      };
    }

    // 非管理员只能导出自己的记录
    if (req.user.role === 'inspector') {
      whereClause.user_id = req.userId;
    }

    const inspections = await Inspection.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username', 'display_name', 'department']
        },
        {
          model: InspectionImage,
          as: 'images',
          attributes: ['image_type', 'image_url']
        }
      ],
      order: [['inspection_time', 'DESC']]
    });

    // 记录导出日志
    await logActivity(
      req.userId,
      'export_inspections',
      'export',
      'inspections',
      `导出巡检记录: ${inspections.length} 条`,
      req.ip,
      req.get('User-Agent')
    );

    if (format === 'csv') {
      // TODO: 实现CSV格式导出
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=inspections.csv');
      // 简化版CSV输出
      const csvData = inspections.map(i => 
        `${i.device_id},${i.inspection_type},${i.inspection_status},${i.inspection_time}`
      ).join('\n');
      res.send('设备ID,巡检类型,巡检状态,巡检时间\n' + csvData);
    } else {
      res.json({
        data: inspections,
        total: inspections.length,
        exported_at: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('导出巡检记录失败:', error);
    res.status(500).json({
      error: '导出巡检记录失败'
    });
  }
});

module.exports = router;
