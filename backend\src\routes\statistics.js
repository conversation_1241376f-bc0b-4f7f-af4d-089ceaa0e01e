const express = require('express');
const { query, validationResult } = require('express-validator');
const { User, Device, Inspection, InspectionImage, Statistics } = require('../models');
const { authenticateToken, requireManager } = require('../middleware/auth');

const router = express.Router();

// 所有统计路由都需要认证
router.use(authenticateToken);

// 获取仪表板统计数据
router.get('/dashboard', async (req, res) => {
  try {
    // 基础统计
    const totalUsers = await User.count({ where: { status: 'active' } });
    const totalDevices = await Device.count({ where: { status: 'active' } });
    const totalInspections = await Inspection.count();
    
    // 今日统计
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const todayInspections = await Inspection.count({
      where: {
        inspection_time: {
          [sequelize.Sequelize.Op.gte]: today,
          [sequelize.Sequelize.Op.lt]: tomorrow
        }
      }
    });

    // 本月统计 (暂时简化)
    const monthlyStats = {
      total: 0,
      normal: 0,
      abnormal: 0,
      warning: 0,
      normalRate: 0
    };

    // 异常统计
    const abnormalInspections = await Inspection.count({
      where: { inspection_status: 'abnormal' }
    });

    const warningInspections = await Inspection.count({
      where: { inspection_status: 'warning' }
    });

    // 图片统计 (暂时简化)
    const imageStats = { total: 0, totalSize: 0 };

    res.json({
      overview: {
        totalUsers,
        totalDevices,
        totalInspections,
        abnormalInspections,
        warningInspections,
        todayInspections
      },
      monthly: monthlyStats,
      images: imageStats
    });

  } catch (error) {
    console.error('获取仪表板统计失败:', error);
    res.status(500).json({
      error: '获取仪表板统计失败'
    });
  }
});

// 获取趋势数据
router.get('/trend', [
  query('days').optional().isInt({ min: 1, max: 365 }).withMessage('天数必须在1-365之间'),
  query('type').optional().isIn(['inspection', 'user', 'device']).withMessage('类型无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const { days = 7, type = 'inspection' } = req.query;

    if (type === 'inspection') {
      const trendData = await Inspection.getTrendData(parseInt(days));
      res.json(trendData);
    } else {
      // 其他类型的趋势数据可以在这里实现
      res.json({
        dates: [],
        data: []
      });
    }

  } catch (error) {
    console.error('获取趋势数据失败:', error);
    res.status(500).json({
      error: '获取趋势数据失败'
    });
  }
});

// 获取用户统计排行
router.get('/users/ranking', [
  query('period').optional().isIn(['today', 'week', 'month', 'year']).withMessage('时间周期无效'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('限制数量必须在1-100之间')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const { period = 'month', limit = 10 } = req.query;
    
    // 计算时间范围
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    const userStats = await Inspection.findAll({
      attributes: [
        'user_id',
        [sequelize.fn('COUNT', sequelize.col('Inspection.id')), 'total_inspections'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_status = 'normal' THEN 1 ELSE 0 END")), 'normal_inspections'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_status = 'abnormal' THEN 1 ELSE 0 END")), 'abnormal_inspections']
      ],
      where: {
        inspection_time: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        }
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username', 'display_name', 'department']
        }
      ],
      group: ['user_id', 'user.id'],
      order: [[sequelize.fn('COUNT', sequelize.col('Inspection.id')), 'DESC']],
      limit: parseInt(limit),
      raw: false
    });

    const ranking = userStats.map((stat, index) => ({
      rank: index + 1,
      user: stat.user,
      total_inspections: parseInt(stat.dataValues.total_inspections),
      normal_inspections: parseInt(stat.dataValues.normal_inspections),
      abnormal_inspections: parseInt(stat.dataValues.abnormal_inspections),
      normal_rate: stat.dataValues.total_inspections > 0 
        ? ((stat.dataValues.normal_inspections / stat.dataValues.total_inspections) * 100).toFixed(2)
        : 0
    }));

    res.json({
      period,
      ranking
    });

  } catch (error) {
    console.error('获取用户统计排行失败:', error);
    res.status(500).json({
      error: '获取用户统计排行失败'
    });
  }
});

// 获取设备统计排行
router.get('/devices/ranking', [
  query('period').optional().isIn(['today', 'week', 'month', 'year']).withMessage('时间周期无效'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('限制数量必须在1-100之间')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const { period = 'month', limit = 10 } = req.query;
    
    // 计算时间范围
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }

    const deviceStats = await Inspection.findAll({
      attributes: [
        'device_id',
        [sequelize.fn('COUNT', sequelize.col('id')), 'total_inspections'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_status = 'abnormal' THEN 1 ELSE 0 END")), 'abnormal_inspections'],
        [sequelize.fn('MAX', sequelize.col('inspection_time')), 'last_inspection']
      ],
      where: {
        inspection_time: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        }
      },
      include: [
        {
          model: Device,
          as: 'device',
          attributes: ['device_name', 'device_type', 'location'],
          required: false
        }
      ],
      group: ['device_id'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
      limit: parseInt(limit),
      raw: false
    });

    const ranking = deviceStats.map((stat, index) => ({
      rank: index + 1,
      device_id: stat.device_id,
      device: stat.device,
      total_inspections: parseInt(stat.dataValues.total_inspections),
      abnormal_inspections: parseInt(stat.dataValues.abnormal_inspections),
      last_inspection: stat.dataValues.last_inspection,
      abnormal_rate: stat.dataValues.total_inspections > 0 
        ? ((stat.dataValues.abnormal_inspections / stat.dataValues.total_inspections) * 100).toFixed(2)
        : 0
    }));

    res.json({
      period,
      ranking
    });

  } catch (error) {
    console.error('获取设备统计排行失败:', error);
    res.status(500).json({
      error: '获取设备统计排行失败'
    });
  }
});

// 获取部门统计
router.get('/departments', async (req, res) => {
  try {
    const departmentStats = await User.findAll({
      attributes: [
        'department',
        [sequelize.fn('COUNT', sequelize.col('User.id')), 'user_count']
      ],
      where: {
        department: {
          [sequelize.Sequelize.Op.not]: null
        },
        status: 'active'
      },
      include: [
        {
          model: Inspection,
          as: 'inspections',
          attributes: [],
          required: false
        }
      ],
      group: ['department'],
      order: [[sequelize.fn('COUNT', sequelize.col('User.id')), 'DESC']],
      raw: true
    });

    // 获取每个部门的巡检统计
    const departmentInspectionStats = await Inspection.findAll({
      attributes: [
        [sequelize.col('user.department'), 'department'],
        [sequelize.fn('COUNT', sequelize.col('Inspection.id')), 'total_inspections'],
        [sequelize.fn('SUM', sequelize.literal("CASE WHEN inspection_status = 'normal' THEN 1 ELSE 0 END")), 'normal_inspections']
      ],
      include: [
        {
          model: User,
          as: 'user',
          attributes: [],
          where: {
            department: {
              [sequelize.Sequelize.Op.not]: null
            }
          }
        }
      ],
      group: ['user.department'],
      raw: true
    });

    // 合并统计数据
    const departments = departmentStats.map(dept => {
      const inspectionStat = departmentInspectionStats.find(
        stat => stat.department === dept.department
      );
      
      const totalInspections = inspectionStat ? parseInt(inspectionStat.total_inspections) : 0;
      const normalInspections = inspectionStat ? parseInt(inspectionStat.normal_inspections) : 0;
      
      return {
        department: dept.department,
        user_count: parseInt(dept.user_count),
        total_inspections: totalInspections,
        normal_inspections: normalInspections,
        normal_rate: totalInspections > 0 
          ? ((normalInspections / totalInspections) * 100).toFixed(2)
          : 0
      };
    });

    res.json({
      departments
    });

  } catch (error) {
    console.error('获取部门统计失败:', error);
    res.status(500).json({
      error: '获取部门统计失败'
    });
  }
});

// 生成统计报告（管理员权限）
router.post('/report', requireManager, async (req, res) => {
  try {
    const {
      start_date,
      end_date,
      include_users = true,
      include_devices = true,
      include_inspections = true
    } = req.body;

    const report = {
      generated_at: new Date().toISOString(),
      period: {
        start_date,
        end_date
      },
      summary: {},
      details: {}
    };

    // 时间范围
    const whereClause = {};
    if (start_date && end_date) {
      whereClause.inspection_time = {
        [sequelize.Sequelize.Op.between]: [start_date, end_date]
      };
    }

    // 总体统计
    const totalInspections = await Inspection.count({ where: whereClause });
    const normalInspections = await Inspection.count({
      where: { ...whereClause, inspection_status: 'normal' }
    });
    const abnormalInspections = await Inspection.count({
      where: { ...whereClause, inspection_status: 'abnormal' }
    });

    report.summary = {
      total_inspections: totalInspections,
      normal_inspections: normalInspections,
      abnormal_inspections: abnormalInspections,
      normal_rate: totalInspections > 0 
        ? ((normalInspections / totalInspections) * 100).toFixed(2)
        : 0
    };

    // 详细统计
    if (include_users) {
      // 用户统计逻辑
      report.details.users = await User.getStatistics();
    }

    if (include_devices) {
      // 设备统计逻辑
      report.details.devices = await Device.getStatistics();
    }

    if (include_inspections) {
      // 巡检统计逻辑
      report.details.inspections = await Inspection.getStatistics({
        startDate: start_date,
        endDate: end_date
      });
    }

    res.json(report);

  } catch (error) {
    console.error('生成统计报告失败:', error);
    res.status(500).json({
      error: '生成统计报告失败'
    });
  }
});

module.exports = router;
