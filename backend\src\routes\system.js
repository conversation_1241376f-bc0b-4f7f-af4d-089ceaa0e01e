const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { SystemConfig, SystemLog, User, Inspection, Device } = require('../models');
const { authenticateToken, requireAdmin, optionalAuth } = require('../middleware/auth');
const { logConfigChange } = require('../utils/logger');

const router = express.Router();

// 获取公开配置（无需认证）
router.get('/config/public', async (req, res) => {
  try {
    const publicConfigs = await SystemConfig.getPublicConfigs();
    res.json(publicConfigs);
  } catch (error) {
    console.error('获取公开配置失败:', error);
    res.status(500).json({
      error: '获取公开配置失败'
    });
  }
});

// 以下路由需要认证
router.use(authenticateToken);

// 获取系统信息
router.get('/info', async (req, res) => {
  try {
    const systemInfo = {
      app_name: await SystemConfig.getValue('app_name', '管道巡检系统'),
      app_version: await SystemConfig.getValue('app_version', '1.0.0'),
      node_version: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memory_usage: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development'
    };

    res.json(systemInfo);
  } catch (error) {
    console.error('获取系统信息失败:', error);
    res.status(500).json({
      error: '获取系统信息失败'
    });
  }
});

// 获取系统健康状态
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      checks: {}
    };

    // 数据库连接检查
    try {
      await User.findOne({ limit: 1 });
      health.checks.database = { status: 'ok', message: '数据库连接正常' };
    } catch (error) {
      health.checks.database = { status: 'error', message: '数据库连接失败' };
      health.status = 'error';
    }

    // 内存使用检查
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
    if (memoryUsageMB > 500) {
      health.checks.memory = { status: 'warning', message: `内存使用较高: ${memoryUsageMB.toFixed(2)}MB` };
    } else {
      health.checks.memory = { status: 'ok', message: `内存使用正常: ${memoryUsageMB.toFixed(2)}MB` };
    }

    res.json(health);
  } catch (error) {
    console.error('获取系统健康状态失败:', error);
    res.status(500).json({
      status: 'error',
      error: '获取系统健康状态失败'
    });
  }
});

// 获取系统配置（管理员权限）
router.get('/config', requireAdmin, [
  query('key').optional().isLength({ max: 100 }).withMessage('配置键长度不能超过100')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const { key } = req.query;

    if (key) {
      // 获取单个配置
      const config = await SystemConfig.findOne({
        where: { config_key: key }
      });

      if (!config) {
        return res.status(404).json({
          error: '配置不存在'
        });
      }

      res.json({
        key: config.config_key,
        value: config.getParsedValue(),
        type: config.config_type,
        description: config.description,
        is_public: config.is_public
      });
    } else {
      // 获取所有配置
      const configs = await SystemConfig.findAll({
        order: [['config_key', 'ASC']]
      });

      const configList = configs.map(config => ({
        key: config.config_key,
        value: config.getParsedValue(),
        type: config.config_type,
        description: config.description,
        is_public: config.is_public,
        updated_at: config.updated_at
      }));

      res.json({
        configs: configList
      });
    }

  } catch (error) {
    console.error('获取系统配置失败:', error);
    res.status(500).json({
      error: '获取系统配置失败'
    });
  }
});

// 设置系统配置（管理员权限）
router.put('/config/:key', requireAdmin, [
  body('value').notEmpty().withMessage('配置值不能为空'),
  body('type').optional().isIn(['string', 'number', 'boolean', 'json']).withMessage('配置类型无效'),
  body('description').optional().isLength({ max: 255 }).withMessage('描述长度不能超过255'),
  body('is_public').optional().isBoolean().withMessage('公开标志必须是布尔值')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const { key } = req.params;
    const { value, type = 'string', description, is_public = false } = req.body;

    // 获取旧值用于日志记录
    const oldConfig = await SystemConfig.findOne({
      where: { config_key: key }
    });
    const oldValue = oldConfig ? oldConfig.config_value : null;

    // 设置配置
    const config = await SystemConfig.setValue(key, value, type, description, is_public);

    // 记录配置变更日志
    await logConfigChange(
      req.userId,
      key,
      oldValue,
      value.toString(),
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '配置设置成功',
      config: {
        key: config.config_key,
        value: config.getParsedValue(),
        type: config.config_type,
        description: config.description,
        is_public: config.is_public
      }
    });

  } catch (error) {
    console.error('设置系统配置失败:', error);
    res.status(500).json({
      error: '设置系统配置失败'
    });
  }
});

// 删除系统配置（管理员权限）
router.delete('/config/:key', requireAdmin, async (req, res) => {
  try {
    const { key } = req.params;

    const config = await SystemConfig.findOne({
      where: { config_key: key }
    });

    if (!config) {
      return res.status(404).json({
        error: '配置不存在'
      });
    }

    await config.destroy();

    // 记录配置删除日志
    await logConfigChange(
      req.userId,
      key,
      config.config_value,
      null,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '配置删除成功'
    });

  } catch (error) {
    console.error('删除系统配置失败:', error);
    res.status(500).json({
      error: '删除系统配置失败'
    });
  }
});

// 获取系统日志（管理员权限）
router.get('/logs', requireAdmin, [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('action').optional().isLength({ max: 100 }).withMessage('操作类型长度不能超过100'),
  query('user_id').optional().isInt({ min: 1 }).withMessage('用户ID必须是正整数'),
  query('start_date').optional().isISO8601().withMessage('开始日期格式无效'),
  query('end_date').optional().isISO8601().withMessage('结束日期格式无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const {
      page = 1,
      limit = 50,
      action,
      user_id,
      start_date,
      end_date
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // 筛选条件
    if (action) whereClause.action = action;
    if (user_id) whereClause.user_id = user_id;

    // 时间范围筛选
    if (start_date && end_date) {
      whereClause.created_at = {
        [sequelize.Sequelize.Op.between]: [start_date, end_date]
      };
    }

    const { rows: logs, count: total } = await SystemLog.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username', 'display_name'],
          required: false
        }
      ]
    });

    res.json({
      data: logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('获取系统日志失败:', error);
    res.status(500).json({
      error: '获取系统日志失败'
    });
  }
});

// 清理系统日志（管理员权限）
router.delete('/logs/cleanup', requireAdmin, [
  body('days').isInt({ min: 1, max: 365 }).withMessage('保留天数必须在1-365之间')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const { days } = req.body;
    const deletedCount = await SystemLog.cleanOldLogs(days);

    res.json({
      message: '日志清理完成',
      deleted_count: deletedCount
    });

  } catch (error) {
    console.error('清理系统日志失败:', error);
    res.status(500).json({
      error: '清理系统日志失败'
    });
  }
});

// 初始化系统配置（管理员权限）
router.post('/init', requireAdmin, async (req, res) => {
  try {
    const results = await SystemConfig.initializeDefaults();

    res.json({
      message: '系统配置初始化完成',
      initialized_configs: results.length
    });

  } catch (error) {
    console.error('初始化系统配置失败:', error);
    res.status(500).json({
      error: '初始化系统配置失败'
    });
  }
});

// 系统维护模式切换（管理员权限）
router.post('/maintenance', requireAdmin, [
  body('enabled').isBoolean().withMessage('维护模式标志必须是布尔值'),
  body('message').optional().isLength({ max: 255 }).withMessage('维护消息长度不能超过255')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const { enabled, message = '系统维护中，请稍后再试' } = req.body;

    await SystemConfig.setValue('maintenance_mode', enabled, 'boolean', '维护模式开关', true);
    await SystemConfig.setValue('maintenance_message', message, 'string', '维护模式消息', true);

    res.json({
      message: enabled ? '维护模式已开启' : '维护模式已关闭',
      maintenance_enabled: enabled,
      maintenance_message: message
    });

  } catch (error) {
    console.error('切换维护模式失败:', error);
    res.status(500).json({
      error: '切换维护模式失败'
    });
  }
});

module.exports = router;
