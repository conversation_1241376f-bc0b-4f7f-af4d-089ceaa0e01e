const express = require('express');
const multer = require('multer');
// const sharp = require('sharp'); // 暂时注释掉，因为安装失败
const path = require('path');
const fs = require('fs').promises;
const { authenticateToken } = require('../middleware/auth');
const { logFileUpload } = require('../utils/logger');

const router = express.Router();

// 所有上传路由都需要认证
router.use(authenticateToken);

// 确保上传目录存在
const ensureUploadDir = async (dir) => {
  try {
    await fs.access(dir);
  } catch (error) {
    await fs.mkdir(dir, { recursive: true });
  }
};

// 配置multer存储
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads');
    await ensureUploadDir(uploadDir);
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const ext = path.extname(file.originalname);
    const filename = `${timestamp}_${randomStr}${ext}`;
    cb(null, filename);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型，只允许上传 JPEG, PNG, GIF 格式的图片'), false);
  }
};

// 配置multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    files: 5 // 最多5个文件
  }
});

// 图片处理函数 (暂时禁用，因为sharp安装失败)
const processImage = async (filePath, options = {}) => {
  try {
    // 暂时直接返回原文件路径，不进行图片处理
    console.log('图片处理功能暂时禁用，直接返回原文件');
    return filePath;
  } catch (error) {
    console.error('图片处理失败:', error);
    return filePath; // 处理失败时返回原文件路径
  }
};

// 上传单张图片
router.post('/image', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: '没有上传文件'
      });
    }

    const { compress = 'true', width, height, quality } = req.body;
    let filePath = req.file.path;

    // 如果需要压缩图片
    if (compress === 'true') {
      const processOptions = {};
      if (width) processOptions.width = parseInt(width);
      if (height) processOptions.height = parseInt(height);
      if (quality) processOptions.quality = parseInt(quality);

      filePath = await processImage(filePath, processOptions);
    }

    // 获取文件信息
    const stats = await fs.stat(filePath);
    const fileUrl = `/uploads/${path.basename(filePath)}`;

    // 记录上传日志
    await logFileUpload(
      req.userId,
      req.file.originalname,
      stats.size,
      req.file.mimetype,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '图片上传成功',
      file: {
        filename: path.basename(filePath),
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: stats.size,
        url: fileUrl,
        uploaded_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('图片上传失败:', error);
    
    // 清理上传的文件
    if (req.file && req.file.path) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('清理文件失败:', unlinkError);
      }
    }

    res.status(500).json({
      error: '图片上传失败'
    });
  }
});

// 上传多张图片
router.post('/images', upload.array('images', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        error: '没有上传文件'
      });
    }

    const { compress = 'true', width, height, quality } = req.body;
    const uploadedFiles = [];

    for (const file of req.files) {
      try {
        let filePath = file.path;

        // 如果需要压缩图片
        if (compress === 'true') {
          const processOptions = {};
          if (width) processOptions.width = parseInt(width);
          if (height) processOptions.height = parseInt(height);
          if (quality) processOptions.quality = parseInt(quality);

          filePath = await processImage(filePath, processOptions);
        }

        // 获取文件信息
        const stats = await fs.stat(filePath);
        const fileUrl = `/uploads/${path.basename(filePath)}`;

        uploadedFiles.push({
          filename: path.basename(filePath),
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: stats.size,
          url: fileUrl
        });

        // 记录上传日志
        await logFileUpload(
          req.userId,
          file.originalname,
          stats.size,
          file.mimetype,
          req.ip,
          req.get('User-Agent')
        );

      } catch (error) {
        console.error(`处理文件 ${file.originalname} 失败:`, error);
        // 继续处理其他文件
      }
    }

    res.json({
      message: `成功上传 ${uploadedFiles.length} 张图片`,
      files: uploadedFiles,
      uploaded_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('批量图片上传失败:', error);
    
    // 清理上传的文件
    if (req.files) {
      for (const file of req.files) {
        try {
          await fs.unlink(file.path);
        } catch (unlinkError) {
          console.error('清理文件失败:', unlinkError);
        }
      }
    }

    res.status(500).json({
      error: '批量图片上传失败'
    });
  }
});

// 上传头像
router.post('/avatar', upload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: '没有上传头像文件'
      });
    }

    // 头像固定处理为正方形，200x200像素
    const filePath = await processImage(req.file.path, {
      width: 200,
      height: 200,
      quality: 90,
      format: 'jpeg'
    });

    // 获取文件信息
    const stats = await fs.stat(filePath);
    const fileUrl = `/uploads/${path.basename(filePath)}`;

    // 记录上传日志
    await logFileUpload(
      req.userId,
      req.file.originalname,
      stats.size,
      'avatar',
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '头像上传成功',
      avatar: {
        filename: path.basename(filePath),
        url: fileUrl,
        size: stats.size,
        uploaded_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('头像上传失败:', error);
    
    // 清理上传的文件
    if (req.file && req.file.path) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('清理文件失败:', unlinkError);
      }
    }

    res.status(500).json({
      error: '头像上传失败'
    });
  }
});

// 删除文件
router.delete('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    
    // 安全检查：防止路径遍历攻击
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        error: '无效的文件名'
      });
    }

    const filePath = path.join(__dirname, '../../uploads', filename);
    
    try {
      await fs.access(filePath);
      await fs.unlink(filePath);
      
      res.json({
        message: '文件删除成功'
      });
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({
          error: '文件不存在'
        });
      }
      throw error;
    }

  } catch (error) {
    console.error('删除文件失败:', error);
    res.status(500).json({
      error: '删除文件失败'
    });
  }
});

// 获取文件信息
router.get('/info/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    
    // 安全检查
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        error: '无效的文件名'
      });
    }

    const filePath = path.join(__dirname, '../../uploads', filename);
    
    try {
      const stats = await fs.stat(filePath);
      
      res.json({
        filename,
        size: stats.size,
        created_at: stats.birthtime,
        modified_at: stats.mtime,
        url: `/uploads/${filename}`
      });
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        return res.status(404).json({
          error: '文件不存在'
        });
      }
      throw error;
    }

  } catch (error) {
    console.error('获取文件信息失败:', error);
    res.status(500).json({
      error: '获取文件信息失败'
    });
  }
});

// 获取上传统计
router.get('/stats/overview', async (req, res) => {
  try {
    const uploadsDir = path.join(__dirname, '../../uploads');
    
    try {
      const files = await fs.readdir(uploadsDir);
      let totalSize = 0;
      let totalFiles = 0;

      for (const file of files) {
        try {
          const filePath = path.join(uploadsDir, file);
          const stats = await fs.stat(filePath);
          if (stats.isFile()) {
            totalSize += stats.size;
            totalFiles++;
          }
        } catch (error) {
          // 忽略单个文件的错误
        }
      }

      res.json({
        total_files: totalFiles,
        total_size: totalSize,
        total_size_mb: (totalSize / 1024 / 1024).toFixed(2),
        upload_dir: uploadsDir
      });

    } catch (error) {
      if (error.code === 'ENOENT') {
        res.json({
          total_files: 0,
          total_size: 0,
          total_size_mb: '0.00',
          upload_dir: uploadsDir
        });
      } else {
        throw error;
      }
    }

  } catch (error) {
    console.error('获取上传统计失败:', error);
    res.status(500).json({
      error: '获取上传统计失败'
    });
  }
});

// 错误处理中间件
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: '文件大小超出限制'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        error: '文件数量超出限制'
      });
    }
  }
  
  res.status(500).json({
    error: error.message || '文件上传失败'
  });
});

module.exports = router;
