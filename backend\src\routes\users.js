const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { User, Inspection, SystemLog, sequelize } = require('../models');
const { authenticateToken, requireAdmin, requireSelfOrAdmin } = require('../middleware/auth');
const { logActivity } = require('../utils/logger');

const router = express.Router();

// 所有用户路由都需要认证
router.use(authenticateToken);

// 获取用户列表（管理员权限）
router.get('/', requireAdmin, [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('search').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('搜索关键词长度不能超过100'),
  query('role').optional({ checkFalsy: true }).isIn(['admin', 'manager', 'supervisor', 'inspector']).withMessage('角色值无效'),
  query('status').optional({ checkFalsy: true }).isIn(['active', 'inactive']).withMessage('状态值无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      role,
      status,
      department
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // 搜索条件
    if (search) {
      whereClause[sequelize.Sequelize.Op.or] = [
        { username: { [sequelize.Sequelize.Op.like]: `%${search}%` } },
        { display_name: { [sequelize.Sequelize.Op.like]: `%${search}%` } },
        { real_name: { [sequelize.Sequelize.Op.like]: `%${search}%` } },
        { email: { [sequelize.Sequelize.Op.like]: `%${search}%` } }
      ];
    }

    // 筛选条件
    if (role) whereClause.role = role;
    if (status) whereClause.status = status;
    if (department) whereClause.department = department;

    const { rows: users, count: total } = await User.findAndCountAll({
      where: whereClause,
      attributes: { exclude: ['password'] },
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      data: users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      error: '获取用户列表失败'
    });
  }
});

// 获取用户详情
router.get('/:id', requireSelfOrAdmin(), async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    
    const user = await User.findByPk(userId, {
      attributes: { exclude: ['password'] },
      include: [
        {
          model: Inspection,
          as: 'inspections',
          limit: 10,
          order: [['inspection_time', 'DESC']],
          attributes: ['id', 'device_id', 'inspection_type', 'inspection_status', 'inspection_time']
        }
      ]
    });

    if (!user) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }

    // 获取用户统计数据
    const stats = await Inspection.getStatistics({ userId });

    res.json({
      user: user.toJSON(),
      stats
    });

  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({
      error: '获取用户详情失败'
    });
  }
});

// 创建用户（管理员权限）
router.post('/', requireAdmin, [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .isAlphanumeric()
    .withMessage('用户名只能包含字母和数字'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确'),
  body('role')
    .isIn(['admin', 'manager', 'supervisor', 'inspector'])
    .withMessage('角色值无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const {
      username,
      password,
      display_name,
      real_name,
      employee_id,
      email,
      phone,
      department,
      role
    } = req.body;

    // 检查用户名是否已存在
    const existingUser = await User.findOne({ where: { username } });
    if (existingUser) {
      return res.status(400).json({
        error: '用户名已存在'
      });
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(400).json({
          error: '邮箱已被使用'
        });
      }
    }

    // 创建用户
    const user = await User.create({
      username,
      password,
      display_name,
      real_name,
      employee_id,
      email,
      phone,
      department,
      role
    });

    // 记录日志
    await logActivity(
      req.userId,
      'create_user',
      'user',
      user.id.toString(),
      `创建用户: ${username}`,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: '用户创建成功',
      user: user.toSafeObject()
    });

  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({
      error: '创建用户失败'
    });
  }
});

// 更新用户信息
router.put('/:id', requireSelfOrAdmin(), [
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确'),
  body('role')
    .optional()
    .isIn(['admin', 'manager', 'supervisor', 'inspector'])
    .withMessage('角色值无效'),
  body('status')
    .optional()
    .isIn(['active', 'inactive'])
    .withMessage('状态值无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const userId = parseInt(req.params.id);
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }

    const {
      display_name,
      real_name,
      employee_id,
      email,
      phone,
      department,
      role,
      status,
      avatar
    } = req.body;

    // 非管理员不能修改角色和状态
    const updateData = {
      display_name,
      real_name,
      employee_id,
      email,
      phone,
      department,
      avatar
    };

    if (req.user.role === 'admin') {
      if (role !== undefined) updateData.role = role;
      if (status !== undefined) updateData.status = status;
    }

    // 检查邮箱是否已被其他用户使用
    if (email && email !== user.email) {
      const existingEmail = await User.findOne({
        where: {
          email,
          id: { [sequelize.Sequelize.Op.ne]: userId }
        }
      });
      if (existingEmail) {
        return res.status(400).json({
          error: '邮箱已被其他用户使用'
        });
      }
    }

    await user.update(updateData);

    // 记录日志
    await logActivity(
      req.userId,
      'update_user',
      'user',
      userId.toString(),
      `更新用户信息: ${user.username}`,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '用户信息更新成功',
      user: user.toSafeObject()
    });

  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      error: '更新用户信息失败'
    });
  }
});

// 删除用户（管理员权限）
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    
    // 不能删除自己
    if (userId === req.userId) {
      return res.status(400).json({
        error: '不能删除自己的账户'
      });
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }

    await user.destroy();

    // 记录日志
    await logActivity(
      req.userId,
      'delete_user',
      'user',
      userId.toString(),
      `删除用户: ${user.username}`,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      error: '删除用户失败'
    });
  }
});

// 获取用户统计信息（管理员权限）
router.get('/stats/overview', requireAdmin, async (req, res) => {
  try {
    const stats = await User.getStatistics();
    res.json(stats);
  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json({
      error: '获取用户统计失败'
    });
  }
});

// 修改密码
router.put('/:id/password', requireSelfOrAdmin(), [
  body('currentPassword')
    .if((value, { req }) => req.params.id == req.userId)
    .notEmpty()
    .withMessage('当前密码不能为空'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('新密码长度至少6个字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        details: errors.array()
      });
    }

    const userId = parseInt(req.params.id);
    const { currentPassword, newPassword } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }

    // 如果是修改自己的密码，需要验证当前密码
    if (userId === req.userId) {
      const isValidPassword = await user.validatePassword(currentPassword);
      if (!isValidPassword) {
        return res.status(400).json({
          error: '当前密码错误'
        });
      }
    }

    // 更新密码
    await user.update({ password: newPassword });

    // 记录日志
    await logActivity(
      req.userId,
      'change_password',
      'user',
      userId.toString(),
      `修改密码: ${user.username}`,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: '密码修改成功'
    });

  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({
      error: '修改密码失败'
    });
  }
});

module.exports = router;
