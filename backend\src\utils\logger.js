const { SystemLog } = require('../models');

/**
 * 记录系统活动日志
 * @param {number} userId - 用户ID
 * @param {string} action - 操作类型
 * @param {string} targetType - 目标类型
 * @param {string} targetId - 目标ID
 * @param {string} description - 操作描述
 * @param {string} ipAddress - IP地址
 * @param {string} userAgent - 用户代理
 * @returns {Promise<Object|null>} 日志记录
 */
const logActivity = async (userId, action, targetType, targetId, description, ipAddress, userAgent) => {
  try {
    return await SystemLog.logActivity(
      userId,
      action,
      targetType,
      targetId,
      description,
      ipAddress,
      userAgent
    );
  } catch (error) {
    console.error('记录活动日志失败:', error);
    return null;
  }
};

/**
 * 记录用户登录日志
 * @param {number} userId - 用户ID
 * @param {string} ipAddress - IP地址
 * @param {string} userAgent - 用户代理
 * @param {boolean} success - 登录是否成功
 */
const logLogin = async (userId, ipAddress, userAgent, success = true) => {
  const action = success ? 'user_login' : 'user_login_failed';
  const description = success ? '用户登录成功' : '用户登录失败';
  
  return await logActivity(
    userId,
    action,
    'user',
    userId?.toString(),
    description,
    ipAddress,
    userAgent
  );
};

/**
 * 记录用户登出日志
 * @param {number} userId - 用户ID
 * @param {string} ipAddress - IP地址
 * @param {string} userAgent - 用户代理
 */
const logLogout = async (userId, ipAddress, userAgent) => {
  return await logActivity(
    userId,
    'user_logout',
    'user',
    userId?.toString(),
    '用户登出',
    ipAddress,
    userAgent
  );
};

/**
 * 记录巡检操作日志
 * @param {number} userId - 用户ID
 * @param {string} action - 操作类型 (create_inspection, update_inspection, delete_inspection)
 * @param {number} inspectionId - 巡检记录ID
 * @param {string} deviceId - 设备ID
 * @param {string} ipAddress - IP地址
 * @param {string} userAgent - 用户代理
 */
const logInspection = async (userId, action, inspectionId, deviceId, ipAddress, userAgent) => {
  const actionMap = {
    create_inspection: '创建巡检记录',
    update_inspection: '更新巡检记录',
    delete_inspection: '删除巡检记录'
  };
  
  const description = `${actionMap[action] || action} - 设备: ${deviceId}`;
  
  return await logActivity(
    userId,
    action,
    'inspection',
    inspectionId?.toString(),
    description,
    ipAddress,
    userAgent
  );
};

/**
 * 记录设备操作日志
 * @param {number} userId - 用户ID
 * @param {string} action - 操作类型
 * @param {string} deviceId - 设备ID
 * @param {string} ipAddress - IP地址
 * @param {string} userAgent - 用户代理
 */
const logDevice = async (userId, action, deviceId, ipAddress, userAgent) => {
  const actionMap = {
    create_device: '创建设备',
    update_device: '更新设备',
    delete_device: '删除设备'
  };
  
  const description = `${actionMap[action] || action} - 设备ID: ${deviceId}`;
  
  return await logActivity(
    userId,
    action,
    'device',
    deviceId,
    description,
    ipAddress,
    userAgent
  );
};

/**
 * 记录文件上传日志
 * @param {number} userId - 用户ID
 * @param {string} fileName - 文件名
 * @param {number} fileSize - 文件大小
 * @param {string} fileType - 文件类型
 * @param {string} ipAddress - IP地址
 * @param {string} userAgent - 用户代理
 */
const logFileUpload = async (userId, fileName, fileSize, fileType, ipAddress, userAgent) => {
  const description = `上传文件: ${fileName} (${(fileSize / 1024).toFixed(2)} KB, ${fileType})`;
  
  return await logActivity(
    userId,
    'file_upload',
    'file',
    fileName,
    description,
    ipAddress,
    userAgent
  );
};

/**
 * 记录系统配置变更日志
 * @param {number} userId - 用户ID
 * @param {string} configKey - 配置键
 * @param {string} oldValue - 旧值
 * @param {string} newValue - 新值
 * @param {string} ipAddress - IP地址
 * @param {string} userAgent - 用户代理
 */
const logConfigChange = async (userId, configKey, oldValue, newValue, ipAddress, userAgent) => {
  const description = `配置变更: ${configKey} (${oldValue} → ${newValue})`;
  
  return await logActivity(
    userId,
    'config_change',
    'config',
    configKey,
    description,
    ipAddress,
    userAgent
  );
};

/**
 * 记录数据导出日志
 * @param {number} userId - 用户ID
 * @param {string} exportType - 导出类型
 * @param {Object} filters - 导出筛选条件
 * @param {string} ipAddress - IP地址
 * @param {string} userAgent - 用户代理
 */
const logDataExport = async (userId, exportType, filters, ipAddress, userAgent) => {
  const description = `数据导出: ${exportType} - 筛选条件: ${JSON.stringify(filters)}`;
  
  return await logActivity(
    userId,
    'data_export',
    'export',
    exportType,
    description,
    ipAddress,
    userAgent
  );
};

module.exports = {
  logActivity,
  logLogin,
  logLogout,
  logInspection,
  logDevice,
  logFileUpload,
  logConfigChange,
  logDataExport
};
