const axios = require('axios');

async function testAdminAPI() {
  console.log('🧪 测试后台管理API...');
  
  try {
    // 1. 登录获取token
    console.log('\n1. 管理员登录...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 配置axios默认headers
    const apiClient = axios.create({
      baseURL: 'http://localhost:3001/api',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // 2. 测试用户API
    console.log('\n2. 测试用户API...');
    const usersResponse = await apiClient.get('/users');
    console.log('✅ 用户API正常:', {
      total: usersResponse.data.pagination?.total,
      count: usersResponse.data.data?.length
    });
    
    // 3. 测试设备API
    console.log('\n3. 测试设备API...');
    const devicesResponse = await apiClient.get('/devices');
    console.log('✅ 设备API正常:', {
      total: devicesResponse.data.pagination?.total,
      count: devicesResponse.data.data?.length
    });
    
    // 4. 测试巡检记录API
    console.log('\n4. 测试巡检记录API...');
    const inspectionsResponse = await apiClient.get('/inspections');
    console.log('✅ 巡检记录API正常:', {
      total: inspectionsResponse.data.pagination?.total,
      count: inspectionsResponse.data.data?.length
    });
    
    // 5. 测试统计API
    console.log('\n5. 测试统计API...');
    try {
      const statsResponse = await apiClient.get('/statistics/dashboard');
      console.log('✅ 统计API正常:', statsResponse.data.overview);
    } catch (error) {
      console.log('⚠️ 统计API有问题:', error.response?.data?.error || error.message);
    }
    
    console.log('\n🎉 后台管理API测试完成！');
    
  } catch (error) {
    console.error('\n❌ 后台管理API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testAdminAPI();
