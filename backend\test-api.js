const axios = require('axios');

async function testAPI() {
  console.log('🔍 测试API连接...');
  
  try {
    // 1. 测试健康检查
    console.log('\n1. 测试健康检查...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ 健康检查成功:', healthResponse.data);
    
    // 2. 测试登录
    console.log('\n2. 测试登录...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    console.log('✅ 登录成功:', {
      message: loginResponse.data.message,
      user: loginResponse.data.user.username,
      hasToken: !!loginResponse.data.token
    });
    
    const token = loginResponse.data.token;
    
    // 3. 测试获取用户列表
    console.log('\n3. 测试获取用户列表...');
    const usersResponse = await axios.get('http://localhost:3001/api/users', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ 获取用户列表成功:', {
      total: usersResponse.data.pagination?.total || 0,
      users: usersResponse.data.data?.length || 0
    });
    
    // 4. 测试获取设备列表
    console.log('\n4. 测试获取设备列表...');
    const devicesResponse = await axios.get('http://localhost:3001/api/devices', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ 获取设备列表成功:', {
      total: devicesResponse.data.pagination?.total || 0,
      devices: devicesResponse.data.data?.length || 0
    });
    
    // 5. 测试获取统计数据
    console.log('\n5. 测试获取统计数据...');
    const statsResponse = await axios.get('http://localhost:3001/api/statistics/dashboard', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('✅ 获取统计数据成功:', statsResponse.data.overview);
    
    console.log('\n🎉 所有API测试通过！系统对接成功！');
    
  } catch (error) {
    console.error('\n❌ API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testAPI();
