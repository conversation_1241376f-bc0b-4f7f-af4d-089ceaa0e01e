const axios = require('axios');

async function testBasicAPI() {
  console.log('🔍 测试基本API功能...');
  
  try {
    // 1. 测试健康检查
    console.log('\n1. 测试健康检查...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ 健康检查成功');
    
    // 2. 测试登录
    console.log('\n2. 测试登录...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    console.log('✅ 登录成功');
    
    const token = loginResponse.data.token;
    
    // 3. 测试获取用户列表
    console.log('\n3. 测试获取用户列表...');
    const usersResponse = await axios.get('http://localhost:3001/api/users', {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log(`✅ 获取用户列表成功: ${usersResponse.data.data.length} 个用户`);
    
    // 4. 测试获取设备列表
    console.log('\n4. 测试获取设备列表...');
    const devicesResponse = await axios.get('http://localhost:3001/api/devices', {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log(`✅ 获取设备列表成功: ${devicesResponse.data.data.length} 个设备`);
    
    // 5. 测试获取巡检记录
    console.log('\n5. 测试获取巡检记录...');
    const inspectionsResponse = await axios.get('http://localhost:3001/api/inspections', {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log(`✅ 获取巡检记录成功: ${inspectionsResponse.data.data.length} 条记录`);
    
    console.log('\n🎉 基本API功能测试通过！');
    console.log('\n📊 数据统计:');
    console.log(`- 用户数量: ${usersResponse.data.pagination.total}`);
    console.log(`- 设备数量: ${devicesResponse.data.pagination.total}`);
    console.log(`- 巡检记录: ${inspectionsResponse.data.pagination.total}`);
    
    console.log('\n✅ 系统对接成功！前后端可以正常通信！');
    
  } catch (error) {
    console.error('\n❌ API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testBasicAPI();
