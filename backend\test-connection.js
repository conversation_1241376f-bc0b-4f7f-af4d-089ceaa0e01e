const mysql = require('mysql2/promise');
require('dotenv').config();

async function testConnection() {
  console.log('🔄 正在测试宝塔数据库连接...');
  console.log('配置信息:');
  console.log(`- 主机: ${process.env.DB_HOST}`);
  console.log(`- 端口: ${process.env.DB_PORT}`);
  console.log(`- 数据库: ${process.env.DB_NAME}`);
  console.log(`- 用户: ${process.env.DB_USER}`);
  console.log(`- 密码: ${process.env.DB_PASSWORD ? '已设置' : '未设置'}`);
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
    
    console.log('✅ 数据库连接成功！');
    
    // 测试查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ 数据库查询测试成功！');
    
    await connection.end();
    console.log('✅ 连接已关闭');
    
  } catch (error) {
    console.log('❌ 数据库连接失败:');
    console.log(`错误代码: ${error.code}`);
    console.log(`错误信息: ${error.message}`);
  }
}

testConnection();
