const axios = require('axios');

async function testCorrectDevice() {
  console.log('🧪 测试正确的设备ID...');
  
  try {
    // 1. 登录获取token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 2. 使用正确的设备ID测试
    const correctData = {
      device_id: 'PIPE001', // 使用实际存在的设备ID
      inspection_type: 'triangle',
      inspection_status: 'normal',
      location: '',
      notes: '',
      abnormal_info: '',
      inspection_time: new Date().toISOString(),
      images: []
    };
    
    console.log('\n发送的数据:', JSON.stringify(correctData, null, 2));
    
    const response = await axios.post('http://localhost:3001/api/inspections', correctData, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ 成功创建巡检记录:', response.data.inspection.id);
    console.log('记录详情:', {
      id: response.data.inspection.id,
      device_id: response.data.inspection.device_id,
      type: response.data.inspection.inspection_type,
      status: response.data.inspection.inspection_status
    });
    
    console.log('\n🎉 测试成功！前端现在应该可以正常创建巡检记录了！');
    
  } catch (error) {
    console.error('\n❌ 测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testCorrectDevice();
