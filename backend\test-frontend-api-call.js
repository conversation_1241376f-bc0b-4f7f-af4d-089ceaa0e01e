const axios = require('axios');

// 模拟前端的API调用
async function testFrontendAPICall() {
  console.log('🧪 模拟前端API调用...');
  
  try {
    // 1. 模拟前端登录
    console.log('\n1. 模拟前端登录...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 前端登录成功');
    
    // 2. 模拟前端调用inspection API
    console.log('\n2. 模拟前端调用inspection API...');
    
    // 模拟前端发送的数据格式
    const frontendData = {
      device_id: 'PIPE001',
      inspection_type: 'triangle',
      inspection_status: 'normal',
      location: '',
      notes: '',
      abnormal_info: '',
      inspection_time: new Date().toISOString(),
      images: []
    };
    
    console.log('前端发送的数据:', JSON.stringify(frontendData, null, 2));
    
    // 使用前端相同的请求方式
    const response = await axios({
      method: 'POST',
      url: 'http://localhost:3001/api/inspections',
      data: frontendData,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      timeout: 10000
    });
    
    console.log('✅ 前端API调用成功!');
    console.log('响应数据:', {
      id: response.data.inspection.id,
      device_id: response.data.inspection.device_id,
      type: response.data.inspection.inspection_type,
      status: response.data.inspection.inspection_status
    });
    
    // 3. 验证后台管理能否看到
    console.log('\n3. 验证后台管理能否看到...');
    const inspectionsResponse = await axios.get('http://localhost:3001/api/inspections', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const newRecord = inspectionsResponse.data.data.find(i => i.id === response.data.inspection.id);
    if (newRecord) {
      console.log('✅ 后台管理可以看到新记录');
    } else {
      console.log('❌ 后台管理看不到新记录');
    }
    
    console.log('\n🎉 前端API调用测试完成！');
    console.log('✅ 前端现在应该可以正常上传数据了！');
    
  } catch (error) {
    console.error('\n❌ 前端API调用测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
      console.error('请求配置:', {
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers
      });
    } else {
      console.error('错误:', error.message);
    }
  }
}

testFrontendAPICall();
