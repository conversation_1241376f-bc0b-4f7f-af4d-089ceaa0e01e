const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testFrontendIntegration() {
  console.log('🧪 测试前端集成流程...');
  
  try {
    // 1. 模拟前端登录
    console.log('\n1. 模拟前端登录...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 前端登录成功');
    
    // 2. 模拟前端获取设备列表
    console.log('\n2. 模拟前端获取设备列表...');
    const devicesResponse = await axios.get('http://localhost:3001/api/devices', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const devices = devicesResponse.data.data;
    console.log('✅ 前端获取设备列表成功:', devices.length, '个设备');
    
    // 3. 模拟前端上传图片
    console.log('\n3. 模拟前端上传图片...');
    
    // 创建测试图片
    const testImageData = Buffer.from([
      0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
      0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
      0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
      0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
      0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
      0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
      0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
      0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
      0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
      0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
      0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
      0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x80, 0xFF, 0xD9
    ]);
    
    const uploadedImages = [];
    const imageTypes = ['left', 'center', 'right'];
    
    for (const type of imageTypes) {
      const testImagePath = path.join(__dirname, `test-${type}.jpg`);
      fs.writeFileSync(testImagePath, testImageData);
      
      const formData = new FormData();
      formData.append('image', fs.createReadStream(testImagePath));
      formData.append('type', type);
      
      const uploadResponse = await axios.post('http://localhost:3001/api/upload/image', formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...formData.getHeaders()
        }
      });
      
      uploadedImages.push({
        type: type,
        url: uploadResponse.data.file.url,
        name: uploadResponse.data.file.filename,
        size: uploadResponse.data.file.size
      });
      
      // 清理测试文件
      fs.unlinkSync(testImagePath);
    }
    
    console.log('✅ 前端图片上传成功:', uploadedImages.length, '张图片');
    
    // 4. 模拟前端创建巡检记录
    console.log('\n4. 模拟前端创建巡检记录...');
    
    const inspectionData = {
      device_id: devices[0].device_id,
      inspection_type: 'triangle',
      inspection_status: 'normal',
      location: '前端测试位置',
      notes: '前端集成测试记录',
      inspection_time: new Date().toISOString(),
      images: uploadedImages
    };
    
    const inspectionResponse = await axios.post('http://localhost:3001/api/inspections', inspectionData, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ 前端创建巡检记录成功:', inspectionResponse.data.inspection.id);
    
    // 5. 验证后台管理能否查看
    console.log('\n5. 验证后台管理能否查看...');
    
    const adminInspectionsResponse = await axios.get('http://localhost:3001/api/inspections', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const inspections = adminInspectionsResponse.data.data;
    const newInspection = inspections.find(i => i.id === inspectionResponse.data.inspection.id);
    
    if (newInspection) {
      console.log('✅ 后台管理可以查看新记录');
      console.log('记录详情:', {
        id: newInspection.id,
        device_id: newInspection.device_id,
        type: newInspection.inspection_type,
        status: newInspection.inspection_status,
        images: newInspection.images?.length || 0
      });
    } else {
      throw new Error('后台管理无法查看新记录');
    }
    
    // 6. 验证图片可以访问
    console.log('\n6. 验证图片可以访问...');
    
    if (newInspection.images && newInspection.images.length > 0) {
      for (const image of newInspection.images) {
        try {
          const imageResponse = await axios.get(`http://localhost:3001${image.image_url}`);
          console.log(`✅ 图片可访问: ${image.image_type} - ${imageResponse.headers['content-length']} bytes`);
        } catch (error) {
          console.log(`❌ 图片无法访问: ${image.image_type}`);
        }
      }
    }
    
    console.log('\n🎉 前端集成测试完成！');
    console.log('\n📊 测试结果总结:');
    console.log('- 前端登录: ✅');
    console.log('- 获取设备: ✅');
    console.log('- 图片上传: ✅');
    console.log('- 创建记录: ✅');
    console.log('- 后台查看: ✅');
    console.log('- 图片访问: ✅');
    console.log('\n✅ 前端到后端数据流转完全正常！');
    
  } catch (error) {
    console.error('\n❌ 前端集成测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testFrontendIntegration();
