const axios = require('axios');

async function testImageData() {
  console.log('🔍 测试图片数据格式...');
  
  try {
    // 1. 登录获取token
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 2. 获取巡检记录
    const inspectionsResponse = await axios.get('http://localhost:3001/api/inspections', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const inspections = inspectionsResponse.data.data;
    console.log(`\n📋 找到 ${inspections.length} 条巡检记录`);
    
    // 3. 查看有图片的记录
    const recordsWithImages = inspections.filter(record => record.images && record.images.length > 0);
    console.log(`\n🖼️ 有图片的记录: ${recordsWithImages.length} 条`);
    
    if (recordsWithImages.length > 0) {
      recordsWithImages.forEach((record, index) => {
        console.log(`\n记录 ${index + 1}:`);
        console.log(`- ID: ${record.id}`);
        console.log(`- 设备: ${record.device_id}`);
        console.log(`- 类型: ${record.inspection_type}`);
        console.log(`- 图片数量: ${record.images.length}`);
        
        record.images.forEach((image, imgIndex) => {
          console.log(`  图片 ${imgIndex + 1}:`);
          console.log(`  - 类型: ${image.image_type}`);
          console.log(`  - URL: ${image.image_url}`);
          console.log(`  - 文件名: ${image.image_name}`);
          console.log(`  - 大小: ${image.file_size} bytes`);
        });
      });
      
      // 4. 测试图片访问
      console.log('\n🔗 测试图片访问...');
      const firstImage = recordsWithImages[0].images[0];
      const imageUrl = `http://localhost:3001${firstImage.image_url}`;
      
      try {
        const imageResponse = await axios.get(imageUrl);
        console.log(`✅ 图片可访问: ${imageUrl}`);
        console.log(`- 状态码: ${imageResponse.status}`);
        console.log(`- 内容类型: ${imageResponse.headers['content-type']}`);
        console.log(`- 文件大小: ${imageResponse.headers['content-length']} bytes`);
      } catch (error) {
        console.log(`❌ 图片无法访问: ${imageUrl}`);
        console.log(`- 错误: ${error.message}`);
      }
      
      // 5. 模拟前端数据转换
      console.log('\n🔄 模拟前端数据转换...');
      const convertedData = recordsWithImages.map(inspection => ({
        id: inspection.id.toString(),
        deviceId: inspection.device_id,
        type: inspection.inspection_type === 'triangle' ? 'triangle-shot' : 'upward-shot',
        date: new Date(inspection.inspection_time).toLocaleDateString('zh-CN'),
        uploadTime: inspection.inspection_time,
        inspector: inspection.user?.display_name || inspection.user?.username || '未知',
        status: inspection.inspection_status === 'normal' ? '正常' : '异常',
        abnormalInfo: inspection.abnormal_info || '',
        imageUrl: inspection.images?.[0]?.image_url || '',
        photos: inspection.inspection_type === 'triangle' ? {
          left: inspection.images?.find(img => img.image_type === 'left')?.image_url || '',
          center: inspection.images?.find(img => img.image_type === 'center')?.image_url || '',
          right: inspection.images?.find(img => img.image_type === 'right')?.image_url || ''
        } : undefined
      }));
      
      console.log('转换后的数据:');
      convertedData.forEach((item, index) => {
        console.log(`\n项目 ${index + 1}:`);
        console.log(`- ID: ${item.id}`);
        console.log(`- 类型: ${item.type}`);
        console.log(`- 状态: ${item.status}`);
        console.log(`- 仰拍图片URL: ${item.imageUrl}`);
        if (item.photos) {
          console.log(`- 三角拍图片:`);
          console.log(`  - 左: ${item.photos.left}`);
          console.log(`  - 中: ${item.photos.center}`);
          console.log(`  - 右: ${item.photos.right}`);
        }
      });
      
    } else {
      console.log('\n⚠️ 没有找到包含图片的记录');
      console.log('请先在前端上传一些带图片的巡检记录');
    }
    
    console.log('\n🎉 图片数据测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testImageData();
