const axios = require('axios');

async function testInspectionAPI() {
  console.log('🧪 测试巡检记录API...');
  
  try {
    // 1. 登录获取token
    console.log('\n1. 登录系统...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 配置axios默认headers
    const apiClient = axios.create({
      baseURL: 'http://localhost:3001/api',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // 2. 获取设备列表
    console.log('\n2. 获取设备列表...');
    const devicesResponse = await apiClient.get('/devices');
    const devices = devicesResponse.data.data;
    console.log('✅ 设备列表获取成功:', devices.length, '个设备');
    
    if (devices.length === 0) {
      console.log('❌ 没有可用设备，无法创建巡检记录');
      return;
    }
    
    const testDevice = devices[0];
    console.log('使用测试设备:', testDevice.device_id);
    
    // 3. 创建三角拍巡检记录
    console.log('\n3. 创建三角拍巡检记录...');
    const triangleInspection = {
      device_id: testDevice.device_id,
      inspection_type: 'triangle',
      inspection_status: 'normal',
      location: '测试位置',
      notes: '前端API测试 - 三角拍',
      inspection_time: new Date().toISOString()
    };
    
    const triangleResponse = await apiClient.post('/inspections', triangleInspection);
    console.log('✅ 三角拍记录创建成功:', triangleResponse.data.inspection.id);
    
    // 4. 创建仰拍巡检记录
    console.log('\n4. 创建仰拍巡检记录...');
    const upwardInspection = {
      device_id: testDevice.device_id,
      inspection_type: 'upward',
      inspection_status: 'warning',
      location: '测试位置',
      notes: '前端API测试 - 仰拍',
      abnormal_info: '发现轻微异常',
      inspection_time: new Date().toISOString()
    };
    
    const upwardResponse = await apiClient.post('/inspections', upwardInspection);
    console.log('✅ 仰拍记录创建成功:', upwardResponse.data.inspection.id);
    
    // 5. 获取巡检记录列表
    console.log('\n5. 获取巡检记录列表...');
    const inspectionsResponse = await apiClient.get('/inspections');
    const inspections = inspectionsResponse.data.data;
    console.log('✅ 巡检记录列表获取成功:', inspections.length, '条记录');
    
    // 显示最新的几条记录
    const recentInspections = inspections.slice(0, 3);
    console.log('\n📋 最新巡检记录:');
    recentInspections.forEach((record, index) => {
      console.log(`${index + 1}. ${record.device_id} - ${record.inspection_type} - ${record.inspection_status} - ${record.inspection_time}`);
    });
    
    // 6. 测试查询参数
    console.log('\n6. 测试查询参数...');
    const filteredResponse = await apiClient.get('/inspections', {
      params: {
        device_id: testDevice.device_id,
        inspection_type: 'triangle'
      }
    });
    console.log('✅ 查询过滤正常:', filteredResponse.data.data.length, '条匹配记录');
    
    console.log('\n🎉 巡检记录API测试完成！');
    console.log('\n📊 测试结果总结:');
    console.log(`- 设备数量: ${devices.length}`);
    console.log(`- 巡检记录总数: ${inspections.length}`);
    console.log(`- 新创建记录: 2条`);
    console.log(`- API功能: 正常`);
    
  } catch (error) {
    console.error('\n❌ 巡检记录API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testInspectionAPI();
