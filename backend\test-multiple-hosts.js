const mysql = require('mysql2/promise');
require('dotenv').config();

async function testConnection(host, port) {
  console.log(`🔄 测试连接: ${host}:${port}`);
  
  try {
    const connection = await mysql.createConnection({
      host: host,
      port: port,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      connectTimeout: 5000
    });
    
    console.log(`✅ 连接成功: ${host}:${port}`);
    
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log(`✅ 查询测试成功: ${host}:${port}`);
    
    await connection.end();
    return true;
    
  } catch (error) {
    console.log(`❌ 连接失败: ${host}:${port} - ${error.code}: ${error.message}`);
    return false;
  }
}

async function testMultipleHosts() {
  console.log('🔍 正在测试多个可能的数据库地址...');
  console.log(`数据库: ${process.env.DB_NAME}`);
  console.log(`用户: ${process.env.DB_USER}`);
  console.log('');
  
  const hostsToTest = [
    { host: 'localhost', port: 3306 },
    { host: '127.0.0.1', port: 3306 },
    { host: 'localhost', port: 3307 },
    { host: '127.0.0.1', port: 3307 }
  ];
  
  for (const config of hostsToTest) {
    const success = await testConnection(config.host, config.port);
    if (success) {
      console.log('');
      console.log('🎉 找到可用的数据库连接！');
      console.log(`请在 .env 文件中设置:`);
      console.log(`DB_HOST=${config.host}`);
      console.log(`DB_PORT=${config.port}`);
      return;
    }
  }
  
  console.log('');
  console.log('❌ 未找到可用的本地数据库连接');
  console.log('💡 可能的原因:');
  console.log('1. 宝塔MySQL在远程服务器上');
  console.log('2. MySQL服务未启动');
  console.log('3. 需要不同的端口');
  console.log('4. 防火墙阻止连接');
}

testMultipleHosts();
