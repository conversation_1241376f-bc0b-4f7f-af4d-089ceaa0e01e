const axios = require('axios');

async function testRegister() {
  console.log('🧪 测试注册功能...');
  
  try {
    // 测试注册新用户
    const testUser = {
      username: `testuser_${Date.now()}`,
      password: 'password123',
      email: `test_${Date.now()}@example.com`,
      display_name: '测试用户'
    };
    
    console.log('\n1. 测试用户注册...');
    console.log('注册数据:', testUser);
    
    const response = await axios.post('http://localhost:3001/api/auth/register', testUser);
    
    console.log('✅ 注册成功:', response.data);
    
    // 测试用户名重复
    console.log('\n2. 测试用户名重复...');
    try {
      await axios.post('http://localhost:3001/api/auth/register', testUser);
      console.log('❌ 应该返回用户名已存在错误');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ 正确返回用户名已存在错误:', error.response.data.error);
      } else {
        console.log('❌ 意外错误:', error.response?.data || error.message);
      }
    }
    
    // 测试新注册用户登录
    console.log('\n3. 测试新用户登录...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: testUser.username,
      password: testUser.password
    });
    
    console.log('✅ 新用户登录成功:', {
      username: loginResponse.data.user.username,
      hasToken: !!loginResponse.data.token
    });
    
    console.log('\n🎉 注册功能测试完成！');
    
  } catch (error) {
    console.error('\n❌ 注册测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testRegister();
