const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testUploadAPI() {
  console.log('🧪 测试图片上传API...');
  
  try {
    // 1. 登录获取token
    console.log('\n1. 登录系统...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ 登录成功');
    
    // 2. 创建测试图片文件
    console.log('\n2. 创建测试图片...');
    const testImagePath = path.join(__dirname, 'test-image.jpg');
    
    // 创建一个简单的测试图片数据（1x1像素的JPEG）
    const testImageData = Buffer.from([
      0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
      0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
      0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
      0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
      0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
      0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
      0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
      0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
      0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
      0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
      0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
      0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x80, 0xFF, 0xD9
    ]);
    
    fs.writeFileSync(testImagePath, testImageData);
    console.log('✅ 测试图片创建成功');
    
    // 3. 测试单张图片上传
    console.log('\n3. 测试单张图片上传...');
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));
    formData.append('type', 'test');
    formData.append('compress', 'true');
    
    const uploadResponse = await axios.post('http://localhost:3001/api/upload/image', formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      }
    });
    
    console.log('✅ 图片上传成功:', uploadResponse.data);
    
    // 4. 验证上传的文件
    console.log('\n4. 验证上传的文件...');
    const uploadedFileUrl = uploadResponse.data.file.url;
    const fileCheckResponse = await axios.get(`http://localhost:3001${uploadedFileUrl}`);
    console.log('✅ 上传的文件可以访问，大小:', fileCheckResponse.headers['content-length'], 'bytes');
    
    // 5. 测试上传统计
    console.log('\n5. 测试上传统计...');
    const statsResponse = await axios.get('http://localhost:3001/api/upload/stats/overview', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ 上传统计:', statsResponse.data);
    
    // 6. 清理测试文件
    console.log('\n6. 清理测试文件...');
    fs.unlinkSync(testImagePath);
    console.log('✅ 测试文件清理完成');
    
    console.log('\n🎉 图片上传API测试完成！');
    console.log('\n📊 测试结果:');
    console.log('- 上传接口: ✅ 正常');
    console.log('- 文件存储: ✅ 正常');
    console.log('- 文件访问: ✅ 正常');
    console.log('- 统计功能: ✅ 正常');
    
    return {
      success: true,
      uploadedFile: uploadResponse.data.file
    };
    
  } catch (error) {
    console.error('\n❌ 图片上传API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('错误:', error.message);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

testUploadAPI();
