const axios = require('axios');

async function testUserIdFix() {
  console.log('🧪 测试用户ID修复...');
  
  try {
    // 1. 测试登录并检查返回的用户信息
    console.log('\n1. 测试登录API返回的用户信息...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('✅ 登录成功');
    console.log('返回的用户信息:');
    console.log('- ID:', loginResponse.data.user.id);
    console.log('- 用户名:', loginResponse.data.user.username);
    console.log('- 显示名称:', loginResponse.data.user.display_name);
    console.log('- 角色:', loginResponse.data.user.role);
    
    if (!loginResponse.data.user.id) {
      console.error('❌ 登录API没有返回用户ID！');
      return;
    }
    
    const token = loginResponse.data.token;
    const userId = loginResponse.data.user.id;
    
    // 2. 测试用户信息更新API
    console.log('\n2. 测试用户信息更新API...');
    const updateData = {
      display_name: '测试用户名',
      real_name: '测试真实姓名',
      email: '<EMAIL>'
    };
    
    console.log('更新数据:', updateData);
    console.log('用户ID:', userId);
    
    const updateResponse = await axios.put(`http://localhost:3001/api/users/${userId}`, updateData, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ 用户信息更新成功');
    console.log('更新后的用户信息:');
    console.log('- 显示名称:', updateResponse.data.user.display_name);
    console.log('- 真实姓名:', updateResponse.data.user.real_name);
    console.log('- 邮箱:', updateResponse.data.user.email);
    
    // 3. 验证更新是否生效
    console.log('\n3. 验证更新是否生效...');
    const verifyResponse = await axios.get('http://localhost:3001/api/auth/me', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('✅ 验证成功');
    console.log('当前用户信息:');
    console.log('- ID:', verifyResponse.data.user.id);
    console.log('- 显示名称:', verifyResponse.data.user.display_name);
    console.log('- 真实姓名:', verifyResponse.data.user.real_name);
    console.log('- 邮箱:', verifyResponse.data.user.email);
    
    console.log('\n🎉 用户ID修复测试完成！');
    console.log('\n📋 修复内容:');
    console.log('- ✅ 登录API现在返回用户ID');
    console.log('- ✅ 用户信息更新API正常工作');
    console.log('- ✅ 数据库同步正常');
    
    console.log('\n🎯 前端现在应该可以:');
    console.log('1. 登录时获取到用户ID');
    console.log('2. 编辑个人信息时正确传递用户ID');
    console.log('3. 成功保存个人信息到数据库');
    
  } catch (error) {
    console.error('\n❌ 测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
      console.error('请求URL:', error.config?.url);
    } else {
      console.error('错误:', error.message);
    }
  }
}

testUserIdFix();
