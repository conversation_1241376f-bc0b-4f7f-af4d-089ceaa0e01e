# 石化管巡 APP 后端接口文档

这是一个基于 node.js 的后端项目，我的数据库连接方式是：mongodb://root:<EMAIL>:27017

## 概述

本文档定义了石化管巡 APP 前端与后端交互所使用的 API 接口规范。后端服务基于 Node.js。

**统一说明：**

- **API Base URL**: `https://your_domain.com/api` (生产环境) 或 `http://localhost:3001/api` (开发环境)
- **请求头**:
  - `Content-Type: application/json` (除文件上传外，默认 JSON 格式)
  - `Authorization: Bearer <token>` (需要认证的接口，`token` 为用户登录后获取的 JWT 或其他认证凭证)
- **响应格式**:
  - 所有 API 响应均采用统一 JSON 格式：
    ```json
    {
      "code": 0, // 状态码：0表示成功，非0表示失败
      "msg": "操作成功", // 提示信息
      "data": {} // 业务数据，成功时返回，失败时为空或null
    }
    ```
  - **通用错误码示例**:
    - `code: 10001` - 参数校验失败
    - `code: 10002` - 认证失败/Token 无效
    - `code: 10003` - 权限不足
    - `code: -1` - 服务器内部错误

---

## 一、用户管理

### 1.1 用户注册

- **Endpoint**: `/user/register`
- **Method**: `POST`
- **Description**: 用户通过用户名和密码进行注册。
- **Request Body**:
  ```json
  {
    "username": "string", // 用户名，必填，唯一
    "password": "string" // 密码，必填，最小6位，后端需加密存储
  }
  ```
- **Response**:
  - **成功**: `HTTP 200 OK`
    ```json
    {
      "code": 0,
      "msg": "注册成功",
      "data": null
    }
    ```
  - **失败 (用户名已存在)**: `HTTP 200 OK`
    ```json
    {
      "code": 20001,
      "msg": "用户名已存在",
      "data": null
    }
    ```
  - **失败 (参数校验)**: `HTTP 400 Bad Request` 或 `HTTP 200 OK`
    ```json
    {
      "code": 10001,
      "msg": "密码长度不足6位",
      "data": null
    }
    ```

### 1.2 用户登录

- **Endpoint**: `/user/login`
- **Method**: `POST`
- **Description**: 用户通过用户名和密码进行登录，成功后返回认证 Token。
- **Request Body**:
  ```json
  {
    "username": "string", // 用户名，必填
    "password": "string" // 密码，必填
  }
  ```
- **Response**:
  - **成功**: `HTTP 200 OK`
    ```json
    {
      "code": 0,
      "msg": "登录成功",
      "data": {
        "token": "string", // JWT或其他认证Token，用于后续接口认证
        "userId": "string", // 用户ID
        "username": "string", // 用户名
        "role": "string", // 用户角色 (例如: "巡检员", "管理员")
        "avatar": "string" // 用户头像URL (可选)
      }
    }
    ```
  - **失败 (用户名或密码错误)**: `HTTP 200 OK`
    ```json
    {
      "code": 20002,
      "msg": "用户名或密码错误",
      "data": null
    }
    ```
  - **失败 (参数校验)**: `HTTP 400 Bad Request` 或 `HTTP 200 OK`
    ```json
    {
      "code": 10001,
      "msg": "用户名和密码不能为空",
      "data": null
    }
    ```

### 1.3 获取用户信息 (额外加入)

- **Endpoint**: `/user/info`
- **Method**: `GET`
- **Description**: 获取当前登录用户的详细信息。需要认证。
- **Request Headers**: `Authorization: Bearer <token>`
- **Response**:
  - **成功**: `HTTP 200 OK`
    ```json
    {
      "code": 0,
      "msg": "获取成功",
      "data": {
        "userId": "string",
        "username": "string",
        "phone": "string",
        "role": "string",
        "department": "string", // 部门
        "avatar": "string", // 头像URL
        "email": "string" // 邮箱
      }
    }
    ```
  - **失败 (未认证/Token 无效)**: `HTTP 401 Unauthorized` 或 `HTTP 200 OK`
    ```json
    {
      "code": 10002,
      "msg": "请先登录",
      "data": null
    }
    ```

### 1.4 修改密码 (额外加入)

- **Endpoint**: `/user/change_password`
- **Method**: `POST`
- **Description**: 修改当前登录用户的密码。需要认证。
- **Request Headers**: `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "oldPassword": "string", // 旧密码，必填
    "newPassword": "string" // 新密码，必填，最小6位，后端需加密存储
  }
  ```
- **Response**:
  - **成功**: `HTTP 200 OK`
    ```json
    {
      "code": 0,
      "msg": "密码修改成功",
      "data": null
    }
    ```
  - **失败 (旧密码不正确)**: `HTTP 200 OK`
    ```json
    {
      "code": 20003,
      "msg": "旧密码不正确",
      "data": null
    }
    ```
  - **失败 (参数校验)**: `HTTP 400 Bad Request` 或 `HTTP 200 OK`
    ```json
    {
      "code": 10001,
      "msg": "新密码长度不足6位",
      "data": null
    }
    ```

---

## 二、图片上传与巡检数据提交

### 2.1 图片上传

- **Endpoint**: `/upload/image`
- **Method**: `POST`
- **Description**: 上传单张图片（用于三角拍、仰拍等功能）。文件将存储到百度对象存储 BOS，后端返回图片可访问 URL。需要认证。
- **Request Headers**: `Content-Type: multipart/form-data`, `Authorization: Bearer <token>`
- **Request Body**:
  - `file`: 文件类型，必填，待上传的图片文件。
  - `deviceId`: `string`, 必填，关联的设备 ID (例如仰拍功能可能需要携带)。
  - `uploadType`: `string`, 可选，图片上传类型，例如 `triangle_left`, `triangle_center`, `triangle_right`, `upward_shot` 等，用于区分图片用途和存储路径。
- **Response**:
  - **成功**: `HTTP 200 OK`
    ```json
    {
      "code": 0,
      "msg": "图片上传成功",
      "data": {
        "imageUrl": "string" // 图片在BOS上的可访问URL
      }
    }
    ```
  - **失败 (文件过大/格式错误)**: `HTTP 400 Bad Request` 或 `HTTP 200 OK`
    ```json
    {
      "code": 30001,
      "msg": "文件格式不正确或大小超过限制",
      "data": null
    }
    ```
  - **失败 (上传 BOS 失败)**: `HTTP 500 Internal Server Error` 或 `HTTP 200 OK`
    ```json
    {
      "code": -1,
      "msg": "图片上传到存储服务失败",
      "data": null
    }
    ```

### 2.2 提交三角拍巡检数据

- **Endpoint**: `/inspection/triangle_submit`
- **Method**: `POST`
- **Description**: 提交三角拍的巡检数据和异常信息。需要认证。
- **Request Headers**: `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "deviceId": "string", // 设备ID，必填
    "leftImageUrl": "string", // 左侧图片URL，必填（来自图片上传接口的返回）
    "centerImageUrl": "string", // 中间图片URL，必填
    "rightImageUrl": "string", // 右侧图片URL，必填
    "abnormalInfo": "string", // 异常情况描述，可选
    "inspectorId": "string" // 巡检人员ID（后端可从token中获取，也可前端传递）
  }
  ```
- **Response**:
  - **成功**: `HTTP 200 OK`
    ```json
    {
      "code": 0,
      "msg": "三角拍数据提交成功",
      "data": null
    }
    ```

### 2.3 提交仰拍巡检数据

- **Endpoint**: `/inspection/upward_submit`
- **Method**: `POST`
- **Description**: 提交仰拍的巡检数据和异常信息。需要认证。
- **Request Headers**: `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "deviceId": "string", // 设备ID，必填
    "imageUrl": "string", // 仰拍图片URL，必填（来自图片上传接口的返回）
    "abnormalInfo": "string", // 异常情况描述，可选
    "inspectorId": "string" // 巡检人员ID
  }
  ```
- **Response**:
  - **成功**: `HTTP 200 OK`
    ```json
    {
      "code": 0,
      "msg": "仰拍数据提交成功",
      "data": null
    }
    ```
