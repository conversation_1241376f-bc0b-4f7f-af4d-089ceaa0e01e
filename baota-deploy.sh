#!/bin/bash

# 宝塔面板一键部署脚本 - 石化管道巡检系统
echo "🎛️ 宝塔面板部署 - 石化管道巡检系统"
echo "========================================"

# 检查是否在宝塔环境
if [ ! -d "/www/server" ]; then
    echo "❌ 未检测到宝塔面板环境"
    echo "请先安装宝塔面板: https://www.bt.cn/new/download.html"
    exit 1
fi

echo "✅ 检测到宝塔面板环境"

# 设置变量
PROJECT_DIR="/www/wwwroot/pipeline-backend"
FRONTEND_DIR="/www/wwwroot/pipeline-frontend"
ADMIN_DIR="/www/wwwroot/pipeline-admin"

# 创建项目目录
echo "📁 创建项目目录..."
mkdir -p $PROJECT_DIR
mkdir -p $FRONTEND_DIR
mkdir -p $ADMIN_DIR

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装"
    echo "请在宝塔面板的软件商店中安装 Node.js版本管理器"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"

# 检查PM2
if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2未安装"
    echo "请在宝塔面板的软件商店中安装 PM2管理器"
    exit 1
fi

echo "✅ PM2已安装"

# 克隆或复制项目代码
echo "📥 部署后端代码..."
if [ -d ".git" ]; then
    # 如果是Git仓库，复制到目标目录
    cp -r . $PROJECT_DIR/
else
    echo "请将项目代码上传到 $PROJECT_DIR"
    echo "或者使用Git克隆: git clone <your-repo> $PROJECT_DIR"
    exit 1
fi

# 进入后端目录
cd $PROJECT_DIR/backend

# 安装后端依赖
echo "📦 安装后端依赖..."
npm install --production

# 创建必要目录
echo "📁 创建必要目录..."
mkdir -p uploads logs

# 设置目录权限
echo "🔐 设置目录权限..."
chown -R www:www $PROJECT_DIR
chmod -R 755 $PROJECT_DIR
chmod -R 777 uploads
chmod -R 777 logs

# 创建环境变量文件
echo "⚙️ 创建环境配置..."
cat > .env << EOF
NODE_ENV=production
PORT=3001

# 数据库配置（请根据实际情况修改）
DB_HOST=localhost
DB_PORT=3306
DB_NAME=pipeline_inspection
DB_USER=pipeline_user
DB_PASS=your-database-password

# JWT密钥（请修改为随机字符串）
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# 文件上传路径
UPLOAD_PATH=$PROJECT_DIR/backend/uploads
EOF

echo "⚠️  请编辑 $PROJECT_DIR/backend/.env 文件，修改数据库密码和JWT密钥"

# 复制PM2配置
cp baota-pm2.config.js pm2.config.js

# 测试后端启动
echo "🧪 测试后端服务..."
node src/app.js &
APP_PID=$!
sleep 3

if kill -0 $APP_PID 2>/dev/null; then
    echo "✅ 后端服务测试成功"
    kill $APP_PID
else
    echo "❌ 后端服务启动失败，请检查配置"
    exit 1
fi

# 构建前端（如果存在）
if [ -f "../../package.json" ]; then
    echo "🔨 构建前端应用..."
    cd ../..
    
    if [ -f "package.json" ]; then
        npm install
        npm run build:h5
        
        if [ -d "dist/build/h5" ]; then
            cp -r dist/build/h5/* $FRONTEND_DIR/
            echo "✅ 前端部署完成"
        fi
    fi
fi

# 构建后台管理（如果存在）
if [ -d "admin" ]; then
    echo "🔨 构建后台管理..."
    cd admin
    
    if [ -f "package.json" ]; then
        npm install
        npm run build
        
        if [ -d "dist" ]; then
            cp -r dist/* $ADMIN_DIR/
            echo "✅ 后台管理部署完成"
        fi
    fi
fi

# 创建Nginx配置模板
echo "🌐 创建Nginx配置模板..."
mkdir -p /tmp/pipeline-nginx

cat > /tmp/pipeline-nginx/frontend.conf << 'EOF'
server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;
    
    root /www/wwwroot/pipeline-frontend;
    index index.html;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传代理
    location /uploads/ {
        proxy_pass http://127.0.0.1:3001/uploads/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

cat > /tmp/pipeline-nginx/admin.conf << 'EOF'
server {
    listen 80;
    listen 443 ssl http2;
    server_name admin.your-domain.com;
    
    root /www/wwwroot/pipeline-admin;
    index index.html;
    
    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

echo "📋 部署完成！"
echo ""
echo "🎉 接下来请在宝塔面板中完成以下步骤："
echo ""
echo "1. 📊 数据库配置："
echo "   - 在宝塔面板创建数据库: pipeline_inspection"
echo "   - 导入SQL文件: $PROJECT_DIR/backend/baota-database-config.sql"
echo ""
echo "2. 🚀 启动后端服务："
echo "   - 打开PM2管理器"
echo "   - 添加项目: $PROJECT_DIR/backend"
echo "   - 启动文件: src/app.js"
echo ""
echo "3. 🌐 配置网站："
echo "   - 添加网站: your-domain.com -> $FRONTEND_DIR"
echo "   - 添加网站: admin.your-domain.com -> $ADMIN_DIR"
echo "   - 复制Nginx配置: /tmp/pipeline-nginx/"
echo ""
echo "4. 🔒 配置SSL证书："
echo "   - 申请Let's Encrypt证书"
echo "   - 开启强制HTTPS"
echo ""
echo "5. 🔧 修改配置："
echo "   - 编辑 $PROJECT_DIR/backend/.env"
echo "   - 修改数据库密码和JWT密钥"
echo ""
echo "6. 🧪 测试访问："
echo "   - 前端: https://your-domain.com"
echo "   - 后台: https://admin.your-domain.com"
echo "   - API: https://your-domain.com/api/health"
echo ""
echo "📖 详细部署指南请查看: BAOTA_DEPLOYMENT.md"
echo ""
echo "🎊 部署脚本执行完成！"
