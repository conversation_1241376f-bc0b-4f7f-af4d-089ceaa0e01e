#!/bin/bash

# 石化管道巡检系统部署脚本
echo "🚀 开始部署石化管道巡检系统..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p uploads logs nginx/ssl

# 检查SSL证书
if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/key.pem" ]; then
    echo "⚠️  SSL证书不存在，生成自签名证书..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout nginx/ssl/key.pem \
        -out nginx/ssl/cert.pem \
        -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 清理旧镜像（可选）
read -p "是否清理旧的Docker镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
fi

# 构建和启动服务
echo "🔨 构建和启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查服务健康状态
echo "🏥 检查服务健康状态..."

# 检查后端API
if curl -f http://localhost:3001/api/health > /dev/null 2>&1; then
    echo "✅ 后端API服务正常"
else
    echo "❌ 后端API服务异常"
fi

# 检查前端
if curl -f http://localhost:80 > /dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
fi

# 检查后台管理
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ 后台管理服务正常"
else
    echo "❌ 后台管理服务异常"
fi

# 显示访问信息
echo ""
echo "🎉 部署完成！"
echo ""
echo "📱 前端应用访问地址:"
echo "   HTTP:  http://localhost"
echo "   HTTPS: https://localhost"
echo ""
echo "🖥️  后台管理访问地址:"
echo "   HTTP:  http://localhost:8080"
echo "   HTTPS: https://localhost:8443"
echo ""
echo "🔧 API接口地址:"
echo "   HTTP:  http://localhost:3001/api"
echo ""
echo "📊 服务监控:"
echo "   查看日志: docker-compose logs -f"
echo "   重启服务: docker-compose restart"
echo "   停止服务: docker-compose down"
echo ""
echo "⚠️  注意事项:"
echo "   1. 首次部署请修改默认密码"
echo "   2. 生产环境请使用真实的SSL证书"
echo "   3. 定期备份数据库数据"
echo ""
