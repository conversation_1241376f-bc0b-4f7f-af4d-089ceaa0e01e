version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: pipeline-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: pipeline_inspection
      MYSQL_USER: pipeline_user
      MYSQL_PASSWORD: pipeline123456
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - pipeline-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pipeline-backend
    restart: always
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: pipeline_inspection
      DB_USER: pipeline_user
      DB_PASS: pipeline123456
      JWT_SECRET: your-production-jwt-secret-key-very-long-and-secure
    ports:
      - "3001:3001"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - mysql
    networks:
      - pipeline-network

  # 前端应用
  frontend:
    build:
      context: .
      dockerfile: frontend.Dockerfile
    container_name: pipeline-frontend
    restart: always
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - pipeline-network

  # 后台管理系统
  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    container_name: pipeline-admin
    restart: always
    ports:
      - "8080:80"
    depends_on:
      - backend
    networks:
      - pipeline-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: pipeline-nginx
    restart: always
    ports:
      - "443:443"
      - "8443:8443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - admin
      - backend
    networks:
      - pipeline-network

volumes:
  mysql_data:

networks:
  pipeline-network:
    driver: bridge
