<script>
export default {
  onLaunch: function () {
    console.log('App Launch')

    // 检测当前环境
    this.checkEnvironment();

    // 检查网络和数据模式
    this.checkDataMode();

    // 开发环境下添加页面导航调试工具
    if (process.env.NODE_ENV === 'development') {
      this.addDevNavTool();
    }
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
  methods: {
    checkEnvironment() {
      // 判断当前运行环境
      try {
        // 判断是否为H5环境
        const isH5 = process.env.UNI_PLATFORM === 'h5';
        console.log('当前环境:', isH5 ? 'H5' : '非H5环境');

        if (isH5) {
          // H5环境可能需要特殊处理
          console.log('H5环境下使用window.location进行路由跳转');
        } else {
          console.log('非H5环境下使用uni API进行路由跳转');
        }
        
        // 保存环境信息到全局
        getApp().globalData = getApp().globalData || {};
        getApp().globalData.isH5 = isH5;
      } catch (error) {
        console.error('环境检测失败:', error);
      }
    },
    async checkDataMode() {
      try {
        // 导入配置
        const { useMockData, checkNetworkStatus } = await import('@/config/index.js');

        const isMockMode = useMockData();
        console.log('数据模式:', isMockMode ? '模拟数据模式' : '真实API模式');

        if (isMockMode) {
          console.log('✅ 当前使用模拟数据，无需连接服务器');
          // 在开发环境下显示提示
          if (process.env.NODE_ENV === 'development') {
            setTimeout(() => {
              uni.showToast({
                title: '模拟数据模式',
                icon: 'none',
                duration: 2000
              });
            }, 1000);
          }
        } else {
          // 检查网络状态
          const networkStatus = await checkNetworkStatus();
          console.log('网络状态:', networkStatus);

          if (!networkStatus.isConnected) {
            uni.showModal({
              title: '网络连接失败',
              content: '无法连接到服务器，请检查网络设置',
              showCancel: false
            });
          }
        }

        // 保存数据模式到全局
        getApp().globalData = getApp().globalData || {};
        getApp().globalData.useMockData = isMockMode;
      } catch (error) {
        console.error('检查数据模式失败:', error);
      }
    },
    addDevNavTool() {
      // 仅在H5环境和开发模式下添加导航工具
      if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
        setTimeout(() => {
          // 创建导航面板
          const navPanel = document.createElement('div');
          navPanel.style.position = 'fixed';
          navPanel.style.bottom = '20px';
          navPanel.style.right = '20px';
          navPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
          navPanel.style.padding = '10px';
          navPanel.style.borderRadius = '5px';
          navPanel.style.zIndex = '9999';
          navPanel.style.color = 'white';
          navPanel.style.fontSize = '14px';
          
          // 添加标题
          const title = document.createElement('div');
          title.textContent = '页面导航';
          title.style.marginBottom = '10px';
          title.style.fontWeight = 'bold';
          navPanel.appendChild(title);
          
          // 添加页面链接
          const pages = [
            { name: '登录', path: '#/pages/login/index' },
            { name: '注册', path: '#/pages/register/index' },
            { name: '首页', path: '#/pages/home/<USER>' },
            { name: '历史', path: '#/pages/history/index' },
            { name: '分析', path: '#/pages/analysis/index' },
            { name: '个人', path: '#/pages/profile/index' }
          ];
          
          pages.forEach(page => {
            const link = document.createElement('div');
            link.textContent = page.name;
            link.style.padding = '5px 10px';
            link.style.cursor = 'pointer';
            link.style.borderRadius = '3px';
            link.style.marginBottom = '5px';
            link.style.backgroundColor = 'rgba(0, 122, 255, 0.7)';
            link.onclick = () => window.location.replace(page.path);
            navPanel.appendChild(link);
          });
          
          // 添加切换按钮
          const toggleBtn = document.createElement('div');
          toggleBtn.textContent = '隐藏';
          toggleBtn.style.padding = '5px 10px';
          toggleBtn.style.cursor = 'pointer'; 
          toggleBtn.style.borderRadius = '3px';
          toggleBtn.style.backgroundColor = 'rgba(255, 59, 48, 0.7)';
          
          // 隐藏/显示面板的逻辑
          let visible = true;
          toggleBtn.onclick = () => {
            if (visible) {
              for (let i = 0; i < navPanel.children.length; i++) {
                if (navPanel.children[i] !== toggleBtn) {
                  navPanel.children[i].style.display = 'none';
                }
              }
              toggleBtn.textContent = '显示';
              visible = false;
            } else {
              for (let i = 0; i < navPanel.children.length; i++) {
                navPanel.children[i].style.display = 'block';
              }
              toggleBtn.textContent = '隐藏';
              visible = true;
            }
          };
          
          navPanel.appendChild(toggleBtn);
          document.body.appendChild(navPanel);
        }, 1000); // 延迟加载确保DOM已经准备好
      }
    }
  }
}
</script>

<style>
/*每个页面公共css */
</style>
