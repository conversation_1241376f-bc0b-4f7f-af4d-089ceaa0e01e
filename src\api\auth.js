// 登录和注册相关的API接口
import { request } from '@/utils/request';

// 登录接口
export const login = async (data) => {
  try {
    const response = await request({
      url: '/auth/login',
      method: 'POST',
      data: {
        username: data.username,
        password: data.password
      }
    });

    // 转换后端响应格式为前端期望的格式
    if (response.token && response.user) {
      return {
        success: true,
        message: response.message || '登录成功',
        data: {
          token: response.token,
          user: {
            id: response.user.id, // 添加用户ID
            username: response.user.username,
            email: response.user.email,
            name: response.user.display_name || response.user.username,
            displayName: response.user.display_name || response.user.username,
            role: response.user.role,
            employeeId: response.user.employee_id,
            phone: response.user.phone,
            department: response.user.department,
            avatar: response.user.avatar || '/static/logo.png'
          }
        }
      };
    } else {
      return {
        success: false,
        message: response.message || '登录失败'
      };
    }
  } catch (error) {
    console.error('登录API错误:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '登录失败，请稍后重试'
    };
  }
};

// 注册接口
export const register = async (data) => {
  try {
    const response = await request({
      url: '/auth/register',
      method: 'POST',
      data: {
        username: data.username,
        password: data.password,
        email: data.email,
        display_name: data.displayName || data.username
      }
    });

    if (response.user) {
      return {
        success: true,
        message: response.message || '注册成功',
        data: {
          user: {
            username: response.user.username,
            email: response.user.email
          }
        }
      };
    } else {
      return {
        success: false,
        message: response.message || '注册失败'
      };
    }
  } catch (error) {
    console.error('注册API错误:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '注册失败，请稍后重试'
    };
  }
};

// 修改密码接口
export const changePassword = async (data) => {
  try {
    const response = await request({
      url: '/auth/change-password',
      method: 'POST',
      data: {
        currentPassword: data.oldPassword,
        newPassword: data.newPassword
      }
    });

    return {
      success: true,
      message: response.message || '密码修改成功'
    };
  } catch (error) {
    console.error('修改密码API错误:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '密码修改失败'
    };
  }
};