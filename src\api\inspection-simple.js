// 简化的巡检记录API - 用于测试
import { request } from '@/utils/request';

// 创建巡检记录（不包含图片）
export const createInspectionSimple = async (inspectionData) => {
  try {
    console.log('发送巡检记录数据:', inspectionData);
    
    const response = await request({
      url: '/inspections',
      method: 'POST',
      data: inspectionData
    });
    
    console.log('巡检记录创建响应:', response);
    
    return {
      success: true,
      message: '巡检记录创建成功',
      data: response.inspection || response
    };
  } catch (error) {
    console.error('创建巡检记录失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '创建巡检记录失败'
    };
  }
};

// 保存三角拍记录（简化版）
export const saveTriangleShotRecordSimple = async (recordData) => {
  try {
    console.log('保存三角拍记录（简化版）:', recordData);
    
    // 直接创建巡检记录，不上传图片
    const inspectionData = {
      device_id: recordData.deviceId || 'PIPE001',
      inspection_type: 'triangle',
      inspection_status: recordData.status === '正常' ? 'normal' : (recordData.status === '异常' ? 'abnormal' : 'warning'),
      location: recordData.location || '',
      notes: recordData.notes || '前端测试记录',
      abnormal_info: recordData.abnormalInfo || '',
      inspection_time: recordData.uploadTime || new Date().toISOString(),
      images: [] // 暂时不包含图片
    };
    
    console.log('发送的巡检数据:', inspectionData);
    
    const result = await createInspectionSimple(inspectionData);
    
    if (result.success) {
      return {
        success: true,
        message: '三角拍记录保存成功（简化版）',
        data: result.data
      };
    } else {
      throw new Error(result.message);
    }
    
  } catch (error) {
    console.error('保存三角拍记录失败:', error);
    return {
      success: false,
      message: error.message || '保存三角拍记录失败'
    };
  }
};

// 保存仰拍记录（简化版）
export const saveUpwardShotRecordSimple = async (recordData) => {
  try {
    console.log('保存仰拍记录（简化版）:', recordData);
    
    // 直接创建巡检记录，不上传图片
    const inspectionData = {
      device_id: recordData.deviceId || 'PIPE001',
      inspection_type: 'upward',
      inspection_status: recordData.status === '正常' ? 'normal' : (recordData.status === '异常' ? 'abnormal' : 'warning'),
      location: recordData.location || '',
      notes: recordData.notes || '前端测试记录',
      abnormal_info: recordData.abnormalInfo || '',
      inspection_time: recordData.uploadTime || new Date().toISOString(),
      images: [] // 暂时不包含图片
    };
    
    console.log('发送的巡检数据:', inspectionData);
    
    const result = await createInspectionSimple(inspectionData);
    
    if (result.success) {
      return {
        success: true,
        message: '仰拍记录保存成功（简化版）',
        data: result.data
      };
    } else {
      throw new Error(result.message);
    }
    
  } catch (error) {
    console.error('保存仰拍记录失败:', error);
    return {
      success: false,
      message: error.message || '保存仰拍记录失败'
    };
  }
};
