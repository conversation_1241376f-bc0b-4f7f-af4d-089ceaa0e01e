// 巡检记录相关的API接口
import { request } from '@/utils/request';

// 获取设备列表
export const getDeviceList = async (params = {}) => {
  try {
    const response = await request({
      url: '/devices',
      method: 'GET',
      data: params
    });
    
    return {
      success: true,
      message: '获取设备列表成功',
      data: {
        devices: response.data || [],
        pagination: response.pagination || {}
      }
    };
  } catch (error) {
    console.error('获取设备列表失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '获取设备列表失败'
    };
  }
};

// 创建巡检记录
export const createInspection = async (inspectionData) => {
  try {
    const response = await request({
      url: '/inspections',
      method: 'POST',
      data: inspectionData
    });
    
    return {
      success: true,
      message: '巡检记录创建成功',
      data: response.inspection || response
    };
  } catch (error) {
    console.error('创建巡检记录失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '创建巡检记录失败'
    };
  }
};

// 获取巡检记录列表
export const getInspectionList = async (params = {}) => {
  try {
    const response = await request({
      url: '/inspections',
      method: 'GET',
      data: params
    });
    
    return {
      success: true,
      message: '获取巡检记录成功',
      data: {
        inspections: response.data || [],
        pagination: response.pagination || {}
      }
    };
  } catch (error) {
    console.error('获取巡检记录失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '获取巡检记录失败'
    };
  }
};

// 上传图片
export const uploadImage = async (filePath, imageType = 'single') => {
  try {
    console.log('开始上传图片:', { filePath, imageType });

    // 使用uni.uploadFile上传图片
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: 'http://localhost:3001/api/upload/image',
        filePath: filePath,
        name: 'image',
        formData: {
          type: imageType,
          compress: 'true'
        },
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('token')}`
        },
        success: (uploadRes) => {
          try {
            console.log('图片上传成功响应:', uploadRes);
            const data = JSON.parse(uploadRes.data);
            console.log('解析后的响应数据:', data);

            if (data.file) {
              console.log('图片上传成功:', data.file);
              resolve({
                success: true,
                message: '图片上传成功',
                data: {
                  url: data.file.url,
                  filename: data.file.filename,
                  size: data.file.size
                }
              });
            } else {
              console.error('上传响应格式错误:', data);
              reject(new Error('上传响应格式错误'));
            }
          } catch (error) {
            console.error('解析上传响应失败:', error, uploadRes);
            reject(new Error('解析上传响应失败'));
          }
        },
        fail: (error) => {
          console.error('图片上传失败:', error);
          console.error('上传失败详情:', {
            filePath,
            imageType,
            url: 'http://localhost:3001/api/upload/image',
            error: error
          });
          reject(new Error(error.errMsg || '图片上传失败'));
        }
      });
    });
  } catch (error) {
    console.error('图片上传失败:', error);
    return {
      success: false,
      message: error.message || '图片上传失败'
    };
  }
};

// 保存三角拍记录
export const saveTriangleShotRecord = async (recordData) => {
  try {
    console.log('开始保存三角拍记录:', recordData);

    // 上传三张图片
    const uploadPromises = [];
    const imageTypes = ['left', 'center', 'right'];
    const images = [];

    console.log('准备上传图片:', {
      left: recordData.photos?.left ? '有图片' : '无图片',
      center: recordData.photos?.center ? '有图片' : '无图片',
      right: recordData.photos?.right ? '有图片' : '无图片'
    });
    
    for (let i = 0; i < imageTypes.length; i++) {
      const type = imageTypes[i];
      const imagePath = recordData.photos[type];
      
      if (imagePath) {
        uploadPromises.push(
          uploadImage(imagePath, type).then(result => {
            if (result.success) {
              images.push({
                type: type,
                url: result.data.url,
                name: result.data.filename,
                size: result.data.size
              });
            }
            return result;
          })
        );
      }
    }
    
    // 等待所有图片上传完成
    console.log('等待图片上传完成...');
    const uploadResults = await Promise.all(uploadPromises);
    console.log('图片上传结果:', uploadResults);

    const failedUploads = uploadResults.filter(result => !result.success);

    if (failedUploads.length > 0) {
      console.error('图片上传失败:', failedUploads);
      throw new Error(`${failedUploads.length} 张图片上传失败`);
    }

    console.log('所有图片上传成功，图片信息:', images);
    
    // 创建巡检记录
    const inspectionData = {
      device_id: recordData.deviceId || 'PIPE001',
      inspection_type: 'triangle',
      inspection_status: recordData.status === '正常' ? 'normal' : (recordData.status === '异常' ? 'abnormal' : 'warning'),
      location: recordData.location || '',
      notes: recordData.notes || '',
      abnormal_info: recordData.abnormalInfo || '',
      inspection_time: recordData.uploadTime || new Date().toISOString(),
      images: images
    };
    
    const result = await createInspection(inspectionData);
    
    if (result.success) {
      return {
        success: true,
        message: '三角拍记录保存成功',
        data: result.data
      };
    } else {
      throw new Error(result.message);
    }
    
  } catch (error) {
    console.error('保存三角拍记录失败:', error);
    return {
      success: false,
      message: error.message || '保存三角拍记录失败'
    };
  }
};

// 保存仰拍记录
export const saveUpwardShotRecord = async (recordData) => {
  try {
    console.log('开始保存仰拍记录:', recordData);

    // 上传图片
    console.log('开始上传仰拍图片:', recordData.imageUrl);
    const uploadResult = await uploadImage(recordData.imageUrl, 'upward');
    console.log('仰拍图片上传结果:', uploadResult);
    
    if (!uploadResult.success) {
      throw new Error(uploadResult.message);
    }
    
    // 创建巡检记录
    const inspectionData = {
      device_id: recordData.deviceId || 'PIPE001',
      inspection_type: 'upward',
      inspection_status: recordData.status === '正常' ? 'normal' : (recordData.status === '异常' ? 'abnormal' : 'warning'),
      location: recordData.location || '',
      notes: recordData.notes || '',
      abnormal_info: recordData.abnormalInfo || '',
      inspection_time: recordData.uploadTime || new Date().toISOString(),
      images: [{
        type: 'upward',
        url: uploadResult.data.url,
        name: uploadResult.data.filename,
        size: uploadResult.data.size
      }]
    };

    console.log('创建仰拍巡检记录:', inspectionData);
    const result = await createInspection(inspectionData);
    console.log('仰拍巡检记录创建结果:', result);
    
    if (result.success) {
      return {
        success: true,
        message: '仰拍记录保存成功',
        data: result.data
      };
    } else {
      throw new Error(result.message);
    }
    
  } catch (error) {
    console.error('保存仰拍记录失败:', error);
    return {
      success: false,
      message: error.message || '保存仰拍记录失败'
    };
  }
};

// 获取巡检历史记录（兼容原有接口）
export const getPhotoList = async (params = {}) => {
  try {
    const result = await getInspectionList(params);
    
    if (result.success) {
      // 转换数据格式以兼容原有的照片列表格式
      const photos = result.data.inspections.map(inspection => {
        // 辅助函数：将相对URL转换为完整URL
        const getFullImageUrl = (relativeUrl) => {
          if (!relativeUrl) return '';
          if (relativeUrl.startsWith('http')) return relativeUrl;
          return `http://localhost:3001${relativeUrl}`;
        };

        return {
          id: inspection.id.toString(),
          deviceId: inspection.device_id,
          type: inspection.inspection_type === 'triangle' ? 'triangle-shot' : 'upward-shot',
          date: new Date(inspection.inspection_time).toLocaleDateString('zh-CN'),
          uploadTime: inspection.inspection_time,
          inspector: inspection.user?.display_name || inspection.user?.username || '未知',
          status: inspection.inspection_status === 'normal' ? '正常' : '异常',
          abnormalInfo: inspection.abnormal_info || '',
          imageUrl: getFullImageUrl(inspection.images?.[0]?.image_url || ''),
          photos: inspection.inspection_type === 'triangle' ? {
            left: getFullImageUrl(inspection.images?.find(img => img.image_type === 'left')?.image_url || ''),
            center: getFullImageUrl(inspection.images?.find(img => img.image_type === 'center')?.image_url || ''),
            right: getFullImageUrl(inspection.images?.find(img => img.image_type === 'right')?.image_url || '')
          } : undefined
        };
      });
      
      return {
        success: true,
        message: '获取历史记录成功',
        data: {
          photos: photos,
          pagination: result.data.pagination
        }
      };
    } else {
      throw new Error(result.message);
    }
    
  } catch (error) {
    console.error('获取历史记录失败:', error);
    return {
      success: false,
      message: error.message || '获取历史记录失败',
      data: {
        photos: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          pages: 0
        }
      }
    };
  }
};

// 删除巡检记录
export const deletePhoto = async (photoId) => {
  try {
    const response = await request({
      url: `/inspections/${photoId}`,
      method: 'DELETE'
    });
    
    return {
      success: true,
      message: '删除成功'
    };
  } catch (error) {
    console.error('删除巡检记录失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '删除失败'
    };
  }
};

// 上传单张照片（兼容原有接口）
export const uploadPhoto = async (filePath, type = 'upload', position = '') => {
  try {
    const result = await uploadImage(filePath, position || type);
    
    if (result.success) {
      return {
        success: true,
        message: '上传成功',
        data: {
          url: result.data.url,
          id: Date.now().toString(),
          position: position,
          originalPath: filePath
        }
      };
    } else {
      throw new Error(result.message);
    }
    
  } catch (error) {
    console.error('上传照片失败:', error);
    return {
      success: false,
      message: error.message || '上传失败'
    };
  }
};
