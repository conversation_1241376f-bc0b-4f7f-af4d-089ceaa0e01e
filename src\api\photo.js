// 模拟照片相关的API接口，使用本地存储

// 获取当前用户信息
const getCurrentUser = () => {
  try {
    const token = uni.getStorageSync('token');
    const userInfo = uni.getStorageSync('userInfo');
    return { token, userInfo };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return { token: null, userInfo: null };
  }
};

// 获取本地存储的照片列表（按用户分离）
export const getLocalPhotos = () => {
  try {
    const { userInfo } = getCurrentUser();
    if (!userInfo) {
      console.log('用户未登录，返回空照片列表');
      return [];
    }

    // 使用用户相关的存储key
    const storageKey = `local_photos_${userInfo.username || 'default'}`;
    const photos = uni.getStorageSync(storageKey) || [];
    console.log(`获取用户 ${userInfo.username} 的照片数量:`, photos.length);

    // 调试：打印每张照片的状态
    photos.forEach((photo, index) => {
      console.log(`照片${index + 1}: 状态=${photo.status}, 时间=${photo.uploadTime || photo.date}`);
    });

    return photos;
  } catch (error) {
    console.error('获取本地照片失败:', error);
    return [];
  }
};

// 保存照片到本地存储（按用户分离）
const saveLocalPhotos = (photos) => {
  try {
    const { userInfo } = getCurrentUser();
    if (!userInfo) {
      console.error('用户未登录，无法保存照片');
      return false;
    }

    const storageKey = `local_photos_${userInfo.username || 'default'}`;
    uni.setStorageSync(storageKey, photos);
    console.log(`保存用户 ${userInfo.username} 的照片到本地存储，数量:`, photos.length);
    return true;
  } catch (error) {
    console.error('保存本地照片失败:', error);
    return false;
  }
};

// 模拟获取照片列表接口
export const getPhotoList = async (params = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      try {
        const { userInfo } = getCurrentUser();
        if (!userInfo) {
          resolve({
            success: false,
            message: '请先登录',
            data: {
              photos: [],
              pagination: {
                page: 1,
                limit: 20,
                total: 0,
                pages: 0
              }
            }
          });
          return;
        }

        const allPhotos = getLocalPhotos();
        const { page = 1, limit = 20 } = params;

        console.log('getPhotoList 调用参数:', params);
        console.log(`用户 ${userInfo.username} 的照片总数:`, allPhotos.length);

        // 分页处理
        const start = (page - 1) * limit;
        const end = start + limit;
        const photos = allPhotos.slice(start, end);

        console.log('分页结果 - 开始:', start, '结束:', end, '当前页数据量:', photos.length);

        const result = {
          success: true,
          message: '获取成功',
          data: {
            photos: photos,
            pagination: {
              page: page,
              limit: limit,
              total: allPhotos.length,
              pages: Math.ceil(allPhotos.length / limit)
            }
          }
        };

        console.log('getPhotoList 返回结果:', result);
        resolve(result);
      } catch (error) {
        console.error('getPhotoList 执行失败:', error);
        resolve({
          success: false,
          message: '获取照片列表失败: ' + error.message,
          data: {
            photos: [],
            pagination: {
              page: 1,
              limit: 20,
              total: 0,
              pages: 0
            }
          }
        });
      }
    }, 300);
  });
};

// H5环境下的图片处理（直接返回原路径）
const saveImageForH5 = async (tempFilePath) => {
  try {
    console.log('H5环境：直接使用图片路径，无需文件复制');

    // H5环境下，图片通常是blob URL或base64，直接使用
    // 生成一个模拟的永久路径标识
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const mockPermanentPath = `h5_img_${timestamp}_${randomStr}`;

    console.log('H5图片处理完成:', {
      original: tempFilePath,
      mock: mockPermanentPath
    });

    // 在H5环境下，我们返回原始路径，因为blob URL在当前会话中有效
    return tempFilePath;
  } catch (error) {
    console.error('H5图片处理失败:', error);
    return tempFilePath;
  }
};

// 原生环境下的图片持久化
const saveImageForNative = async (tempFilePath) => {
  try {
    // 生成永久文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const fileExtension = tempFilePath.split('.').pop() || 'jpg';
    const permanentFileName = `img_${timestamp}_${randomStr}.${fileExtension}`;

    // 获取永久存储路径
    const fs = uni.getFileSystemManager();
    const permanentPath = `${uni.env.USER_DATA_PATH}/${permanentFileName}`;

    // 复制文件到永久位置
    return new Promise((resolve, reject) => {
      fs.copyFile({
        srcPath: tempFilePath,
        destPath: permanentPath,
        success: () => {
          console.log('图片保存到永久存储成功:', permanentPath);
          resolve(permanentPath);
        },
        fail: (error) => {
          console.error('图片保存到永久存储失败:', error);
          // 如果保存失败，返回原始路径
          resolve(tempFilePath);
        }
      });
    });
  } catch (error) {
    console.error('原生环境图片持久化处理失败:', error);
    return tempFilePath;
  }
};

// 将临时文件保存到永久存储位置（兼容H5和原生环境）
const saveImageToPermanentStorage = async (tempFilePath) => {
  try {
    console.log('开始图片持久化处理，当前环境:', isH5Environment() ? 'H5' : 'Native');

    if (isH5Environment()) {
      // H5环境：直接使用原路径
      return await saveImageForH5(tempFilePath);
    } else {
      // 原生环境：复制到永久存储
      return await saveImageForNative(tempFilePath);
    }
  } catch (error) {
    console.error('图片持久化处理失败:', error);
    return tempFilePath;
  }
};

// 模拟上传照片接口（增强版 - 支持图片持久化）
export const uploadPhoto = async (filePath, type = 'upload', position = '') => {
  return new Promise(async (resolve) => {
    try {
      console.log('开始上传图片:', filePath);

      // 将临时文件保存到永久存储
      const permanentPath = await saveImageToPermanentStorage(filePath);

      setTimeout(() => {
        const newPhoto = {
          id: Date.now().toString() + (position ? '-' + position : ''),
          url: permanentPath, // 使用永久路径
          originalPath: filePath, // 保留原始临时路径用于调试
          filename: filePath.split('/').pop(),
          uploadTime: new Date().toISOString(),
          deviceId: 'mock-device-001',
          type: type,
          position: position,
          isPermanent: true // 标记为永久存储
        };

        console.log('图片上传完成:', {
          original: filePath,
          permanent: permanentPath,
          id: newPhoto.id
        });

        resolve({
          success: true,
          message: '上传成功',
          data: {
            url: permanentPath, // 返回永久路径
            id: newPhoto.id,
            position: position,
            originalPath: filePath
          }
        });
      }, 500);
    } catch (error) {
      console.error('图片上传失败:', error);
      resolve({
        success: false,
        message: '上传失败: ' + error.message
      });
    }
  });
};

// 保存三角拍记录到历史数据
export const saveTriangleShotRecord = async (recordData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      try {
        const { userInfo } = getCurrentUser();
        if (!userInfo) {
          resolve({
            success: false,
            message: '请先登录'
          });
          return;
        }

        const allPhotos = getLocalPhotos();

        // 创建历史记录条目
        const historyRecord = {
          id: Date.now().toString(),
          deviceId: recordData.deviceId,
          type: 'triangle-shot',
          date: new Date().toLocaleDateString('zh-CN'),
          uploadTime: recordData.uploadTime,
          inspector: userInfo.username || recordData.inspector,
          status: recordData.status,
          abnormalInfo: recordData.abnormalInfo,
          // 将三张照片的URL合并，用于历史页面显示
          imageUrl: recordData.photos.center.url || recordData.photos.center, // 主要显示中间的照片
          photos: {
            left: recordData.photos.left.url || recordData.photos.left,
            center: recordData.photos.center.url || recordData.photos.center,
            right: recordData.photos.right.url || recordData.photos.right
          }
        };

        allPhotos.unshift(historyRecord); // 添加到开头
        const saveResult = saveLocalPhotos(allPhotos);

        if (saveResult) {
          console.log(`用户 ${userInfo.username} 保存三角拍记录成功:`, historyRecord);
          resolve({
            success: true,
            message: '记录保存成功',
            data: historyRecord
          });
        } else {
          resolve({
            success: false,
            message: '记录保存失败'
          });
        }
      } catch (error) {
        console.error('保存三角拍记录失败:', error);
        resolve({
          success: false,
          message: '记录保存失败: ' + error.message
        });
      }
    }, 300);
  });
};

// 保存仰拍记录到历史数据
export const saveUpwardShotRecord = async (recordData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      try {
        const { userInfo } = getCurrentUser();
        if (!userInfo) {
          resolve({
            success: false,
            message: '请先登录'
          });
          return;
        }

        const allPhotos = getLocalPhotos();

        // 创建历史记录条目
        const historyRecord = {
          id: Date.now().toString(),
          deviceId: recordData.deviceId,
          type: 'upward-shot',
          date: new Date().toLocaleDateString('zh-CN'),
          uploadTime: recordData.uploadTime,
          inspector: userInfo.username || recordData.inspector,
          status: recordData.status,
          abnormalInfo: recordData.abnormalInfo,
          imageUrl: recordData.imageUrl
        };

        allPhotos.unshift(historyRecord); // 添加到开头
        const saveResult = saveLocalPhotos(allPhotos);

        if (saveResult) {
          console.log(`用户 ${userInfo.username} 保存仰拍记录成功:`, historyRecord);
          resolve({
            success: true,
            message: '记录保存成功',
            data: historyRecord
          });
        } else {
          resolve({
            success: false,
            message: '记录保存失败'
          });
        }
      } catch (error) {
        console.error('保存仰拍记录失败:', error);
        resolve({
          success: false,
          message: '记录保存失败: ' + error.message
        });
      }
    }, 300);
  });
};

// 删除永久存储的图片文件
const deletePermanentImage = async (imagePath) => {
  try {
    if (!imagePath || !imagePath.includes(uni.env.USER_DATA_PATH)) {
      console.log('跳过删除非永久存储图片:', imagePath);
      return true;
    }

    const fs = uni.getFileSystemManager();
    return new Promise((resolve) => {
      fs.unlink({
        filePath: imagePath,
        success: () => {
          console.log('永久存储图片删除成功:', imagePath);
          resolve(true);
        },
        fail: (error) => {
          console.warn('永久存储图片删除失败:', error);
          resolve(false); // 即使删除失败也继续
        }
      });
    });
  } catch (error) {
    console.error('删除永久存储图片时出错:', error);
    return false;
  }
};

// 模拟删除照片接口（增强版 - 支持删除永久存储的图片）
export const deletePhoto = async (photoId) => {
  return new Promise(async (resolve) => {
    setTimeout(async () => {
      try {
        const allPhotos = getLocalPhotos();
        const index = allPhotos.findIndex(photo => photo.id === photoId);

        if (index > -1) {
          const photo = allPhotos[index];

          // 删除永久存储的图片文件
          if (photo.imageUrl) {
            await deletePermanentImage(photo.imageUrl);
          }

          // 如果是三角拍，删除三张图片
          if (photo.photos) {
            await Promise.all([
              deletePermanentImage(photo.photos.left),
              deletePermanentImage(photo.photos.center),
              deletePermanentImage(photo.photos.right)
            ]);
          }

          // 从数据中删除记录
          allPhotos.splice(index, 1);
          saveLocalPhotos(allPhotos);

          console.log('照片记录和文件删除成功:', photoId);
          resolve({
            success: true,
            message: '删除成功'
          });
        } else {
          resolve({
            success: false,
            message: '照片不存在'
          });
        }
      } catch (error) {
        console.error('删除照片失败:', error);
        resolve({
          success: false,
          message: '删除失败: ' + error.message
        });
      }
    }, 300);
  });
};

// 添加模拟照片数据（用于演示）
export const addMockPhotos = () => {
  const mockPhotos = [
    {
      id: 'mock-1',
      deviceId: 'DEVICE001',
      type: 'triangle-shot',
      date: '2024/1/15',
      uploadTime: '2024-01-15T10:30:00.000Z',
      inspector: '张三',
      status: '正常',
      abnormalInfo: '',
      imageUrl: '/static/logo.png', // 使用项目中存在的图片
      photos: {
        left: '/static/logo.png',
        center: '/static/logo.png',
        right: '/static/logo.png'
      }
    },
    {
      id: 'mock-2',
      deviceId: 'DEVICE002',
      type: 'upward-shot',
      date: '2024/1/14',
      uploadTime: '2024-01-14T15:45:00.000Z',
      inspector: '李四',
      status: '异常',
      abnormalInfo: '发现设备振动异常，需要进一步检查',
      imageUrl: '/static/logo.png'
    },
    {
      id: 'mock-3',
      deviceId: 'DEVICE003',
      type: 'triangle-shot',
      date: '2024/1/13',
      uploadTime: '2024-01-13T09:15:00.000Z',
      inspector: '王五',
      status: '异常',
      abnormalInfo: '设备位移超出正常范围',
      imageUrl: '/static/logo.png',
      photos: {
        left: '/static/logo.png',
        center: '/static/logo.png',
        right: '/static/logo.png'
      }
    }
  ];

  saveLocalPhotos(mockPhotos);
  return mockPhotos;
};

// 初始化模拟数据（如果本地存储为空）
export const initMockDataIfEmpty = () => {
  try {
    const existingPhotos = getLocalPhotos();
    console.log('检查现有照片数量:', existingPhotos.length);

    if (existingPhotos.length === 0) {
      console.log('本地存储为空，添加模拟数据');
      const mockPhotos = addMockPhotos();
      console.log('已添加模拟数据，数量:', mockPhotos.length);
      return mockPhotos;
    } else {
      console.log('本地存储已有数据，跳过初始化');
      return existingPhotos;
    }
  } catch (error) {
    console.error('初始化模拟数据失败:', error);
    return [];
  }
};

// H5环境下的图片清理
const clearH5Images = () => {
  try {
    console.log('H5环境：清理localStorage中的图片数据');

    // 获取所有照片数据
    const allPhotos = getLocalPhotos();
    let clearedCount = 0;

    // 统计将要清理的图片数量
    allPhotos.forEach(photo => {
      if (photo.type === 'triangle' && photo.photos) {
        if (photo.photos.left) clearedCount++;
        if (photo.photos.center) clearedCount++;
        if (photo.photos.right) clearedCount++;
      } else if (photo.imageUrl) {
        clearedCount++;
      }
    });

    // 清空所有照片数据（这会清除图片引用）
    saveLocalPhotos([]);

    console.log('H5环境图片数据清理完成，清理数量:', clearedCount);

    return {
      success: true,
      message: `清理了 ${clearedCount} 个图片引用`,
      count: clearedCount,
      environment: 'H5',
      note: '清理了localStorage中的图片数据引用'
    };
  } catch (error) {
    console.error('H5图片清理失败:', error);
    return {
      success: false,
      message: 'H5图片清理失败: ' + error.message
    };
  }
};

// 原生环境下的图片清理
const clearNativeImages = async () => {
  try {
    console.log('原生环境：清理文件系统中的图片文件');

    const fs = uni.getFileSystemManager();
    const userDataPath = uni.env.USER_DATA_PATH;

    return new Promise((resolve) => {
      // 读取用户数据目录
      fs.readdir({
        dirPath: userDataPath,
        success: (res) => {
          const imageFiles = res.files.filter(file =>
            file.startsWith('img_') &&
            (file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.jpeg'))
          );

          console.log('找到永久存储图片文件:', imageFiles.length);

          // 删除所有图片文件
          const deletePromises = imageFiles.map(file => {
            const filePath = `${userDataPath}/${file}`;
            return deletePermanentImage(filePath);
          });

          Promise.all(deletePromises).then(() => {
            console.log('所有永久存储图片清理完成');
            resolve({
              success: true,
              message: `清理了 ${imageFiles.length} 个图片文件`,
              count: imageFiles.length,
              environment: 'Native',
              note: '清理了文件系统中的图片文件'
            });
          });
        },
        fail: (error) => {
          console.error('读取用户数据目录失败:', error);
          resolve({
            success: false,
            message: '清理失败: ' + error.errMsg
          });
        }
      });
    });
  } catch (error) {
    console.error('原生环境图片清理失败:', error);
    return {
      success: false,
      message: '清理失败: ' + error.message
    };
  }
};

// 清理所有永久存储的图片（兼容H5和原生环境）
export const clearAllPermanentImages = async () => {
  try {
    console.log('开始清理所有图片，当前环境:', isH5Environment() ? 'H5' : 'Native');

    if (isH5Environment()) {
      // H5环境：清理localStorage中的图片数据
      return clearH5Images();
    } else {
      // 原生环境：清理文件系统中的图片文件
      return await clearNativeImages();
    }
  } catch (error) {
    console.error('清理图片失败:', error);
    return {
      success: false,
      message: '清理失败: ' + error.message,
      environment: isH5Environment() ? 'H5' : 'Native'
    };
  }
};

// 检测当前运行环境
const isH5Environment = () => {
  // #ifdef H5
  return true;
  // #endif
  // #ifndef H5
  return false;
  // #endif
};

// H5环境下的存储信息获取
const getH5StorageInfo = () => {
  try {
    console.log('H5环境：使用localStorage统计存储信息');

    // 获取所有照片数据
    const allPhotos = getLocalPhotos();

    // 统计图片数量
    let imageCount = 0;
    let estimatedSize = 0;
    const files = [];

    allPhotos.forEach(photo => {
      if (photo.type === 'triangle') {
        // 三角拍有3张图片
        if (photo.photos) {
          if (photo.photos.left) {
            imageCount++;
            files.push(`triangle_left_${photo.id}.jpg`);
          }
          if (photo.photos.center) {
            imageCount++;
            files.push(`triangle_center_${photo.id}.jpg`);
          }
          if (photo.photos.right) {
            imageCount++;
            files.push(`triangle_right_${photo.id}.jpg`);
          }
        }
      } else if (photo.type === 'upward') {
        // 仰拍有1张图片
        if (photo.imageUrl) {
          imageCount++;
          files.push(`upward_${photo.id}.jpg`);
        }
      } else if (photo.imageUrl) {
        // 其他类型的单张图片
        imageCount++;
        files.push(`photo_${photo.id}.jpg`);
      }
    });

    // 估算大小（每张图片约500KB）
    estimatedSize = imageCount * 500 * 1024;

    return {
      success: true,
      data: {
        imageCount: imageCount,
        estimatedSize: estimatedSize,
        estimatedSizeMB: (estimatedSize / 1024 / 1024).toFixed(2),
        files: files,
        environment: 'H5',
        note: '基于localStorage数据统计'
      }
    };
  } catch (error) {
    console.error('H5存储信息获取失败:', error);
    return {
      success: false,
      message: 'H5存储信息获取失败: ' + error.message
    };
  }
};

// 原生环境下的存储信息获取
const getNativeStorageInfo = async () => {
  try {
    const fs = uni.getFileSystemManager();
    const userDataPath = uni.env.USER_DATA_PATH;

    return new Promise((resolve) => {
      fs.readdir({
        dirPath: userDataPath,
        success: (res) => {
          const imageFiles = res.files.filter(file =>
            file.startsWith('img_') &&
            (file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.jpeg'))
          );

          // 计算总大小（简化版，实际应该读取每个文件的大小）
          const estimatedSize = imageFiles.length * 500 * 1024; // 假设每张图片500KB

          resolve({
            success: true,
            data: {
              imageCount: imageFiles.length,
              estimatedSize: estimatedSize,
              estimatedSizeMB: (estimatedSize / 1024 / 1024).toFixed(2),
              files: imageFiles,
              environment: 'Native',
              note: '基于文件系统统计'
            }
          });
        },
        fail: (error) => {
          resolve({
            success: false,
            message: '获取存储信息失败: ' + error.errMsg
          });
        }
      });
    });
  } catch (error) {
    return {
      success: false,
      message: '获取存储信息失败: ' + error.message
    };
  }
};

// 获取存储空间使用情况（兼容H5和原生环境）
export const getStorageInfo = async () => {
  try {
    console.log('开始获取存储信息，当前环境:', isH5Environment() ? 'H5' : 'Native');

    if (isH5Environment()) {
      // H5环境：使用localStorage数据统计
      return getH5StorageInfo();
    } else {
      // 原生环境：使用文件系统API
      return await getNativeStorageInfo();
    }
  } catch (error) {
    console.error('获取存储信息失败:', error);
    return {
      success: false,
      message: '获取存储信息失败: ' + error.message,
      environment: isH5Environment() ? 'H5' : 'Native'
    };
  }
};