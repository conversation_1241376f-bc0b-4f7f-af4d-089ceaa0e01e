// 用户相关API
import { request } from '@/utils/request';

// 获取当前用户信息
export const getCurrentUser = async () => {
  try {
    const response = await request({
      url: '/auth/me',
      method: 'GET'
    });
    
    return {
      success: true,
      message: '获取用户信息成功',
      data: response
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '获取用户信息失败'
    };
  }
};

// 更新用户信息
export const updateUserProfile = async (userId, profileData) => {
  try {
    console.log('=== updateUserProfile API 调试 ===');
    console.log('用户ID:', userId);
    console.log('用户ID类型:', typeof userId);
    console.log('更新数据:', profileData);
    console.log('请求URL:', `/users/${userId}`);

    // 检查token
    const token = uni.getStorageSync('token');
    console.log('Token存在:', !!token);

    const response = await request({
      url: `/users/${userId}`,
      method: 'PUT',
      data: profileData
    });

    console.log('用户信息更新响应:', response);
    
    return {
      success: true,
      message: '用户信息更新成功',
      data: response.user || response
    };
  } catch (error) {
    console.error('更新用户信息失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '更新用户信息失败'
    };
  }
};

// 上传头像
export const uploadAvatar = async (filePath) => {
  try {
    console.log('开始上传头像:', filePath);
    
    // 使用uni.uploadFile上传头像
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: 'http://localhost:3001/api/upload/image',
        filePath: filePath,
        name: 'image',
        formData: {
          type: 'avatar',
          compress: 'true'
        },
        header: {
          'Authorization': `Bearer ${uni.getStorageSync('token')}`
        },
        success: (uploadRes) => {
          try {
            console.log('头像上传成功响应:', uploadRes);
            const data = JSON.parse(uploadRes.data);
            console.log('解析后的响应数据:', data);
            
            if (data.file) {
              console.log('头像上传成功:', data.file);
              resolve({
                success: true,
                message: '头像上传成功',
                data: {
                  url: data.file.url,
                  filename: data.file.filename,
                  size: data.file.size
                }
              });
            } else {
              console.error('头像上传响应格式错误:', data);
              reject(new Error('上传响应格式错误'));
            }
          } catch (error) {
            console.error('解析头像上传响应失败:', error, uploadRes);
            reject(new Error('解析上传响应失败'));
          }
        },
        fail: (error) => {
          console.error('头像上传失败:', error);
          reject(new Error(error.errMsg || '头像上传失败'));
        }
      });
    });
  } catch (error) {
    console.error('上传头像失败:', error);
    return {
      success: false,
      message: error.message || '上传头像失败'
    };
  }
};

// 修改密码
export const changePassword = async (userId, passwordData) => {
  try {
    console.log('修改密码:', { userId });
    
    const response = await request({
      url: `/users/${userId}/password`,
      method: 'PUT',
      data: passwordData
    });
    
    console.log('密码修改响应:', response);
    
    return {
      success: true,
      message: '密码修改成功',
      data: response
    };
  } catch (error) {
    console.error('修改密码失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '修改密码失败'
    };
  }
};

// 获取用户统计信息
export const getUserStats = async () => {
  try {
    const response = await request({
      url: '/statistics/user',
      method: 'GET'
    });
    
    return {
      success: true,
      message: '获取用户统计成功',
      data: response
    };
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return {
      success: false,
      message: error.response?.data?.error || error.message || '获取用户统计失败'
    };
  }
};
