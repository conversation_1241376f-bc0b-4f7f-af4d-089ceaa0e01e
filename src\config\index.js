// 应用配置文件
export const config = {
  // 开发模式配置
  development: {
    // 连接真实后端API
    useMockData: false,
    // 后端API地址
    baseURL: 'http://localhost:3001/api',
    // 请求超时时间
    timeout: 10000,
    // 调试模式
    debug: true,
    // 上传地址
    uploadURL: 'http://localhost:3001'
  },
  
  // 生产模式配置
  production: {
    // 生产环境使用真实API
    useMockData: false,
    // 生产环境API地址（宝塔面板nginx代理）
    baseURL: '/api',
    timeout: 30000,
    debug: false
  },

  // 宝塔面板部署配置
  baota: {
    useMockData: false,
    baseURL: '/api', // 通过nginx代理到后端
    timeout: 30000,
    debug: false
  }
};

// 获取当前环境配置
export const getCurrentConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  return config[env] || config.development;
};

// 检查是否使用模拟数据
export const useMockData = () => {
  const currentConfig = getCurrentConfig();
  return currentConfig.useMockData;
};

// 网络状态检查
export const checkNetworkStatus = () => {
  return new Promise((resolve) => {
    // 在模拟数据模式下，总是返回网络正常
    if (useMockData()) {
      resolve({
        isConnected: true,
        networkType: 'mock',
        message: '使用模拟数据模式'
      });
      return;
    }
    
    // 真实网络检查
    uni.getNetworkType({
      success: (res) => {
        resolve({
          isConnected: res.networkType !== 'none',
          networkType: res.networkType,
          message: res.networkType === 'none' ? '网络未连接' : '网络正常'
        });
      },
      fail: () => {
        resolve({
          isConnected: false,
          networkType: 'unknown',
          message: '网络检查失败'
        });
      }
    });
  });
};
