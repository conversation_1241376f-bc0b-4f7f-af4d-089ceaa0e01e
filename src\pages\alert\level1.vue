<template>
  <!-- 预警容器 - 整个一级预警页面的容器 -->
  <view class="alert-container">
    <!-- 页面头部 - 显示预警级别标题 -->
    <view class="header">
      <text class="title">一级预警</text>
    </view>

    <!-- 地图区域 - 显示位置信息和标签 -->
    <view class="map-area">
      <!-- 地图图片 - 显示地图信息 -->
      <image class="map-image" src="/static/map-placeholder.png" mode="aspectFill"></image>
      
      <!-- 标签区域 - 显示预警相关的标签 -->
      <view class="tag-area">
        <text class="tags-label">Tags:</text>
        <!-- 标签容器 - 包含多个标签项 -->
        <view class="tags-container">
          <!-- 标签项 - 显示"设备振动异常"标签 -->
          <view class="tag-item">设备振动异常</view>
          <!-- 标签项 - 显示"位移异常"标签 -->
          <view class="tag-item">位移异常</view>
        </view>
      </view>
    </view>

    <!-- 设备信息区域 - 显示设备相关信息 -->
    <view class="device-info">
      <!-- 信息项 - 设备名称 -->
      <view class="info-item">
        <text class="label">设备名称:</text>
        <text class="value">XXXX</text>
      </view>
      <!-- 信息项 - 设备编号 -->
      <view class="info-item">
        <text class="label">设备编号:</text>
        <text class="value">XXXX</text>
      </view>
    </view>

    <!-- 操作按钮区域 - 包含短信通知和拨打电话按钮 -->
    <view class="action-buttons">
      <!-- 短信通知按钮 - 点击发送短信通知 -->
      <button class="action-btn sms-btn" @click="sendSMS">短信通知</button>
      <!-- 关键电话按钮 - 点击拨打关键联系人电话 -->
      <button class="action-btn call-btn" @click="makeCall">关键手机</button>
    </view>

    <!-- 底部导航栏 - 包含四个导航选项 -->
    <view class="tab-bar">
      <!-- 首页选项 - 点击跳转到首页 -->
      <view class="tab-item" @click="goToHome">
        <image class="tab-icon" src="/static/home.png" mode="aspectFit"></image>
        <text class="tab-text">首页</text>
      </view>
      <!-- 预警选项 - 当前激活项 -->
      <view class="tab-item active">
        <image class="tab-icon" src="/static/alert-active.png" mode="aspectFit"></image>
        <text class="tab-text">预警</text>
      </view>
      <!-- 拍照选项 - 点击跳转到三角拍页面 -->
      <view class="tab-item" @click="goToTriangleShot">
        <image class="tab-icon" src="/static/camera.png" mode="aspectFit"></image>
        <text class="tab-text">拍照</text>
      </view>
      <!-- 个人中心选项 - 点击跳转到个人中心页面 -->
      <view class="tab-item" @click="goToProfile">
        <image class="tab-icon" src="/static/user.png" mode="aspectFit"></image>
        <text class="tab-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  // 定义组件的数据
  data() {
    return {
      // 设备信息对象 - 包含设备ID和名称
      deviceInfo: {
        id: 'DEVICE001', // 设备ID
        name: '设备001'   // 设备名称
      }
    }
  },
  // 定义组件的方法
  methods: {
    // 发送短信通知的方法
    sendSMS() {
      // 显示发送成功的提示
      uni.showToast({
        title: '短信通知已发送',
        icon: 'success'
      })
    },
    // 拨打电话的方法
    makeCall() {
      // 调用系统拨号功能
      uni.makePhoneCall({
        phoneNumber: '10086', // 这里替换为实际电话号码
        fail: () => {
          // 拨打失败时的提示
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          })
        }
      })
    },
    // 跳转到首页的方法
    goToHome() {
      // 使用switchTab切换到tabBar页面
      uni.switchTab({
        url: '/pages/home/<USER>'
      })
    },
    // 跳转到三角拍页面的方法
    goToTriangleShot() {
      // 使用navigateTo跳转到非tabBar页面
      uni.navigateTo({
        url: '/pages/triangle-shot/index'
      })
    },
    // 跳转到个人中心的方法
    goToProfile() {
      // 使用switchTab切换到tabBar页面
      uni.switchTab({
        url: '/pages/profile/index'
      })
    }
  }
}
</script>

<style>
/* 预警容器样式 - 设置最小高度、背景色、位置和底部内边距 */
.alert-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 120rpx;
}

/* 头部样式 - 设置背景色、内边距和文本对齐方式 */
.header {
  background-color: #fff;
  padding: 30rpx;
  text-align: center;
}

/* 标题样式 - 设置字体大小、字体粗细和颜色 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 地图区域样式 - 设置外边距、背景色、圆角和溢出处理 */
.map-area {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 地图图片样式 - 设置宽度和高度 */
.map-image {
  width: 100%;
  height: 500rpx;
}

/* 标签区域样式 - 设置内边距 */
.tag-area {
  padding: 20rpx;
}

/* 标签标题样式 - 设置字体大小、颜色和底部外边距 */
.tags-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

/* 标签容器样式 - 设置显示方式、换行和间距 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

/* 标签项样式 - 设置背景色、内边距、圆角、字体大小、颜色和边框 */
.tag-item {
  background-color: #f0f0f0;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  border: 1rpx solid #ddd;
}

/* 设备信息区域样式 - 设置外边距、背景色、圆角和内边距 */
.device-info {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
}

/* 信息项样式 - 设置显示方式和内边距 */
.info-item {
  display: flex;
  padding: 15rpx 0;
}

/* 标签样式 - 设置字体大小、颜色和宽度 */
.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

/* 值样式 - 设置字体大小、颜色和弹性布局 */
.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 操作按钮区域样式 - 设置显示方式、对齐方式、外边距和间距 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin: 20rpx;
  gap: 20rpx;
}

/* 操作按钮通用样式 - 设置弹性布局、高度、行高、圆角、字体大小、颜色和文本对齐方式 */
.action-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  color: #fff;
  text-align: center;
}

/* 短信按钮样式 - 设置背景色 */
.sms-btn {
  background-color: #ff3b30;
}

/* 拨打电话按钮样式 - 设置背景色 */
.call-btn {
  background-color: #007aff;
}

/* 底部导航栏样式 - 设置位置、尺寸、背景色、显示方式、对齐方式和上边框 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  border-top: 1rpx solid #eee;
}

/* 导航项样式 - 设置弹性布局、显示方式、排列方向、对齐方式和居中方式 */
.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 导航项激活状态样式 - 设置颜色 */
.tab-item.active {
  color: #007aff;
}

/* 导航图标样式 - 设置宽度和高度 */
.tab-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 导航文本样式 - 设置字体大小和上外边距 */
.tab-text {
  font-size: 24rpx;
  margin-top: 6rpx;
}
</style> 