<template>
  <view class="analysis-container">
    <view class="header">
      <text class="title">数据分析</text>
    </view>
    
    <view class="stats-cards">
      <view class="stat-card">
        <text class="card-title">本月巡检统计</text>
        <view class="card-content">
          <view class="stat-item">
            <text class="number">{{ stats.thisMonth.total }}</text>
            <text class="label">总巡检次数</text>
          </view>
          <view class="stat-item">
            <text class="number">{{ stats.thisMonth.abnormal }}</text>
            <text class="label">异常次数</text>
          </view>
          <view class="stat-item">
            <text class="number">{{ stats.thisMonth.normalRate }}%</text>
            <text class="label">正常率</text>
          </view>
        </view>
      </view>
      
      <view class="stat-card">
        <text class="card-title">设备状态分布</text>
        <view class="card-content">
          <view class="stat-item">
            <text class="number">{{ stats.deviceStatus.normal }}%</text>
            <text class="label">正常运行</text>
          </view>
          <view class="stat-item">
            <text class="number">{{ stats.deviceStatus.maintenance }}%</text>
            <text class="label">需要维护</text>
          </view>
          <view class="stat-item">
            <text class="number">{{ stats.deviceStatus.fault }}%</text>
            <text class="label">故障</text>
          </view>
        </view>
      </view>
    </view>

    <view class="chart-area">
      <text class="section-title">巡检趋势</text>
      <!-- 这里可以添加图表组件 -->
      <view class="chart-placeholder">
        <text>图表区域</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      stats: {
        thisMonth: {
          total: 0,
          abnormal: 0,
          normalRate: 100
        },
        deviceStatus: {
          normal: 85,
          maintenance: 10,
          fault: 5
        }
      }
    }
  },
  onLoad() {
    // 延迟加载，避免与导航栏切换冲突
    setTimeout(() => {
      this.loadAnalysisData();
    }, 100);
  },
  onShow() {
    // 页面显示时安全刷新数据
    console.log('数据分析页面onShow');
    this.loadAnalysisDataSafely();
  },
  methods: {
    // 加载分析数据
    async loadAnalysisData() {
      try {
        // 直接检查存储
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        console.log('分析页面登录检查 - Token:', token ? '已设置' : '未设置');
        console.log('分析页面登录检查 - UserInfo:', userInfo ? '已设置' : '未设置');

        if (!token || !userInfo) {
          console.log('数据分析页面检测到未登录状态');
          // 不立即跳转，给用户选择
          uni.showModal({
            title: '登录提示',
            content: '需要登录才能查看数据分析，是否前往登录？',
            success: (res) => {
              if (res.confirm) {
                uni.reLaunch({
                  url: '/pages/login/index'
                });
              } else {
                // 用户选择不登录，跳转到首页
                uni.switchTab({
                  url: '/pages/home/<USER>'
                });
              }
            }
          });
          return;
        }

        // 使用用户管理工具获取统计数据
        const { getUserStats } = await import('@/utils/userManager.js');
        const stats = getUserStats();
        this.stats = {
          thisMonth: stats.thisMonth,
          deviceStatus: stats.deviceStatus
        };

        console.log('数据分析页面数据加载成功:', this.stats);
      } catch (error) {
        console.error('加载分析数据失败:', error);
      }
    },
    // 安全地加载分析数据
    async loadAnalysisDataSafely() {
      try {
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        if (token && userInfo) {
          console.log('分析页面onShow - 刷新数据');
          const { getUserStats } = await import('@/utils/userManager.js');
          const stats = getUserStats();
          this.stats = {
            thisMonth: stats.thisMonth,
            deviceStatus: stats.deviceStatus
          };
          console.log('数据分析页面数据刷新成功:', this.stats);
        }
      } catch (error) {
        console.error('安全加载分析数据失败:', error);
      }
    }
  }
}
</script>

<style>
.analysis-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.header {
  padding: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.stats-cards {
  margin-top: 20rpx;
}

.stat-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.card-content {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.number {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  display: block;
}

.label {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}

.chart-area {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.chart-placeholder {
  height: 400rpx;
  background-color: #f8f9fc;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8rpx;
}

.chart-placeholder text {
  color: #999;
  font-size: 28rpx;
}
</style> 