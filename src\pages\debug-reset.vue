<template>
  <view class="container">
    <view class="header">
      <text class="title">调试和重置工具</text>
    </view>
    
    <view class="content">
      <view class="section">
        <text class="section-title">当前状态检查</text>
        <button @click="checkCurrentStatus" class="btn">检查当前状态</button>
      </view>
      
      <view class="section">
        <text class="section-title">清理和重置</text>
        <button @click="clearStorage" class="btn danger">清除本地存储</button>
        <button @click="reLogin" class="btn primary">重新登录</button>
      </view>
      
      <view class="section">
        <text class="section-title">测试功能</text>
        <button @click="testUserUpdate" class="btn">测试用户信息更新</button>
      </view>
      
      <view class="result" v-if="result">
        <text class="result-title">结果：</text>
        <text class="result-text">{{ result }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { login } from '@/api/auth';
import { updateUserProfile } from '@/api/user';

export default {
  data() {
    return {
      result: ''
    };
  },
  
  methods: {
    // 检查当前状态
    checkCurrentStatus() {
      console.log('=== 检查当前状态 ===');
      
      const userInfo = uni.getStorageSync('userInfo');
      const token = uni.getStorageSync('token');
      
      console.log('用户信息:', userInfo);
      console.log('Token存在:', !!token);
      
      let status = '当前状态:\n';
      status += `Token: ${token ? '存在' : '不存在'}\n`;
      
      if (userInfo) {
        status += `用户信息存在\n`;
        status += `- ID: ${userInfo.id || '缺失'}\n`;
        status += `- 用户名: ${userInfo.username || '缺失'}\n`;
        status += `- 显示名称: ${userInfo.displayName || '缺失'}\n`;
        status += `- 邮箱: ${userInfo.email || '缺失'}\n`;
        
        if (!userInfo.id) {
          status += '\n❌ 用户信息缺少ID，需要重新登录！';
        } else {
          status += '\n✅ 用户信息完整';
        }
      } else {
        status += '用户信息: 不存在\n';
        status += '❌ 需要登录';
      }
      
      this.result = status;
    },
    
    // 清除本地存储
    clearStorage() {
      try {
        uni.clearStorageSync();
        console.log('本地存储已清除');
        
        this.result = '✅ 本地存储已清除\n请重新登录';
        
        uni.showToast({
          title: '本地存储已清除',
          icon: 'success'
        });
      } catch (error) {
        console.error('清除本地存储失败:', error);
        this.result = '❌ 清除本地存储失败: ' + error.message;
      }
    },
    
    // 重新登录
    async reLogin() {
      try {
        this.result = '正在重新登录...';
        
        // 先清除旧数据
        uni.clearStorageSync();
        
        // 重新登录
        const loginResult = await login('admin', 'admin123');
        
        console.log('重新登录结果:', loginResult);
        
        if (loginResult.success) {
          // 保存新的用户信息和token
          uni.setStorageSync('token', loginResult.data.token);
          uni.setStorageSync('userInfo', loginResult.data.user);
          
          console.log('保存的用户信息:', loginResult.data.user);
          
          this.result = '✅ 重新登录成功！\n';
          this.result += `用户ID: ${loginResult.data.user.id}\n`;
          this.result += `用户名: ${loginResult.data.user.username}\n`;
          this.result += `显示名称: ${loginResult.data.user.displayName}\n`;
          
          uni.showToast({
            title: '重新登录成功',
            icon: 'success'
          });
        } else {
          throw new Error(loginResult.message);
        }
      } catch (error) {
        console.error('重新登录失败:', error);
        this.result = '❌ 重新登录失败: ' + error.message;
        
        uni.showToast({
          title: '重新登录失败',
          icon: 'none'
        });
      }
    },
    
    // 测试用户信息更新
    async testUserUpdate() {
      try {
        this.result = '正在测试用户信息更新...';
        
        const userInfo = uni.getStorageSync('userInfo');
        
        if (!userInfo || !userInfo.id) {
          this.result = '❌ 用户信息缺少ID，请先重新登录';
          return;
        }
        
        const testData = {
          display_name: '测试更新 ' + new Date().toLocaleTimeString()
        };
        
        console.log('测试更新数据:', testData);
        console.log('用户ID:', userInfo.id);
        
        const result = await updateUserProfile(userInfo.id, testData);
        
        console.log('更新结果:', result);
        
        if (result.success) {
          this.result = '✅ 用户信息更新成功！\n';
          this.result += `新的显示名称: ${result.data.display_name || testData.display_name}`;
          
          uni.showToast({
            title: '更新成功',
            icon: 'success'
          });
        } else {
          throw new Error(result.message);
        }
      } catch (error) {
        console.error('测试更新失败:', error);
        this.result = '❌ 用户信息更新失败: ' + error.message;
        
        uni.showToast({
          title: '更新失败',
          icon: 'none'
        });
      }
    }
  },
  
  onLoad() {
    // 页面加载时自动检查状态
    this.checkCurrentStatus();
  }
};
</script>

<style scoped>
.container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.btn {
  width: 100%;
  padding: 15px;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 10px;
  border: none;
}

.btn:last-child {
  margin-bottom: 0;
}

.btn.primary {
  background: #007AFF;
  color: white;
}

.btn.danger {
  background: #FF3B30;
  color: white;
}

.btn:not(.primary):not(.danger) {
  background: #f0f0f0;
  color: #333;
}

.btn:active {
  opacity: 0.8;
}

.result {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-top: 20px;
}

.result-title {
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.result-text {
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.5;
}
</style>
