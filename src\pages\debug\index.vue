<template>
  <view class="debug-container">
    <view class="debug-header">
      <text class="debug-title">登录状态调试</text>
    </view>
    
    <view class="debug-section">
      <text class="section-title">存储状态</text>
      <view class="debug-item">
        <text class="label">Token:</text>
        <text class="value">{{ storageInfo.token || '未设置' }}</text>
      </view>
      <view class="debug-item">
        <text class="label">UserInfo:</text>
        <text class="value">{{ storageInfo.userInfoStr }}</text>
      </view>
    </view>
    
    <view class="debug-section">
      <text class="section-title">用户管理工具状态</text>
      <view class="debug-item">
        <text class="label">是否已登录:</text>
        <text class="value">{{ userManagerInfo.isLoggedIn ? '是' : '否' }}</text>
      </view>
      <view class="debug-item">
        <text class="label">当前用户:</text>
        <text class="value">{{ userManagerInfo.currentUserStr }}</text>
      </view>
    </view>
    
    <view class="debug-section">
      <text class="section-title">操作</text>
      <button class="debug-btn" @click="refreshDebugInfo">刷新调试信息</button>
      <button class="debug-btn" @click="clearStorage">清除存储</button>
      <button class="debug-btn" @click="mockLogin">模拟登录</button>
      <button class="debug-btn" @click="goToLogin">跳转登录页</button>
      <button class="debug-btn" @click="goToHome">跳转首页</button>
    </view>
    
    <view class="debug-section">
      <text class="section-title">错误日志</text>
      <view class="error-log">
        <text v-for="(error, index) in errorLog" :key="index" class="error-item">
          {{ error }}
        </text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      storageInfo: {
        token: '',
        userInfoStr: ''
      },
      userManagerInfo: {
        isLoggedIn: false,
        currentUserStr: ''
      },
      errorLog: []
    }
  },
  onLoad() {
    this.refreshDebugInfo();
  },
  methods: {
    async refreshDebugInfo() {
      try {
        // 检查存储状态
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');
        
        this.storageInfo = {
          token: token || '',
          userInfoStr: userInfo ? JSON.stringify(userInfo, null, 2) : '未设置'
        };
        
        // 检查用户管理工具状态
        try {
          const { isUserLoggedIn, getCurrentUserInfo } = await import('@/utils/userManager.js');
          const isLoggedIn = isUserLoggedIn();
          const currentUser = getCurrentUserInfo();
          
          this.userManagerInfo = {
            isLoggedIn,
            currentUserStr: currentUser ? JSON.stringify(currentUser, null, 2) : '未获取到用户信息'
          };
        } catch (error) {
          this.addError('用户管理工具检查失败: ' + error.message);
        }
        
        console.log('调试信息刷新完成');
      } catch (error) {
        this.addError('刷新调试信息失败: ' + error.message);
      }
    },
    
    clearStorage() {
      try {
        uni.removeStorageSync('token');
        uni.removeStorageSync('userInfo');
        uni.showToast({
          title: '存储已清除',
          icon: 'success'
        });
        this.refreshDebugInfo();
      } catch (error) {
        this.addError('清除存储失败: ' + error.message);
      }
    },
    
    async mockLogin() {
      try {
        // 模拟登录数据
        const mockToken = 'mock-token-debug-' + Date.now();
        const mockUserInfo = {
          username: 'debug',
          name: '调试用户',
          displayName: '调试用户',
          role: '测试员',
          employeeId: 'DEBUG001',
          phone: '13800138888',
          avatar: '/static/logo.png',
          email: '<EMAIL>'
        };
        
        uni.setStorageSync('token', mockToken);
        uni.setStorageSync('userInfo', mockUserInfo);
        
        uni.showToast({
          title: '模拟登录成功',
          icon: 'success'
        });
        
        this.refreshDebugInfo();
      } catch (error) {
        this.addError('模拟登录失败: ' + error.message);
      }
    },
    
    goToLogin() {
      uni.reLaunch({
        url: '/pages/login/index'
      });
    },
    
    goToHome() {
      uni.reLaunch({
        url: '/pages/home/<USER>'
      });
    },
    
    addError(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.errorLog.unshift(`[${timestamp}] ${message}`);
      if (this.errorLog.length > 10) {
        this.errorLog.pop();
      }
    }
  }
}
</script>

<style>
.debug-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.debug-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.debug-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.debug-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.debug-item {
  margin-bottom: 15rpx;
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.value {
  font-size: 22rpx;
  color: #333;
  background-color: #f8f9fa;
  padding: 10rpx;
  border-radius: 4rpx;
  word-break: break-all;
}

.debug-btn {
  margin: 10rpx 0;
  background-color: #007AFF;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
}

.debug-btn::after {
  border: none;
}

.error-log {
  max-height: 400rpx;
  overflow-y: auto;
}

.error-item {
  display: block;
  font-size: 22rpx;
  color: #ff4d4f;
  margin-bottom: 10rpx;
  padding: 10rpx;
  background-color: #fff2f0;
  border-radius: 4rpx;
}
</style>
