<template>
  <view class="container">
    <view class="title">新功能演示</view>
    
    <!-- 功能介绍 -->
    <view class="feature-section">
      <text class="section-title">🎯 新增功能</text>
      <view class="feature-list">
        <view class="feature-item">
          <text class="feature-icon">📝</text>
          <view class="feature-content">
            <text class="feature-name">三角拍设备ID输入</text>
            <text class="feature-desc">可以自定义输入设备ID</text>
          </view>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🖼️</text>
          <view class="feature-content">
            <text class="feature-name">仰拍历史界面优化</text>
            <text class="feature-desc">大尺寸缩略图，彩色状态徽章</text>
          </view>
        </view>
        <view class="feature-item">
          <text class="feature-icon">📷</text>
          <view class="feature-content">
            <text class="feature-name">三角拍图片预览</text>
            <text class="feature-desc">三张缩略图，有序浏览</text>
          </view>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🔄</text>
          <view class="feature-content">
            <text class="feature-name">三角拍图片有序浏览</text>
            <text class="feature-desc">按照左中右顺序浏览图片</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 测试按钮 -->
    <view class="test-section">
      <text class="section-title">🚀 功能测试</text>
      <view class="button-list">
        <button class="demo-btn primary" @tap="goToTriangleShot">
          测试三角拍设备ID输入
        </button>
        <button class="demo-btn secondary" @tap="goToUpwardShot">
          测试仰拍界面优化
        </button>
        <button class="demo-btn info" @tap="goToHistory">
          查看历史数据预览
        </button>
        <button class="demo-btn warning" @tap="createTestData">
          创建测试数据
        </button>
      </view>
    </view>
    
    <!-- 使用说明 -->
    <view class="guide-section">
      <text class="section-title">📖 使用说明</text>
      <view class="guide-content">
        <view class="guide-step">
          <text class="step-number">1</text>
          <text class="step-text">进入三角拍页面，输入自定义设备ID</text>
        </view>
        <view class="guide-step">
          <text class="step-number">2</text>
          <text class="step-text">按顺序拍摄左、中、右三张照片</text>
        </view>
        <view class="guide-step">
          <text class="step-number">3</text>
          <text class="step-text">选择检查状态并上传</text>
        </view>
        <view class="guide-step">
          <text class="step-number">4</text>
          <text class="step-text">在历史数据中查看图片缩略图</text>
        </view>
        <view class="guide-step">
          <text class="step-number">5</text>
          <text class="step-text">点击缩略图全屏预览，按顺序浏览</text>
        </view>
      </view>
    </view>
    
    <!-- 状态显示 -->
    <view class="status-section">
      <text class="section-title">📊 功能状态</text>
      <view class="status-list">
        <view class="status-item">
          <text class="status-label">三角拍设备ID输入:</text>
          <text class="status-value success">✅ 已实现</text>
        </view>
        <view class="status-item">
          <text class="status-label">图片缩略图显示:</text>
          <text class="status-value success">✅ 已实现</text>
        </view>
        <view class="status-item">
          <text class="status-label">图片预览功能:</text>
          <text class="status-value success">✅ 已实现</text>
        </view>
        <view class="status-item">
          <text class="status-label">有序图片浏览:</text>
          <text class="status-value success">✅ 已实现</text>
        </view>
        <view class="status-item">
          <text class="status-label">仰拍界面优化:</text>
          <text class="status-value success">✅ 已实现</text>
        </view>
        <view class="status-item">
          <text class="status-label">状态徽章显示:</text>
          <text class="status-value success">✅ 已实现</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DemoFeatures',
  methods: {
    // 跳转到三角拍页面，传递测试设备ID
    goToTriangleShot() {
      uni.navigateTo({
        url: '/pages/triangle-shot/index?deviceId=DEMO_DEVICE_001'
      });
    },

    // 跳转到仰拍页面，传递测试设备ID
    goToUpwardShot() {
      uni.navigateTo({
        url: '/pages/upward-shot/index?deviceId=DEMO_DEVICE_002'
      });
    },

    // 跳转到历史数据页面
    goToHistory() {
      uni.navigateTo({
        url: '/pages/history/index'
      });
    },
    
    // 创建测试数据
    createTestData() {
      uni.showModal({
        title: '创建测试数据',
        content: '这个功能需要实际拍照上传才能创建数据。请使用"测试三角拍设备ID输入"按钮进行实际测试。',
        showCancel: false,
        confirmText: '知道了'
      });
    }
  }
};
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.feature-section,
.test-section,
.guide-section,
.status-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.feature-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.feature-desc {
  font-size: 22rpx;
  color: #666;
}

.button-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.demo-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  color: #fff;
}

.demo-btn.primary {
  background-color: #007AFF;
}

.demo-btn.secondary {
  background-color: #34C759;
}

.demo-btn.info {
  background-color: #FF9500;
}

.demo-btn.warning {
  background-color: #FF3B30;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.guide-step {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.step-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #007AFF;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  margin-right: 15rpx;
}

.step-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 26rpx;
  color: #333;
}

.status-value {
  font-size: 24rpx;
  font-weight: bold;
}

.status-value.success {
  color: #34C759;
}
</style>
