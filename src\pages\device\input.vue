<template>
  <view class="input-container">
    <view class="header">
      <text class="title">输入设备ID</text>
    </view>

    <view class="input-area">
      <input type="text" v-model="deviceId" placeholder="请输入设备ID" class="device-input" />
      <text class="tip">请输入正确的设备ID，确保与设备标签一致</text>
    </view>

    <button class="submit-btn" @click="handleSubmit">确认</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceId: ''
    }
  },
  methods: {
    handleSubmit() {
      if (!this.deviceId) {
        uni.showToast({
          title: '请输入设备ID',
          icon: 'none'
        })
        return
      }
      // TODO: 验证设备ID
      uni.showToast({
        title: '设备ID验证成功',
        icon: 'success'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
}
</script>

<style>
.input-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40rpx;
}

.header {
  margin-bottom: 60rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.input-area {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.device-input {
  height: 88rpx;
  font-size: 32rpx;
  padding: 0 20rpx;
  background-color: #f8f9fc;
  border-radius: 8rpx;
}

.tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  display: block;
}

.submit-btn {
  margin-top: 60rpx;
  background: #007AFF;
  color: #fff;
  border-radius: 12rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
}
</style> 