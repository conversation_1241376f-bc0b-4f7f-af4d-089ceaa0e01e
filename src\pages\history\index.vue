<template>
  <view class="history-container">
    <view class="header">
      <text class="title">历史数据</text>
      <view class="user-info" v-if="currentUser">
        <text class="user-name">{{ currentUser.username }}</text>
        <button class="debug-btn" @click="refreshData">刷新</button>
        <button class="debug-btn" @click="showDebugInfo">调试</button>
        <button class="debug-btn" @click="checkPageStatus">页面状态</button>
        <button class="debug-btn logout" @click="logout">退出</button>
      </view>
    </view>

    <view class="history-list">
      <view
        class="history-item"
        v-for="(item, index) in historyList"
        :key="index"
      >
        <view class="item-header">
          <view class="header-left">
            <text class="device-name">设备ID: {{ item.deviceId }}</text>
            <text class="record-type" v-if="item.type === 'triangle-shot'">三角拍</text>
            <text class="record-type" v-else-if="item.type === 'upward-shot'">仰拍</text>
            <text class="record-type" v-else>普通上传</text>
          </view>
          <text class="date">{{ item.date }}</text>
        </view>
        <view class="item-content">
          <view class="info-row">
            <text class="label">巡检人员：</text>
            <text class="value">{{ item.inspector }}</text>
          </view>
          <view class="info-row">
            <text class="label">巡检状态：</text>
            <view class="status-badge" :class="getStatusClass(item.status)">
              <text class="status-text">{{ item.status }}</text>
            </view>
          </view>
          <view class="info-row" v-if="item.abnormalInfo">
            <text class="label">异常描述：</text>
            <text class="value">{{ item.abnormalInfo }}</text>
          </view>
          <view class="info-row" v-if="item.uploadTimeFormatted || item.uploadTime">
            <text class="label">上传时间：</text>
            <text class="value time-value">{{ item.uploadTimeFormatted || formatUploadTime(item.uploadTime) }}</text>
          </view>
        </view>

        <!-- 图片展示区域 -->
        <view class="image-section" v-if="item.imageUrl || item.photos">
          <!-- 仰拍单张图片 -->
          <view v-if="item.imageUrl && item.type === 'upward-shot'" class="image-container">
            <text class="image-title">📷 仰拍图片</text>
            <view class="single-photo-container">
              <view class="single-photo-item" @click="viewSingleImage(item.imageUrl)">
                <image :src="item.imageUrl" class="single-photo-thumbnail" mode="aspectFill"></image>
                <text class="single-photo-label">点击查看大图</text>
              </view>
            </view>
          </view>

          <!-- 三角拍多张图片 -->
          <view v-if="item.photos && item.type === 'triangle-shot'" class="image-container">
            <text class="image-title">📷 三角拍图片</text>
            <view class="photos-container">
              <view
                class="photo-item"
                v-if="item.photos.left"
                @click="viewTriangleImages(item.photos, 0)"
              >
                <image :src="item.photos.left" class="photo-thumbnail" mode="aspectFill"></image>
                <text class="photo-label">左</text>
              </view>
              <view
                class="photo-item"
                v-if="item.photos.center"
                @click="viewTriangleImages(item.photos, 1)"
              >
                <image :src="item.photos.center" class="photo-thumbnail" mode="aspectFill"></image>
                <text class="photo-label">中</text>
              </view>
              <view
                class="photo-item"
                v-if="item.photos.right"
                @click="viewTriangleImages(item.photos, 2)"
              >
                <image :src="item.photos.right" class="photo-thumbnail" mode="aspectFill"></image>
                <text class="photo-label">右</text>
              </view>
            </view>
            <text class="photos-tip">点击图片查看大图，可左右滑动浏览</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getPhotoList } from "@/api/inspection";

export default {
  data() {
    return {
      historyList: [],
      loading: false,
      page: 1,
      limit: 20,
      hasMore: true,
      currentUser: null,
    };
  },
  onLoad() {
    console.log('历史数据页面加载');
    // 延迟检查，避免与导航栏切换冲突
    setTimeout(() => {
      this.checkLoginAndLoadData();
    }, 100);

    // 监听数据更新事件
    uni.$on('dataUpdated', this.handleDataUpdated);
  },
  onShow() {
    // 页面显示时只刷新数据，不重新检查登录
    console.log('历史数据页面onShow');
    this.refreshDataSafely();
  },
  onUnload() {
    // 移除事件监听
    uni.$off('dataUpdated', this.handleDataUpdated);
  },
  onPullDownRefresh() {
    this.page = 1;
    this.hasMore = true;
    this.loadHistoryData().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.page++;
      this.loadHistoryData();
    }
  },
  methods: {
    async checkLoginAndLoadData() {
      try {
        // 直接检查存储，避免模块导入问题
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        console.log('历史页面登录检查 - Token:', token ? '已设置' : '未设置');
        console.log('历史页面登录检查 - UserInfo:', userInfo ? '已设置' : '未设置');

        if (!token || !userInfo) {
          console.log('历史页面检测到未登录状态');
          // 不立即跳转，给用户选择
          uni.showModal({
            title: '登录提示',
            content: '需要登录才能查看历史数据，是否前往登录？',
            success: (res) => {
              if (res.confirm) {
                uni.reLaunch({
                  url: '/pages/login/index'
                });
              } else {
                // 用户选择不登录，跳转到首页
                uni.switchTab({
                  url: '/pages/home/<USER>'
                });
              }
            }
          });
          return;
        }

        console.log(`用户 ${userInfo.username || userInfo.displayName} 已登录，加载历史数据`);
        this.currentUser = userInfo;
        this.loadHistoryData();
      } catch (error) {
        console.error('检查登录状态失败:', error);
        // 给用户选择是否重新登录
        uni.showModal({
          title: '提示',
          content: '登录状态检查失败，是否重新登录？',
          success: (res) => {
            if (res.confirm) {
              uni.reLaunch({
                url: '/pages/login/index'
              });
            }
          }
        });
      }
    },
    // 安全地刷新数据
    async refreshDataSafely() {
      try {
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        if (token && userInfo) {
          console.log('历史页面onShow - 刷新数据');
          this.currentUser = userInfo;
          // 重置分页并刷新
          this.page = 1;
          this.hasMore = true;
          this.historyList = [];
          await this.loadHistoryData();
        }
      } catch (error) {
        console.error('安全刷新数据失败:', error);
      }
    },
    // 检查页面状态
    checkPageStatus() {
      try {
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        let statusInfo = '📊 历史数据页面状态\n\n';
        statusInfo += `🔐 登录状态:\n`;
        statusInfo += `Token: ${token ? '✅ 已设置' : '❌ 未设置'}\n`;
        statusInfo += `UserInfo: ${userInfo ? '✅ 已设置' : '❌ 未设置'}\n\n`;

        if (userInfo) {
          statusInfo += `👤 当前用户: ${userInfo.username || userInfo.displayName}\n`;
        }

        statusInfo += `📋 页面数据:\n`;
        statusInfo += `历史记录数量: ${this.historyList.length}\n`;
        statusInfo += `当前页码: ${this.page}\n`;
        statusInfo += `是否还有更多: ${this.hasMore ? '是' : '否'}\n\n`;

        statusInfo += `🔄 页面生命周期:\n`;
        statusInfo += `• onLoad: 延迟100ms检查登录\n`;
        statusInfo += `• onShow: 安全刷新数据\n`;
        statusInfo += `• 登录检查: 显示确认对话框\n\n`;

        statusInfo += `💡 如果有问题:\n`;
        statusInfo += `• 点击"刷新"重新加载数据\n`;
        statusInfo += `• 点击"调试"查看详细信息\n`;
        statusInfo += `• 点击"退出"重新登录`;

        uni.showModal({
          title: '页面状态',
          content: statusInfo,
          showCancel: true,
          cancelText: '关闭',
          confirmText: '刷新数据',
          success: (res) => {
            if (res.confirm) {
              this.refreshData();
            }
          }
        });
      } catch (error) {
        uni.showToast({
          title: '获取状态失败',
          icon: 'none'
        });
      }
    },
    async initializeData() {
      try {
        console.log('开始初始化历史数据');

        // 不需要初始化模拟数据，直接从后端获取
        console.log('开始从后端获取历史数据');

        // 加载历史数据
        await this.loadHistoryData();
      } catch (error) {
        console.error('初始化数据失败:', error);
        uni.showToast({
          title: '数据初始化失败',
          icon: 'none'
        });
      }
    },
    async loadHistoryData() {
      if (this.loading) return;

      console.log('开始加载历史数据，页码:', this.page);
      this.loading = true;

      try {
        const response = await getPhotoList({
          page: this.page,
          limit: this.limit,
        });

        console.log('获取历史数据响应:', response);

        if (response.success) {
          const { photos, pagination } = response.data;
          console.log('获取到照片数量:', photos.length);

          // 处理图片 URL，使用本地路径
          const processedPhotos = photos.map((photo) => ({
            ...photo,
            imageUrl: photo.url || photo.imageUrl || null,
          }));

          if (this.page === 1) {
            this.historyList = processedPhotos;
          } else {
            this.historyList = [...this.historyList, ...processedPhotos];
          }

          this.hasMore = this.historyList.length < pagination.total;
          console.log('历史列表更新完成，总数量:', this.historyList.length);
        } else {
          console.error('获取历史数据失败:', response.message);
          uni.showToast({
            title: response.message || "获取历史数据失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error('加载历史数据异常:', error);
        uni.showToast({
          title: error.message || "获取历史数据失败",
          icon: "none",
        });
      } finally {
        this.loading = false;
      }
    },
    // 查看单张图片（仰拍）
    viewSingleImage(imageUrl) {
      if (!imageUrl) {
        uni.showToast({
          title: "暂无图片",
          icon: "none",
        });
        return;
      }

      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl,
      });
    },

    // 查看三角拍图片（按左中右顺序）
    viewTriangleImages(photos, startIndex = 0) {
      if (!photos) {
        uni.showToast({
          title: "暂无图片",
          icon: "none",
        });
        return;
      }

      // 按照左中右的顺序构建图片数组
      const orderedUrls = [];
      const orderedLabels = [];

      if (photos.left) {
        orderedUrls.push(photos.left);
        orderedLabels.push('左侧照片');
      }
      if (photos.center) {
        orderedUrls.push(photos.center);
        orderedLabels.push('中间照片');
      }
      if (photos.right) {
        orderedUrls.push(photos.right);
        orderedLabels.push('右侧照片');
      }

      if (orderedUrls.length === 0) {
        uni.showToast({
          title: "暂无图片",
          icon: "none",
        });
        return;
      }

      // 确保startIndex在有效范围内
      const currentIndex = Math.min(startIndex, orderedUrls.length - 1);

      uni.previewImage({
        urls: orderedUrls,
        current: orderedUrls[currentIndex],
      });
    },
    async debugInitData() {
      try {
        console.log('手动初始化测试数据');

        // 清空现有数据
        uni.removeStorageSync('local_photos');

        // 重新从后端获取数据
        console.log('重新从后端获取数据');

        // 重新加载页面数据
        this.page = 1;
        this.hasMore = true;
        await this.loadHistoryData();

        uni.showToast({
          title: '测试数据初始化成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('手动初始化失败:', error);
        uni.showToast({
          title: '初始化失败: ' + error.message,
          icon: 'none'
        });
      }
    },
    showDebugInfo() {
      try {
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        // 获取当前用户的照片数据
        const currentUserKey = userInfo ? `local_photos_${userInfo.username}` : 'local_photos';
        const currentUserPhotos = uni.getStorageSync(currentUserKey) || [];

        // 获取所有存储的key，查看数据隔离情况
        const allKeys = [];
        try {
          // 尝试获取一些常见的用户数据
          const adminPhotos = uni.getStorageSync('local_photos_admin') || [];
          const userPhotos = uni.getStorageSync('local_photos_user') || [];
          const defaultPhotos = uni.getStorageSync('local_photos') || [];

          if (adminPhotos.length > 0) allKeys.push(`admin: ${adminPhotos.length}条`);
          if (userPhotos.length > 0) allKeys.push(`user: ${userPhotos.length}条`);
          if (defaultPhotos.length > 0) allKeys.push(`default: ${defaultPhotos.length}条`);
        } catch (e) {
          console.log('获取其他用户数据失败:', e);
        }

        const debugInfo = {
          '当前用户': userInfo ? userInfo.username : '未登录',
          '当前用户照片数量': currentUserPhotos.length,
          '页面显示数量': this.historyList.length,
          '存储Key': currentUserKey,
          '其他用户数据': allKeys.length > 0 ? allKeys.join(', ') : '无',
          '登录Token': token ? '已设置' : '未设置',
          '当前页码': this.page,
          '是否还有更多': this.hasMore
        };

        let message = '调试信息:\n';
        for (const [key, value] of Object.entries(debugInfo)) {
          message += `${key}: ${value}\n`;
        }

        console.log('调试信息:', debugInfo);
        console.log('当前用户照片数据:', currentUserPhotos);

        uni.showModal({
          title: '调试信息',
          content: message,
          showCancel: false
        });
      } catch (error) {
        console.error('获取调试信息失败:', error);
        uni.showToast({
          title: '获取调试信息失败',
          icon: 'none'
        });
      }
    },
    logout() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除登录信息
            uni.removeStorageSync('token');
            uni.removeStorageSync('userInfo');

            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            });

            // 跳转到登录页面
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/login/index'
              });
            }, 1500);
          }
        }
      });
    },
    async refreshData() {
      try {
        console.log('手动刷新历史数据');

        // 重置分页
        this.page = 1;
        this.hasMore = true;
        this.historyList = [];

        // 重新加载数据
        await this.loadHistoryData();

        uni.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1000
        });
      } catch (error) {
        console.error('刷新数据失败:', error);
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      }
    },
    // 格式化上传时间
    formatUploadTime(uploadTime) {
      if (!uploadTime) return '';

      try {
        const date = new Date(uploadTime);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        console.error('时间格式化失败:', error);
        return uploadTime;
      }
    },

    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case '正常':
          return 'status-normal';
        case '异常':
          return 'status-abnormal';
        case '未知':
          return 'status-unknown';
        default:
          return 'status-default';
      }
    },

    // 处理数据更新事件
    handleDataUpdated(eventData) {
      console.log('历史数据页面收到数据更新事件:', eventData);

      // 延迟一点刷新，确保数据已经保存
      setTimeout(() => {
        console.log('开始刷新历史数据');
        this.refreshData();
      }, 200);
    },
  },
};
</script>

<style>
.history-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.debug-controls {
  display: flex;
  gap: 10rpx;
}

.debug-btn {
  padding: 10rpx 20rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.debug-btn.secondary {
  background-color: #666;
}

.debug-btn.logout {
  background-color: #ff3b30;
}

.history-list {
  margin-top: 20rpx;
}

.history-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.device-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.record-type {
  font-size: 22rpx;
  color: #007aff;
  background-color: #e6f3ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}

.date {
  font-size: 24rpx;
  color: #999;
}

.item-content {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
}

.value {
  font-size: 26rpx;
  color: #333;
}

.value.normal {
  color: #07c160;
}

.value.abnormal {
  color: #ff4d4f;
}

.view-image-link {
  color: #007aff;
  text-decoration: underline;
  cursor: pointer;
}

.time-value {
  color: #007aff;
  font-weight: 500;
}

/* 图片相关样式 */
.photos-container {
  display: flex;
  gap: 15rpx;
  margin-top: 10rpx;
  flex-wrap: wrap;
}

.photo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.photo-thumbnail {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
}

.photo-label {
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
  text-align: center;
}

.photo-item:active .photo-thumbnail {
  opacity: 0.7;
}

.photos-tip {
  font-size: 22rpx;
  color: #007AFF;
  text-align: center;
  margin-top: 15rpx;
  font-weight: 500;
}

/* 仰拍单张图片样式 */
.single-photo-container {
  margin-top: 10rpx;
}

.single-photo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  width: 160rpx;
}

.single-photo-thumbnail {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.single-photo-label {
  font-size: 24rpx;
  color: #007AFF;
  margin-top: 10rpx;
  text-align: center;
  font-weight: 500;
}

.single-photo-item:active .single-photo-thumbnail {
  opacity: 0.7;
  transform: scale(0.95);
  transition: all 0.2s ease;
}

/* 图片展示区域样式 */
.image-section {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f5f5f5;
}

.image-container {
  background-color: #fafafa;
  border-radius: 8rpx;
  padding: 16rpx;
  border: 1rpx solid #f0f0f0;
}

.image-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

/* 优化历史记录项的整体布局 */
.history-item {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
}

/* 优化信息行的间距 */
.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.info-row:last-child {
  margin-bottom: 0;
}

/* 状态徽章样式 */
.status-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  text-align: center;
  min-width: 60rpx;
  display: inline-block;
}

.status-badge.status-normal {
  background-color: #f0f9f0;
  color: #52c41a;
  border: 1rpx solid #d9f7be;
}

.status-badge.status-abnormal {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1rpx solid #ffccc7;
}

.status-badge.status-unknown {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1rpx solid #ffd591;
}

.status-badge.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1rpx solid #e8e8e8;
}

.status-text {
  font-size: 20rpx;
  font-weight: 500;
}
</style>
