<template>
  <!-- 整个首页容器 -->
  <view class="home-container">
    <!-- 用户信息区域 - 显示用户头像、用户名和统计信息 -->
    <view class="user-info">
      <view class="user-profile">
        <!-- 用户头像图片 -->
        <view class="avatar-wrapper" @click="goToProfile" @tap="goToProfile">
          <image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'"></image>
        </view>
        <view class="user-details">
          <!-- 用户名显示 -->
          <text class="username">{{ userInfo.displayName || userInfo.username || '未登录' }}</text>
          <text class="user-role">{{ userInfo.role || '巡检员' }}</text>
        </view>
        <view class="user-actions">
          <button class="profile-btn" @click="goToProfile" @tap="goToProfile">个人中心</button>
        </view>
      </view>

      <!-- 统计信息区域 - 包含巡检次数等统计数据 -->
      <view class="stats">
        <!-- 本月巡检总次数统计项 -->
        <view class="stat-item">
          <!-- 数字显示 -->
          <text class="number">{{ stats.thisMonth }}</text>
          <!-- 标签描述 -->
          <text class="label">本月巡检总次数</text>
        </view>
        <!-- 本月异常次数统计项 -->
        <view class="stat-item">
          <text class="number">{{ stats.abnormal }}</text>
          <text class="label">本月异常次数</text>
        </view>
        <!-- 正常率统计项 -->
        <view class="stat-item">
          <text class="number">{{ stats.normalRate }}%</text>
          <text class="label">正常率</text>
        </view>
      </view>
    </view>

    <!-- 主要功能区域 -->
    <view class="main-functions">
      <text class="section-title">主要功能</text>
      <view class="primary-buttons">
        <!-- 扫一扫按钮 -->
        <button class="primary-btn scan-btn" @click="scanCode">
          <view class="btn-content">
            <text class="btn-icon">📱</text>
            <text class="btn-title">扫一扫</text>
            <text class="btn-desc">扫描设备二维码</text>
          </view>
        </button>

      </view>
    </view>

    <!-- 拍照功能区域 -->
    <view class="photo-functions">
      <text class="section-title">拍照检查</text>
      <view class="photo-buttons">
        <!-- 三角拍按钮 -->
        <button class="photo-btn triangle-btn" @click="goToTriangleShot">
          <view class="photo-btn-content">
            <text class="photo-icon">📷</text>
            <text class="photo-title">三角拍</text>
          </view>
        </button>
        <!-- 仰拍按钮 -->
        <button class="photo-btn upward-btn" @click="goToUpwardShot">
          <view class="photo-btn-content">
            <text class="photo-icon">📸</text>
            <text class="photo-title">仰拍</text>
          </view>
        </button>
      </view>
    </view>

    <!-- 调试功能区域 -->
    <view class="debug-functions" v-if="showDebugBtn">
      <view class="debug-buttons">
        <!-- 调试按钮 -->
        <button class="debug-btn" @click="showDebugMenu">
          <text class="debug-icon">🔧</text>
          <text class="debug-text">调试功能</text>
        </button>
      </view>
    </view>


  </view>
</template>

<script>
// 导入图标资源
import { icons } from "@/static/icons.js";
import { addMockPhotos } from "@/api/photo";

export default {
  // 定义组件的数据
  data() {
    return {
      // 用户信息对象 - 包含头像和用户名
      userInfo: {
        avatar: "/static/logo.png", // 默认头像路径
        username: "加载中...", // 默认用户名
      },
      // 统计数据
      stats: {
        thisMonth: 0,
        abnormal: 0,
        normalRate: 100
      },
      // 是否显示调试按钮
      showDebugBtn: process.env.NODE_ENV === 'development',
      icons, // 图标资源对象
    };
  },
  onLoad() {
    // 检查登录状态并加载用户信息
    this.checkLoginAndLoadData();

    // 监听数据更新事件
    uni.$on('dataUpdated', this.handleDataUpdated);
    // 监听用户信息更新事件
    uni.$on('userInfoUpdated', this.handleUserInfoUpdated);
  },
  onShow() {
    // 页面显示时刷新数据和统计信息
    console.log('首页onShow - 刷新用户数据和统计信息');
    this.loadUserDataSafely();
    this.refreshStats();

    // 检查是否有数据更新标记
    this.checkDataUpdateFlag();
  },
  onUnload() {
    // 移除事件监听
    uni.$off('dataUpdated', this.handleDataUpdated);
    uni.$off('userInfoUpdated', this.handleUserInfoUpdated);
  },
  // 定义组件的方法
  methods: {
    // 检查登录状态并加载数据
    checkLoginAndLoadData() {
      try {
        // 直接检查存储，避免模块导入问题
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        console.log('首页登录检查 - Token:', token ? '已设置' : '未设置');
        console.log('首页登录检查 - UserInfo:', userInfo ? '已设置' : '未设置');

        if (!token || !userInfo) {
          console.log('用户未登录，跳转到登录页面');
          uni.reLaunch({
            url: '/pages/login/index'
          });
          return;
        }

        console.log('用户已登录，加载首页数据');
        // 加载用户数据
        this.loadUserData();
      } catch (error) {
        console.error('检查登录状态失败:', error);
        // 不要立即跳转，给用户一个提示
        uni.showModal({
          title: '提示',
          content: '登录状态检查失败，是否重新登录？',
          success: (res) => {
            if (res.confirm) {
              uni.reLaunch({
                url: '/pages/login/index'
              });
            }
          }
        });
      }
    },
    // 加载用户数据
    async loadUserData() {
      try {
        const { getCurrentUserInfo, getUserStats } = await import('@/utils/userManager.js');

        // 获取用户信息
        const userInfo = getCurrentUserInfo();
        if (userInfo) {
          this.userInfo = {
            avatar: userInfo.avatar,
            username: userInfo.displayName
          };
          console.log('用户信息加载成功:', this.userInfo);
        }

        // 获取统计数据
        const stats = getUserStats();
        this.stats = {
          thisMonth: stats.thisMonth.total,
          abnormal: stats.thisMonth.abnormal,
          normalRate: stats.thisMonth.normalRate
        };
        console.log('统计数据加载成功:', this.stats);
      } catch (error) {
        console.error('加载用户数据失败:', error);
      }
    },
    // 安全地加载用户数据（不检查登录状态）
    async loadUserDataSafely() {
      try {
        // 直接检查存储
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        if (!token || !userInfo) {
          console.log('onShow时发现未登录，但不跳转');
          return;
        }

        // 加载用户数据
        await this.loadUserData();
      } catch (error) {
        console.error('安全加载用户数据失败:', error);
        // 不做任何跳转，只记录错误
      }
    },

    // 刷新统计数据
    async refreshStats() {
      try {
        console.log('=== 开始刷新统计数据 ===');
        const { getUserStats } = await import('@/utils/userManager.js');
        const stats = getUserStats();

        console.log('从userManager获取的原始统计数据:', stats);

        const newStats = {
          thisMonth: stats.thisMonth.total,
          abnormal: stats.thisMonth.abnormal,
          normalRate: stats.thisMonth.normalRate
        };

        console.log('处理后的统计数据:', newStats);
        console.log('更新前的this.stats:', this.stats);

        this.stats = newStats;

        console.log('更新后的this.stats:', this.stats);
        console.log('=== 统计数据刷新完成 ===');
      } catch (error) {
        console.error('刷新统计数据失败:', error);
      }
    },
    // 显示调试菜单
    showDebugMenu() {
      uni.showActionSheet({
        itemList: ['查看调试信息', '存储管理', '清空历史数据', '重置统计数据', '清除所有缓存'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.showDebugInfo();
              break;
            case 1:
              this.showStorageManager();
              break;
            case 2:
              this.clearHistoryData();
              break;
            case 3:
              this.resetStats();
              break;
            case 4:
              this.clearAllCache();
              break;
          }
        }
      });
    },

    // 显示调试信息
    showDebugInfo() {
      try {
        const token = uni.getStorageSync('token');
        const userInfo = uni.getStorageSync('userInfo');

        let debugInfo = '🔧 登录状态调试信息\n\n';
        debugInfo += `Token: ${token ? '✅ 已设置' : '❌ 未设置'}\n`;
        debugInfo += `UserInfo: ${userInfo ? '✅ 已设置' : '❌ 未设置'}\n\n`;

        if (userInfo) {
          debugInfo += '👤 用户信息:\n';
          debugInfo += `用户名: ${userInfo.username || '未设置'}\n`;
          debugInfo += `显示名: ${userInfo.displayName || userInfo.name || '未设置'}\n`;
          debugInfo += `角色: ${userInfo.role || '未设置'}\n`;
          debugInfo += `工号: ${userInfo.employeeId || '未设置'}\n`;
        }

        debugInfo += '\n📊 统计数据:\n';
        debugInfo += `本月巡检: ${this.stats.thisMonth}\n`;
        debugInfo += `异常次数: ${this.stats.abnormal}\n`;
        debugInfo += `正常率: ${this.stats.normalRate}%\n`;

        // 检查历史数据
        let historyCount = 0;
        if (userInfo) {
          const storageKey = `local_photos_${userInfo.username || 'default'}`;
          const userPhotos = uni.getStorageSync(storageKey) || [];
          historyCount = userPhotos.length;
        }
        debugInfo += `\n📋 历史记录数量: ${historyCount}\n`;

        uni.showModal({
          title: '调试信息',
          content: debugInfo,
          showCancel: false,
          confirmText: '确定'
        });
      } catch (error) {
        uni.showToast({
          title: '获取调试信息失败',
          icon: 'none'
        });
      }
    },

    // 显示存储管理器
    async showStorageManager() {
      try {
        uni.showLoading({
          title: '获取存储信息...'
        });

        const { getStorageInfo } = await import('@/api/photo.js');
        const storageInfo = await getStorageInfo();

        uni.hideLoading();

        if (storageInfo.success) {
          const data = storageInfo.data;

          uni.showActionSheet({
            itemList: [
              `查看详情 (${data.imageCount} 张图片, ${data.estimatedSizeMB} MB)`,
              '清理图片文件',
              '查看文件列表'
            ],
            success: (res) => {
              switch (res.tapIndex) {
                case 0:
                  this.showStorageDetails(data);
                  break;
                case 1:
                  this.cleanImageFiles();
                  break;
                case 2:
                  this.showFileList(data.files);
                  break;
              }
            }
          });
        } else {
          uni.showToast({
            title: '获取存储信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('显示存储管理器失败:', error);
        uni.showToast({
          title: '存储管理功能加载失败',
          icon: 'none'
        });
      }
    },

    // 显示存储详情
    showStorageDetails(data) {
      const details = [
        `图片数量: ${data.imageCount} 张`,
        `估计大小: ${data.estimatedSizeMB} MB`,
        `平均大小: ${data.imageCount > 0 ? (data.estimatedSize / data.imageCount / 1024).toFixed(1) : 0} KB/张`,
        `运行环境: ${data.environment || '未知'}`,
        `存储方式: ${data.note || '标准存储'}`
      ].join('\n');

      uni.showModal({
        title: '存储详情',
        content: details,
        showCancel: false,
        confirmText: '确定'
      });
    },

    // 清理图片文件
    async cleanImageFiles() {
      try {
        const { getStorageInfo } = await import('@/api/photo.js');
        const storageInfo = await getStorageInfo();

        if (!storageInfo.success || storageInfo.data.imageCount === 0) {
          uni.showToast({
            title: '没有图片文件需要清理',
            icon: 'none'
          });
          return;
        }

        uni.showModal({
          title: '清理图片文件',
          content: `确定要删除所有 ${storageInfo.data.imageCount} 张图片文件吗？\n\n这将释放约 ${storageInfo.data.estimatedSizeMB} MB 空间，但会清空所有历史记录中的图片。`,
          success: async (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: '清理中...'
              });

              try {
                const { clearAllPermanentImages } = await import('@/api/photo.js');
                const clearResult = await clearAllPermanentImages();

                uni.hideLoading();

                if (clearResult.success) {
                  uni.showToast({
                    title: `清理完成，删除了 ${clearResult.count} 个文件`,
                    icon: 'success',
                    duration: 3000
                  });

                  // 刷新统计数据
                  this.refreshStats();

                  // 通知其他页面数据已清除
                  uni.$emit('dataCleared');
                } else {
                  uni.showToast({
                    title: '清理失败: ' + clearResult.message,
                    icon: 'none'
                  });
                }
              } catch (error) {
                uni.hideLoading();
                console.error('清理图片文件失败:', error);
                uni.showToast({
                  title: '清理失败: ' + error.message,
                  icon: 'none'
                });
              }
            }
          }
        });
      } catch (error) {
        console.error('清理图片文件失败:', error);
        uni.showToast({
          title: '清理功能加载失败',
          icon: 'none'
        });
      }
    },

    // 显示文件列表
    showFileList(files) {
      if (!files || files.length === 0) {
        uni.showToast({
          title: '没有图片文件',
          icon: 'none'
        });
        return;
      }

      const fileList = files.slice(0, 10).map((file, index) =>
        `${index + 1}. ${file}`
      ).join('\n');

      const content = files.length > 10
        ? fileList + `\n... 还有 ${files.length - 10} 个文件`
        : fileList;

      uni.showModal({
        title: `文件列表 (${files.length} 个)`,
        content: content,
        showCancel: false,
        confirmText: '确定'
      });
    },

    // 清空历史数据
    clearHistoryData() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有历史数据吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            try {
              // 获取当前用户信息
              const userInfo = uni.getStorageSync('userInfo');

              if (userInfo) {
                // 清空当前用户的照片数据
                const storageKey = `local_photos_${userInfo.username || 'default'}`;
                uni.removeStorageSync(storageKey);
                console.log(`已清空用户 ${userInfo.username} 的历史数据`);
              }

              // 同时清空旧的historyData（兼容性）
              uni.removeStorageSync('historyData');

              uni.showToast({
                title: '历史数据已清空',
                icon: 'success'
              });

              console.log('历史数据已清空');
            } catch (error) {
              console.error('清空历史数据失败:', error);
              uni.showToast({
                title: '清空失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },

    // 重置统计数据
    resetStats() {
      uni.showModal({
        title: '确认重置',
        content: '确定要重置统计数据吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            try {
              // 重置统计数据
              this.stats = {
                thisMonth: 0,
                abnormal: 0,
                normalRate: 0
              };

              // 清空相关存储
              uni.removeStorageSync('userStats');

              uni.showToast({
                title: '统计数据已重置',
                icon: 'success'
              });

              console.log('统计数据已重置');
            } catch (error) {
              console.error('重置统计数据失败:', error);
              uni.showToast({
                title: '重置失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },

    // 清除所有缓存
    clearAllCache() {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除所有缓存数据吗？这将保留登录信息，但清除其他所有数据。',
        success: (res) => {
          if (res.confirm) {
            try {
              // 保留登录信息
              const token = uni.getStorageSync('token');
              const userInfo = uni.getStorageSync('userInfo');

              // 清除所有存储
              uni.clearStorageSync();

              // 恢复登录信息
              if (token) uni.setStorageSync('token', token);
              if (userInfo) uni.setStorageSync('userInfo', userInfo);

              // 重置页面数据
              this.stats = {
                thisMonth: 0,
                abnormal: 0,
                normalRate: 0
              };

              uni.showToast({
                title: '缓存已清除',
                icon: 'success'
              });

              console.log('所有缓存已清除');
            } catch (error) {
              console.error('清除缓存失败:', error);
              uni.showToast({
                title: '清除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    // 清除存储并重新登录
    clearStorageAndRelogin() {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除所有存储数据并重新登录吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              uni.clearStorageSync();
              uni.showToast({
                title: '存储已清除',
                icon: 'success'
              });
              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/login/index'
                });
              }, 1500);
            } catch (error) {
              uni.showToast({
                title: '清除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    // 调用扫码功能的方法
    scanCode() {
      try {
        // 调用uni-app的扫码API
        uni.scanCode({
          success: (res) => {
            // 扫码成功的回调
            console.log("扫码结果：", res);
            // TODO: 处理扫码结果
          },
          fail: (err) => {
            // 扫码失败的回调
            console.error("扫码失败:", err);
            uni.showToast({
              title: "扫码失败",
              icon: "none",
            });
          },
        });
      } catch (error) {
        // 调用扫码API出现异常的处理
        console.error("调用扫码API失败:", error);
        uni.showToast({
          title: "无法调用扫码功能",
          icon: "none",
        });
      }
    },
    // 跳转到三角拍页面的方法
    goToTriangleShot() {
      try {
        // H5环境下的跳转处理 - 使用window.location
        // #ifdef H5
        if (typeof window !== "undefined" && window.location) {
          window.location.href = "#/pages/triangle-shot/index";
        } else {
          // #endif

          // 非H5环境下的跳转处理 - 使用uni.navigateTo
          // #ifndef H5
          uni.navigateTo({
            url: "/pages/triangle-shot/index",
            fail: (err) => {
              // 导航失败时的错误处理
              console.error("导航失败:", err);
              uni.showToast({
                title: "页面跳转失败",
                icon: "none",
              });
            },
          });
          // #endif
          // H5环境下的结束标记
          // #ifdef H5
        }
        // #endif
      } catch (error) {
        // 跳转过程中出现异常的处理
        console.error("页面跳转失败:", error);
      }
    },
    // 跳转到仰拍页面的方法
    goToUpwardShot() {
      try {
        // H5环境下的跳转处理 - 使用window.location
        // #ifdef H5
        if (typeof window !== "undefined" && window.location) {
          window.location.href = "#/pages/upward-shot/index";
        } else {
          // #endif

          // 非H5环境下的跳转处理 - 使用uni.navigateTo
          // #ifndef H5
          uni.navigateTo({
            url: "/pages/upward-shot/index",
            fail: (err) => {
              // 导航失败时的错误处理
              console.error("导航失败:", err);
              uni.showToast({
                title: "页面跳转失败",
                icon: "none",
              });
            },
          });
          // #endif
          // H5环境下的结束标记
          // #ifdef H5
        }
        // #endif
      } catch (error) {
        // 跳转过程中出现异常的处理
        console.error("页面跳转失败:", error);
      }
    },

    // 跳转到个人中心
    goToProfile() {
      console.log('点击跳转到个人中心');

      uni.switchTab({
        url: '/pages/profile/index'
      });
    },

    // 处理数据更新事件
    handleDataUpdated(eventData) {
      console.log('首页收到数据更新事件:', eventData);

      // 延迟一点刷新，确保数据已经保存
      setTimeout(() => {
        console.log('开始刷新首页统计数据');
        this.refreshStats();

        uni.showToast({
          title: '数据已同步',
          icon: 'success',
          duration: 1500
        });
      }, 200);
    },

    // 处理用户信息更新事件
    handleUserInfoUpdated(updatedUserInfo) {
      console.log('首页收到用户信息更新事件:', updatedUserInfo);

      // 更新页面显示的用户信息
      this.userInfo = {
        ...this.userInfo,
        ...updatedUserInfo
      };

      console.log('首页用户信息已同步更新');
    },

    // 检查数据更新标记
    checkDataUpdateFlag() {
      try {
        const updateFlag = uni.getStorageSync('dataUpdateFlag');
        if (updateFlag) {
          console.log('检测到数据更新标记，刷新统计数据');
          this.refreshStats();
          // 清除标记
          uni.removeStorageSync('dataUpdateFlag');
        }
      } catch (error) {
        console.error('检查数据更新标记失败:', error);
      }
    },
  },
};
</script>

<style>
/* 首页容器样式 */
.home-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 60rpx;
}

/* 用户信息区域样式 */
.user-info {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  margin: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
}

.user-profile {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

/* 头像包装器样式 */
.avatar-wrapper {
  margin-right: 24rpx;
  cursor: pointer;
  pointer-events: auto;
  z-index: 10;
  transition: all 0.2s ease;
}

.avatar-wrapper:active {
  opacity: 0.7;
  transform: scale(0.95);
}

/* 头像样式 */
.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 3rpx solid #f0f0f0;
  display: block;
}



.user-details {
  flex: 1;
}

/* 用户名样式 */
.username {
  font-size: 36rpx;
  color: #333333;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.user-role {
  font-size: 24rpx;
  color: #666666;
  background-color: #f8f9fa;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.user-actions {
  margin-left: 20rpx;
}

.profile-btn {
  background-color: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  cursor: pointer;
  pointer-events: auto;
  z-index: 10;
  transition: all 0.2s ease;
}

.profile-btn:active {
  background-color: #0056CC;
  transform: scale(0.95);
}

/* 统计区域样式 */
.stats {
  display: flex;
  gap: 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

/* 统计项样式 */
.stat-item {
  flex: 1;
  text-align: center;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

/* 数字样式 */
.number {
  font-size: 40rpx;
  color: #007AFF;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

/* 标签样式 */
.label {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;
}

/* 主要功能区域 */
.main-functions {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  display: block;
}

.primary-buttons {
  padding: 0 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  align-items: center;
}

.primary-btn {
  background-color: #ffffff;
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 30rpx 40rpx;
  transition: all 0.3s ease;
  width: 80%;
  max-width: 500rpx;
}

.primary-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
}

.btn-content {
  display: flex;
  align-items: center;
}

.btn-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.btn-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 6rpx;
  display: block;
}

.btn-desc {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

.scan-btn .btn-icon {
  color: #007AFF;
}



/* 拍照功能区域 */
.photo-functions {
  margin-bottom: 40rpx;
}

.photo-buttons {
  padding: 0 20rpx;
  display: flex;
  gap: 20rpx;
}

.photo-btn {
  flex: 1;
  background-color: #ffffff;
  border-radius: 16rpx;
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx 20rpx;
  transition: all 0.3s ease;
}

.photo-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
}

.photo-btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.photo-icon {
  font-size: 56rpx;
  margin-bottom: 16rpx;
}

.photo-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.triangle-btn .photo-icon {
  color: #FF6B6B;
}

.upward-btn .photo-icon {
  color: #4ECDC4;
}

/* 调试功能区域 */
.debug-functions {
  margin-bottom: 40rpx;
}

.debug-buttons {
  padding: 0 20rpx;
  display: flex;
  justify-content: center;
}

.debug-btn {
  background-color: #fff3cd;
  border-radius: 12rpx;
  border: 1rpx solid #ffeaa7;
  padding: 20rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.2s ease;
  min-width: 200rpx;
}

.debug-btn:active {
  background-color: #ffeaa7;
  transform: translateY(1rpx);
}

.debug-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  color: #856404;
}

.debug-text {
  font-size: 24rpx;
  color: #856404;
  font-weight: 500;
}

/* 调试按钮样式 */
.debug-btn {
  background-color: #ff9500 !important;
  color: #fff !important;
  font-size: 28rpx !important;
}


</style>
