<template>
  <view class="login-container">
    <view class="bg-overlay"></view>
    
    <view class="login-card">
      <view class="header">
        <text class="title">登录</text>
        <text class="subtitle">石化管巡</text>
      </view>
      
      <form @submit.prevent="handleLogin">
        <view class="form">
          <view class="input-group">
            <uni-icons type="person" size="22" color="#666"></uni-icons>
            <input type="text" v-model="account" placeholder="输入手机号/工号" class="input" />
          </view>
          <view class="input-group">
            <uni-icons type="locked" size="22" color="#666"></uni-icons>
            <input type="password" v-model="password" placeholder="输入密码" class="input" />
          </view>
          <button class="login-btn" :disabled="loading" @click="handleLogin">
            {{loading ? '登录中...' : '登录'}}
          </button>
          <view class="register-link" @click="goToRegister">没有账号？立即注册</view>
        </view>
      </form>
    </view>
    
    <view class="footer-text">Copyright © 2023-2024 石化管巡</view>
  </view>
</template>

<script>
import { backgroundImages } from '@/static/images/bg.js';
import { login } from '@/api/auth';

export default {
  data() {
    return {
      account: '',
      password: '',
      loading: false,
      backgroundImage: backgroundImages.loginBg
    }
  },
  methods: {
    async handleLogin() {
      if (!this.account || !this.password) {
        uni.showToast({
          title: '请输入账号和密码',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      try {
        const response = await login({
          username: this.account,
          password: this.password
        });
        
        if (response.success) {
          // 存储 token 和用户信息
          uni.setStorageSync('token', response.data.token);
          uni.setStorageSync('userInfo', response.data.user);

          console.log('登录成功，用户信息:', response.data.user);

          uni.showToast({
            title: '登录成功',
            icon: 'success'
          });
          // 跳转到首页
          setTimeout(() => {
            this.navigateToHome();
          }, 1500);
        } else {
          uni.showToast({
            title: response.message || '登录失败，请检查账号密码',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.showToast({
          title: error.response?.data?.message || '登录失败，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    navigateToHome() {
      // 判断环境，使用不同的跳转方式
      // #ifdef H5
      if (window && window.location) {
        window.location.replace('#/pages/home/<USER>');
      } else {
      // #endif
        
      // #ifndef H5
        uni.switchTab({
          url: '/pages/home/<USER>',
          fail: (err) => {
            console.error('跳转失败:', err);
            // 如果switchTab失败，尝试使用reLaunch
            uni.reLaunch({
              url: '/pages/home/<USER>'
            });
          }
        });
      // #endif
      // #ifdef H5
      }
      // #endif
    },
    goToRegister() {
      // 判断环境，使用不同的跳转方式
      // #ifdef H5
      if (window && window.location) {
        window.location.replace('#/pages/register/index');
      } else {
      // #endif
        
      // #ifndef H5
        uni.navigateTo({
          url: '/pages/register/index',
          fail: (err) => {
            console.error('导航错误:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      // #endif
      // #ifdef H5
      }
      // #endif
    }
  }
}
</script>

<style>
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background-image: url('https://raw.githubusercontent.com/liuyibo666666/pic/main/img/202404250132071.jpg');
  background-size: cover;
  background-position: center;
  padding: 0;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5));
  z-index: 0;
}

.login-card {
  width: 85%;
  max-width: 600rpx;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(8px);
}

.header {
  margin-bottom: 60rpx;
  text-align: center;
}

.title {
  font-size: 56rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 36rpx;
  color: #666;
  display: block;
}

.form {
  width: 100%;
}

.input-group {
  margin-bottom: 30rpx;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.input-group uni-icons {
  margin-right: 15rpx;
}

.input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.login-btn {
  margin-top: 60rpx;
  background: linear-gradient(135deg, #007AFF, #0057b8);
  color: #fff;
  border-radius: 12rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 15rpx rgba(0, 122, 255, 0.4);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);
}

.login-btn[disabled] {
  opacity: 0.6;
  background: linear-gradient(135deg, #007AFF, #0057b8);
}

.register-link {
  text-align: center;
  margin-top: 40rpx;
  color: #007AFF;
  font-size: 28rpx;
  font-weight: 500;
}

.footer-text {
  position: absolute;
  bottom: 30rpx;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  z-index: 1;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}
</style> 