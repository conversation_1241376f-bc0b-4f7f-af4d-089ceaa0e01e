<template>
  <view class="about">
    <view class="logo-section">
      <image class="logo" src="/static/logo.png"></image>
      <text class="app-name">石化管巡</text>
      <text class="version">Version {{version}}</text>
    </view>

    <view class="info-list">
      <view class="info-item" @click="handleContact">
        <text class="label">联系我们</text>
        <text class="arrow">></text>
      </view>
      <view class="info-item" @click="handlePrivacy">
        <text class="label">隐私政策</text>
        <text class="arrow">></text>
      </view>
      <view class="info-item" @click="handleTerms">
        <text class="label">用户协议</text>
        <text class="arrow">></text>
      </view>
    </view>

    <view class="copyright">
      <text class="copyright-text">Copyright © 2024 石化管巡</text>
      <text class="copyright-text">All Rights Reserved</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      version: '1.0.0'
    }
  },
  methods: {
    handleContact() {
      // TODO: 跳转到联系我们页面或显示联系方式
      uni.showModal({
        title: '联系我们',
        content: '电话：400-888-8888\n邮箱：<EMAIL>',
        showCancel: false
      })
    },
    handlePrivacy() {
      // TODO: 跳转到隐私政策页面
      uni.navigateTo({
        url: '/pages/webview/privacy'
      })
    },
    handleTerms() {
      // TODO: 跳转到用户协议页面
      uni.navigateTo({
        url: '/pages/webview/terms'
      })
    }
  }
}
</script>

<style>
.about {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40rpx;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 60rpx 0;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.app-name {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.version {
  font-size: 24rpx;
  color: #999;
}

.info-list {
  background-color: #fff;
  border-radius: 12rpx;
  margin-top: 40rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #333;
}

.arrow {
  font-size: 28rpx;
  color: #999;
}

.copyright {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 40rpx;
  text-align: center;
}

.copyright-text {
  font-size: 24rpx;
  color: #999;
  display: block;
  line-height: 1.8;
}
</style> 