<template>
  <view class="change-password">
    <view class="form">
      <view class="input-group">
        <text class="label">原密码</text>
        <input type="password" v-model="oldPassword" placeholder="请输入原密码" class="input"/>
      </view>
      <view class="input-group">
        <text class="label">新密码</text>
        <input type="password" v-model="newPassword" placeholder="请输入新密码" class="input"/>
      </view>
      <view class="input-group">
        <text class="label">确认密码</text>
        <input type="password" v-model="confirmPassword" placeholder="请再次输入新密码" class="input"/>
      </view>
    </view>

    <view class="tips">
      <text class="tip-text">密码必须包含字母和数字，长度8-20位</text>
    </view>

    <button class="submit-btn" @click="handleSubmit">确认修改</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  },
  methods: {
    handleSubmit() {
      if (!this.oldPassword || !this.newPassword || !this.confirmPassword) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }
      
      if (this.newPassword !== this.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        })
        return
      }

      // TODO: 调用修改密码接口
      uni.showToast({
        title: '密码修改成功',
        icon: 'success'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
}
</script>

<style>
.change-password {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.form {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
  margin-top: 20rpx;
}

.input-group {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.input-group:last-child {
  border-bottom: none;
}

.label {
  width: 140rpx;
  font-size: 28rpx;
  color: #333;
}

.input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
}

.tips {
  padding: 20rpx 30rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.submit-btn {
  margin-top: 60rpx;
  background: #007AFF;
  color: #fff;
  border-radius: 12rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
}
</style> 