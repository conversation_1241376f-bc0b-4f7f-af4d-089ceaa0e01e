<template>
  <scroll-view class="edit-container" scroll-y="true" enable-back-to-top="true">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <text class="nav-title">编辑个人信息</text>
      <view class="nav-right" @click="saveProfile">
        <text class="save-text">保存</text>
      </view>
    </view>

    <!-- 头像编辑区域 -->
    <view class="avatar-section">
      <text class="section-title">头像</text>
      <view class="avatar-edit">
        <image 
          class="avatar-preview" 
          :src="formData.avatar || '/static/logo.png'"
          mode="aspectFill"
        ></image>
        <view class="avatar-actions">
          <button class="avatar-btn" @click="chooseAvatar">选择头像</button>
          <button class="avatar-btn secondary" @click="takePhoto">拍照</button>
        </view>
      </view>
    </view>

    <!-- 基本信息编辑 -->
    <view class="form-section">
      <text class="section-title">基本信息</text>
      
      <view class="form-item">
        <text class="form-label">显示名称</text>
        <input 
          class="form-input" 
          v-model="formData.displayName" 
          placeholder="请输入显示名称"
          maxlength="20"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">真实姓名</text>
        <input 
          class="form-input" 
          v-model="formData.name" 
          placeholder="请输入真实姓名"
          maxlength="10"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">工号</text>
        <input 
          class="form-input" 
          v-model="formData.employeeId" 
          placeholder="请输入工号"
          maxlength="20"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">角色</text>
        <picker 
          :value="roleIndex" 
          :range="roleOptions" 
          @change="onRoleChange"
          class="form-picker"
        >
          <view class="picker-text">{{ formData.role || '请选择角色' }}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">邮箱</text>
        <input 
          class="form-input" 
          v-model="formData.email" 
          placeholder="请输入邮箱地址"
          type="email"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">手机号</text>
        <input 
          class="form-input" 
          v-model="formData.phone" 
          placeholder="请输入手机号"
          type="number"
          maxlength="11"
        />
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button class="action-btn cancel" @click="goBack">取消</button>
      <button class="action-btn save" @click="saveProfile">保存修改</button>
    </view>
  </scroll-view>
</template>

<script>
import { updateUserProfile, uploadAvatar } from '@/api/user';

export default {
  data() {
    return {
      formData: {
        avatar: '',
        displayName: '',
        name: '',
        employeeId: '',
        role: '',
        email: '',
        phone: '',
        department: '', // 添加department字段
        username: ''
      },
      roleOptions: ['巡检员', '组长', '主管', '经理'],
      roleIndex: 0,
      originalData: {} // 保存原始数据用于比较
    }
  },
  
  onLoad() {
    this.loadUserInfo();
  },
  
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        // 从本地存储获取用户信息
        const userInfo = uni.getStorageSync('userInfo');
        console.log('从本地存储获取的用户信息:', userInfo);

        if (userInfo) {
          this.formData = {
            avatar: userInfo.avatar || '/static/logo.png',
            displayName: userInfo.displayName || '',
            name: userInfo.name || '',
            employeeId: userInfo.employeeId || '',
            role: userInfo.role || '巡检员',
            email: userInfo.email || '',
            phone: userInfo.phone || '',
            department: userInfo.department || '', // 添加缺失的department字段
            username: userInfo.username || ''
          };

          // 保存原始数据，确保包含用户ID
          this.originalData = {
            id: userInfo.id, // 确保包含用户ID
            ...this.formData
          };

          console.log('原始数据（包含ID）:', this.originalData);

          // 设置角色选择器的索引
          this.roleIndex = this.roleOptions.indexOf(this.formData.role);
          if (this.roleIndex === -1) this.roleIndex = 0;
        } else {
          uni.showToast({
            title: '未找到用户信息，请重新登录',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
        uni.showToast({
          title: '加载用户信息失败',
          icon: 'none'
        });
      }
    },
    
    // 选择头像
    async chooseAvatar() {
      try {
        const res = await new Promise((resolve, reject) => {
          uni.chooseImage({
            count: 1,
            sourceType: ['album'],
            success: resolve,
            fail: reject
          });
        });

        // 显示上传中
        uni.showLoading({
          title: '上传头像中...'
        });

        // 上传头像到服务器
        const uploadResult = await uploadAvatar(res.tempFilePaths[0]);

        uni.hideLoading();

        if (uploadResult.success) {
          // 更新头像URL为服务器返回的完整URL
          this.formData.avatar = `http://localhost:3001${uploadResult.data.url}`;

          uni.showToast({
            title: '头像上传成功',
            icon: 'success'
          });
        } else {
          throw new Error(uploadResult.message);
        }

      } catch (error) {
        uni.hideLoading();
        console.error('选择头像失败:', error);
        uni.showToast({
          title: error.message || '头像上传失败',
          icon: 'none'
        });
      }
    },
    
    // 拍照
    async takePhoto() {
      try {
        const res = await new Promise((resolve, reject) => {
          uni.chooseImage({
            count: 1,
            sourceType: ['camera'],
            success: resolve,
            fail: reject
          });
        });

        // 显示上传中
        uni.showLoading({
          title: '上传头像中...'
        });

        // 上传头像到服务器
        const uploadResult = await uploadAvatar(res.tempFilePaths[0]);

        uni.hideLoading();

        if (uploadResult.success) {
          // 更新头像URL为服务器返回的完整URL
          this.formData.avatar = `http://localhost:3001${uploadResult.data.url}`;

          uni.showToast({
            title: '头像上传成功',
            icon: 'success'
          });
        } else {
          throw new Error(uploadResult.message);
        }

      } catch (error) {
        uni.hideLoading();
        console.error('拍照失败:', error);
        uni.showToast({
          title: error.message || '头像上传失败',
          icon: 'none'
        });
      }
    },
    
    // 角色选择改变
    onRoleChange(e) {
      this.roleIndex = e.detail.value;
      this.formData.role = this.roleOptions[this.roleIndex];
    },
    
    // 保存个人信息
    async saveProfile() {
      try {
        // 验证必填字段
        if (!this.formData.displayName.trim()) {
          uni.showToast({
            title: '请输入显示名称',
            icon: 'none'
          });
          return;
        }

        if (!this.formData.name.trim()) {
          uni.showToast({
            title: '请输入真实姓名',
            icon: 'none'
          });
          return;
        }

        // 检查用户ID
        if (!this.originalData.id) {
          console.error('用户ID缺失，尝试从本地存储重新获取...');
          const userInfo = uni.getStorageSync('userInfo');
          if (userInfo && userInfo.id) {
            this.originalData.id = userInfo.id;
            console.log('从本地存储获取到用户ID:', userInfo.id);
          } else {
            uni.showToast({
              title: '用户信息异常，请重新登录',
              icon: 'none'
            });
            return;
          }
        }

        uni.showLoading({
          title: '保存中...'
        });

        // 安全的字符串处理函数
        const safeTrim = (value) => {
          return (value || '').toString().trim();
        };

        // 处理头像URL：确保保存相对路径到数据库
        let avatarPath = null;
        if (this.formData.avatar && this.formData.avatar !== '/static/logo.png') {
          if (this.formData.avatar.startsWith('http://localhost:3001')) {
            // 如果是完整URL，提取相对路径
            avatarPath = this.formData.avatar.replace('http://localhost:3001', '');
          } else if (this.formData.avatar.startsWith('/')) {
            // 如果已经是相对路径，直接使用
            avatarPath = this.formData.avatar;
          } else {
            // 其他情况，保持原值
            avatarPath = this.formData.avatar;
          }
        }

        // 准备要更新的数据
        const updateData = {
          display_name: safeTrim(this.formData.displayName),
          real_name: safeTrim(this.formData.name),
          employee_id: safeTrim(this.formData.employeeId),
          email: safeTrim(this.formData.email) || null, // 空字符串转为null
          phone: safeTrim(this.formData.phone) || null,
          department: safeTrim(this.formData.department) || null,
          avatar: avatarPath // 使用处理后的头像路径
        };

        console.log('头像处理结果:');
        console.log('- 原始头像URL:', this.formData.avatar);
        console.log('- 处理后的头像路径:', avatarPath);
        console.log('- 将保存到数据库的头像值:', updateData.avatar);

        // 验证邮箱格式（如果不为空）
        if (updateData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updateData.email)) {
          uni.hideLoading();
          uni.showToast({
            title: '邮箱格式不正确',
            icon: 'none'
          });
          return;
        }

        console.log('=== 调试信息 ===');
        console.log('准备更新的用户数据:', updateData);
        console.log('用户ID:', this.originalData.id);
        console.log('原始数据:', this.originalData);
        console.log('表单数据:', this.formData);

        // 检查token
        const token = uni.getStorageSync('token');
        console.log('Token存在:', !!token);
        console.log('Token前20位:', token ? token.substring(0, 20) + '...' : '无');

        // 调用后端API更新用户信息
        console.log('开始调用updateUserProfile API...');
        const result = await updateUserProfile(this.originalData.id, updateData);
        console.log('API调用结果:', result);

        uni.hideLoading();

        if (result.success) {
          // 更新本地存储
          const updatedUserInfo = {
            ...this.originalData,
            ...this.formData,
            updateTime: new Date().toISOString()
          };

          uni.setStorageSync('userInfo', updatedUserInfo);

          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });

          // 发送用户信息更新事件
          uni.$emit('userInfoUpdated', updatedUserInfo);

          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          throw new Error(result.message);
        }
        
      } catch (error) {
        uni.hideLoading();
        console.error('保存个人信息失败:', error);
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    },
    
    // 返回
    goBack() {
      // 检查是否有未保存的修改
      const hasChanges = JSON.stringify(this.formData) !== JSON.stringify(this.originalData);
      
      if (hasChanges) {
        uni.showModal({
          title: '提示',
          content: '您有未保存的修改，确定要离开吗？',
          success: (res) => {
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
      } else {
        uni.navigateBack();
      }
    }
  }
}
</script>

<style scoped>
.edit-container {
  height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx; /* 为底部固定按钮留出空间 */
  box-sizing: border-box;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e0e0e0;
}

.nav-left {
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 32rpx;
  color: #007AFF;
  margin-right: 8rpx;
}

.back-text {
  font-size: 28rpx;
  color: #007AFF;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.save-text {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 500;
}

/* 头像编辑区域 */
.avatar-section {
  background-color: #ffffff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.avatar-edit {
  display: flex;
  align-items: center;
}

.avatar-preview {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  border: 2rpx solid #e0e0e0;
}

.avatar-actions {
  flex: 1;
}

.avatar-btn {
  background-color: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
}

.avatar-btn.secondary {
  background-color: #f0f0f0;
  color: #333333;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #fafafa;
}

.form-picker {
  width: 100%;
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #fafafa;
}

.picker-text {
  font-size: 26rpx;
  color: #333333;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e0e0e0;
}

.action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.action-btn.cancel {
  background-color: #f0f0f0;
  color: #666666;
  margin-right: 15rpx;
}

.action-btn.save {
  background-color: #007AFF;
  color: #ffffff;
}
</style>
