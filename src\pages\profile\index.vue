<template>
  <view class="profile-container">
    <!-- 页面标题 -->
    <view class="header">
      <text class="title">个人中心</text>
      <view class="user-info" v-if="userInfo">
        <text class="user-name">{{ userInfo.displayName || userInfo.username || '用户' }}</text>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-header">
        <view class="avatar-section">
          <image class="avatar" :src="getAvatarUrl(userInfo.avatar)" mode="aspectFill"></image>
          <view class="user-details">
            <text class="username">{{ userInfo.displayName || userInfo.name || userInfo.username || '未设置' }}</text>
            <text class="user-role">{{ userInfo.role || '巡检员' }}</text>
          </view>
        </view>
      </view>
      
      <view class="user-info-list">
        <view class="info-row">
          <text class="label">工号：</text>
          <text class="value">{{ userInfo.employeeId || '未设置' }}</text>
        </view>
        <view class="info-row">
          <text class="label">邮箱：</text>
          <text class="value">{{ userInfo.email || '未设置' }}</text>
        </view>
        <view class="info-row">
          <text class="label">电话：</text>
          <text class="value">{{ userInfo.phone || '未设置' }}</text>
        </view>
        <view class="info-row">
          <text class="label">部门：</text>
          <text class="value">{{ userInfo.department || '未设置' }}</text>
        </view>
      </view>
    </view>

    <!-- 统计数据卡片 -->
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">巡检统计</text>
        <text class="card-subtitle">本月数据</text>
      </view>
      
      <view class="stats-list">
        <view class="stat-row">
          <text class="stat-label">总次数：</text>
          <text class="stat-value">{{ stats.thisMonth.total }}</text>
        </view>
        <view class="stat-row">
          <text class="stat-label">正常：</text>
          <text class="stat-value normal">{{ stats.thisMonth.normal }}</text>
        </view>
        <view class="stat-row">
          <text class="stat-label">异常：</text>
          <text class="stat-value abnormal">{{ stats.thisMonth.abnormal }}</text>
        </view>
        <view class="stat-row">
          <text class="stat-label">正常率：</text>
          <text class="stat-value rate">{{ stats.thisMonth.normalRate }}%</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-card">
      <view class="menu-list">
        <view class="menu-item" @click="editProfile">
          <text class="menu-label">编辑个人信息</text>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" @click="goToChangePassword">
          <text class="menu-label">修改密码</text>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" @click="refreshStats">
          <text class="menu-label">刷新统计数据</text>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" @click="showStorageManager">
          <text class="menu-label">存储管理</text>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" @click="clearCache">
          <text class="menu-label">清除缓存</text>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" @click="goToAbout">
          <text class="menu-label">关于应用</text>
          <text class="menu-arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-card">
      <button class="logout-btn" @click="handleLogout">
        <text class="logout-text">退出登录</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getCurrentUser, getUserStats } from '@/api/user';

export default {
  data() {
    return {
      userInfo: {
        avatar: "/static/logo.png",
        username: "加载中...",
        displayName: "",
        name: "",
        role: "",
        employeeId: "",
        email: "",
        phone: "",
        department: ""
      },
      stats: {
        thisMonth: {
          total: 0,
          normal: 0,
          abnormal: 0,
          normalRate: 100
        },
        allTime: {
          total: 0
        }
      }
    };
  },

  onLoad() {
    this.loadUserInfo();
    this.loadStats();
  },

  onShow() {
    this.loadUserInfo();
    this.refreshStats();
  },

  onReady() {
    // 监听用户信息更新事件
    uni.$on('userInfoUpdated', (userInfo) => {
      console.log('收到用户信息更新事件:', userInfo);
      this.userInfo = {
        ...this.userInfo,
        ...userInfo
      };
    });
  },

  onUnload() {
    // 移除事件监听
    uni.$off('userInfoUpdated');
  },

  methods: {
    // 获取头像URL
    getAvatarUrl(avatar) {
      if (!avatar || avatar === '/static/logo.png') {
        return '/static/logo.png';
      }

      // 如果已经是完整URL，直接返回
      if (avatar.startsWith('http')) {
        return avatar;
      }

      // 如果是相对路径，添加域名
      if (avatar.startsWith('/')) {
        return `http://localhost:3001${avatar}`;
      }

      // 其他情况，返回默认头像
      return '/static/logo.png';
    },

    // 加载用户信息
    async loadUserInfo() {
      try {
        // 先从本地存储加载
        const localUserInfo = uni.getStorageSync('userInfo');
        if (localUserInfo) {
          this.userInfo = {
            ...this.userInfo,
            ...localUserInfo
          };
        }

        // 然后从后端获取最新数据
        const result = await getCurrentUser();
        if (result.success) {
          const serverUserInfo = result.data.user || result.data;

          // 转换字段名以匹配前端格式
          const formattedUserInfo = {
            id: serverUserInfo.id,
            username: serverUserInfo.username,
            displayName: serverUserInfo.display_name,
            name: serverUserInfo.real_name,
            role: serverUserInfo.role,
            employeeId: serverUserInfo.employee_id,
            email: serverUserInfo.email,
            phone: serverUserInfo.phone,
            department: serverUserInfo.department,
            avatar: serverUserInfo.avatar ? `http://localhost:3001${serverUserInfo.avatar}` : '/static/logo.png',
            status: serverUserInfo.status,
            lastLoginAt: serverUserInfo.last_login_at,
            createdAt: serverUserInfo.created_at
          };

          // 更新本地存储和页面数据
          this.userInfo = {
            ...this.userInfo,
            ...formattedUserInfo
          };

          uni.setStorageSync('userInfo', this.userInfo);
          console.log('用户信息从服务器更新成功:', this.userInfo);
        } else {
          console.log('从服务器获取用户信息失败，使用本地数据:', result.message);
        }

      } catch (error) {
        console.error('加载用户信息失败:', error);
        // 如果网络请求失败，继续使用本地数据
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const { getUserStats } = await import('@/utils/userManager.js');
        const stats = getUserStats();
        this.stats = stats;
        console.log('统计数据加载成功:', stats);
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },

    // 刷新统计数据
    async refreshStats() {
      try {
        await this.loadStats();
        uni.showToast({
          title: '统计数据已刷新',
          icon: 'success'
        });
      } catch (error) {
        console.error('刷新统计数据失败:', error);
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      }
    },

    // 编辑个人信息
    editProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit'
      });
    },

    // 修改密码
    goToChangePassword() {
      uni.showToast({
        title: '修改密码功能开发中',
        icon: 'none'
      });
    },

    // 显示存储管理器
    async showStorageManager() {
      try {
        uni.showLoading({
          title: '获取存储信息...'
        });

        const { getStorageInfo } = await import('@/api/photo.js');
        const storageInfo = await getStorageInfo();
        
        uni.hideLoading();

        if (storageInfo.success) {
          const data = storageInfo.data;
          
          uni.showActionSheet({
            itemList: [
              `查看详情 (${data.imageCount} 张图片, ${data.estimatedSizeMB} MB)`,
              '清理图片文件',
              '查看文件列表'
            ],
            success: (res) => {
              switch (res.tapIndex) {
                case 0:
                  this.showStorageDetails(data);
                  break;
                case 1:
                  this.cleanImageFiles();
                  break;
                case 2:
                  this.showFileList(data.files);
                  break;
              }
            }
          });
        } else {
          uni.showToast({
            title: '获取存储信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('显示存储管理器失败:', error);
        uni.showToast({
          title: '存储管理功能加载失败',
          icon: 'none'
        });
      }
    },

    // 显示存储详情
    showStorageDetails(data) {
      const details = [
        `图片数量: ${data.imageCount} 张`,
        `估计大小: ${data.estimatedSizeMB} MB`,
        `平均大小: ${data.imageCount > 0 ? (data.estimatedSize / data.imageCount / 1024).toFixed(1) : 0} KB/张`,
        `运行环境: ${data.environment || '未知'}`,
        `存储方式: ${data.note || '标准存储'}`
      ].join('\n');

      uni.showModal({
        title: '存储详情',
        content: details,
        showCancel: false,
        confirmText: '确定'
      });
    },

    // 清理图片文件
    async cleanImageFiles() {
      try {
        const { getStorageInfo } = await import('@/api/photo.js');
        const storageInfo = await getStorageInfo();
        
        if (!storageInfo.success || storageInfo.data.imageCount === 0) {
          uni.showToast({
            title: '没有图片文件需要清理',
            icon: 'none'
          });
          return;
        }

        uni.showModal({
          title: '清理图片文件',
          content: `确定要删除所有 ${storageInfo.data.imageCount} 张图片文件吗？\n\n这将释放约 ${storageInfo.data.estimatedSizeMB} MB 空间，但会清空所有历史记录中的图片。`,
          success: async (res) => {
            if (res.confirm) {
              uni.showLoading({
                title: '清理中...'
              });

              try {
                const { clearAllPermanentImages } = await import('@/api/photo.js');
                const clearResult = await clearAllPermanentImages();
                
                uni.hideLoading();

                if (clearResult.success) {
                  uni.showToast({
                    title: `清理完成，删除了 ${clearResult.count} 个文件`,
                    icon: 'success',
                    duration: 3000
                  });
                  
                  // 通知其他页面数据已清除
                  uni.$emit('dataCleared');
                } else {
                  uni.showToast({
                    title: '清理失败: ' + clearResult.message,
                    icon: 'none'
                  });
                }
              } catch (error) {
                uni.hideLoading();
                console.error('清理图片文件失败:', error);
                uni.showToast({
                  title: '清理失败: ' + error.message,
                  icon: 'none'
                });
              }
            }
          }
        });
      } catch (error) {
        console.error('清理图片文件失败:', error);
        uni.showToast({
          title: '清理功能加载失败',
          icon: 'none'
        });
      }
    },

    // 显示文件列表
    showFileList(files) {
      if (!files || files.length === 0) {
        uni.showToast({
          title: '没有图片文件',
          icon: 'none'
        });
        return;
      }

      const fileList = files.slice(0, 10).map((file, index) => 
        `${index + 1}. ${file}`
      ).join('\n');
      
      const content = files.length > 10 
        ? fileList + `\n... 还有 ${files.length - 10} 个文件`
        : fileList;

      uni.showModal({
        title: `文件列表 (${files.length} 个)`,
        content: content,
        showCancel: false,
        confirmText: '确定'
      });
    },

    // 清除缓存
    async clearCache() {
      try {
        const { getStorageInfo } = await import('@/api/photo.js');
        const storageInfo = await getStorageInfo();

        let contentText = '确定要清除应用缓存吗？这将清除除登录信息外的所有本地数据。';

        if (storageInfo.success) {
          contentText += `\n\n当前存储：${storageInfo.data.imageCount} 张图片，约 ${storageInfo.data.estimatedSizeMB} MB`;
        }

        uni.showModal({
          title: '清除缓存',
          content: contentText,
          success: async (res) => {
            if (res.confirm) {
              uni.showLoading({ title: '清理中...' });

              try {
                const token = uni.getStorageSync('token');
                const userInfo = uni.getStorageSync('userInfo');

                const { clearAllPermanentImages } = await import('@/api/photo.js');
                const clearResult = await clearAllPermanentImages();

                uni.clearStorageSync();

                if (token) uni.setStorageSync('token', token);
                if (userInfo) uni.setStorageSync('userInfo', userInfo);

                uni.hideLoading();

                let successMessage = '缓存已清除';
                if (clearResult.success && clearResult.count > 0) {
                  successMessage += `，清理了 ${clearResult.count} 个图片文件`;
                }

                uni.showToast({
                  title: successMessage,
                  icon: 'success',
                  duration: 3000
                });

                this.loadUserInfo();
                uni.$emit('dataCleared');

              } catch (error) {
                uni.hideLoading();
                uni.showToast({
                  title: '清除失败: ' + error.message,
                  icon: 'none'
                });
              }
            }
          }
        });
      } catch (error) {
        console.error('获取存储信息失败:', error);
        this.simpleClearCache();
      }
    },

    // 简化版清除缓存
    simpleClearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除应用缓存吗？这将清除除登录信息外的所有本地数据。',
        success: (res) => {
          if (res.confirm) {
            try {
              const token = uni.getStorageSync('token');
              const userInfo = uni.getStorageSync('userInfo');

              uni.clearStorageSync();

              if (token) uni.setStorageSync('token', token);
              if (userInfo) uni.setStorageSync('userInfo', userInfo);

              uni.showToast({
                title: '缓存已清除',
                icon: 'success'
              });

              this.loadUserInfo();
            } catch (error) {
              uni.showToast({
                title: '清除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },

    // 关于应用
    goToAbout() {
      uni.showModal({
        title: '关于应用',
        content: '管道巡检系统 v1.0.0\n\n一个简洁高效的管道巡检记录应用',
        showCancel: false,
        confirmText: '确定'
      });
    },

    // 退出登录
    handleLogout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            try {
              uni.removeStorageSync('token');
              uni.removeStorageSync('userInfo');

              uni.showToast({
                title: '已退出登录',
                icon: 'success'
              });

              setTimeout(() => {
                uni.reLaunch({
                  url: '/pages/login/index'
                });
              }, 1500);
            } catch (error) {
              uni.showToast({
                title: '退出失败',
                icon: 'none'
              });
            }
          }
        }
      });
    }
  }
}
</script>

<style scoped>
/* 页面容器 - 简约风格 */
.profile-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20rpx;
}

/* 页面标题 */
.header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 用户信息卡片 */
.user-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.user-header {
  margin-bottom: 20rpx;
}

.avatar-section {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
  border: 2rpx solid #e0e0e0;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.user-role {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.user-info-list {
  border-top: 1rpx solid #f5f5f5;
  padding-top: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.info-row:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
}

/* 统计数据卡片 */
.stats-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.card-subtitle {
  font-size: 24rpx;
  color: #666;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 28rpx;
  color: #666;
}

.stat-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.stat-value.normal {
  color: #28a745;
}

.stat-value.abnormal {
  color: #dc3545;
}

.stat-value.rate {
  color: #007bff;
}

/* 功能菜单卡片 */
.menu-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.menu-list {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-label {
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  font-size: 32rpx;
  color: #ccc;
  font-weight: bold;
}

/* 退出登录卡片 */
.logout-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
}

.logout-btn {
  width: 100%;
  background-color: #dc3545;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
  transition: background-color 0.2s;
}

.logout-btn:active {
  background-color: #c82333;
}

.logout-text {
  color: #fff;
}
</style>
