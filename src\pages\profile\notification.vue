<template>
  <view class="notification">
    <view class="notification-list">
      <view class="notification-item" v-for="(item, index) in notifications" :key="index">
        <view class="item-header">
          <text class="title">{{item.title}}</text>
          <text class="time">{{item.time}}</text>
        </view>
        <text class="content">{{item.content}}</text>
      </view>
    </view>

    <view v-if="notifications.length === 0" class="empty-state">
      <text class="empty-text">暂无消息通知</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      notifications: [
        {
          title: '设备异常提醒',
          time: '2024-03-20 14:30',
          content: '设备ID：DEVICE001 出现异常，请及时处理。'
        },
        {
          title: '巡检任务提醒',
          time: '2024-03-20 09:00',
          content: '您有新的巡检任务待执行，请查看详情。'
        }
      ]
    }
  }
}
</script>

<style>
.notification {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.notification-list {
  margin-top: 20rpx;
}

.notification-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style> 