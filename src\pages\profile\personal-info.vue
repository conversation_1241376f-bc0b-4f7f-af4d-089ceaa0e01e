<template>
  <view class="personal-info-container">
    <view class="info-form">
      <view class="form-item">
        <text class="label">头像</text>
        <view class="avatar-wrapper">
          <image class="avatar" :src="userInfo.avatar"></image>
          <text class="change-btn">更换头像</text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">姓名</text>
        <input type="text" v-model="userInfo.name" placeholder="请输入姓名" class="input" />
      </view>
      
      <view class="form-item">
        <text class="label">工号</text>
        <input type="text" v-model="userInfo.employeeId" placeholder="请输入工号" class="input" />
      </view>
      
      <view class="form-item">
        <text class="label">手机号</text>
        <input type="text" v-model="userInfo.phone" placeholder="请输入手机号" class="input" />
      </view>
    </view>
    
    <button class="save-btn" @click="saveUserInfo">保存</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        avatar: '/static/logo.png',
        name: '',
        employeeId: '',
        phone: ''
      }
    }
  },
  onLoad() {
    this.loadUserInfo();
  },
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        const { getCurrentUserInfo } = await import('@/utils/userManager.js');
        const userInfo = getCurrentUserInfo();

        if (userInfo) {
          this.userInfo = {
            avatar: userInfo.avatar,
            name: userInfo.displayName,
            employeeId: userInfo.employeeId,
            phone: userInfo.phone
          };
          console.log('个人信息页面用户信息加载成功:', this.userInfo);
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
      }
    },
    // 保存用户信息
    async saveUserInfo() {
      try {
        const { updateUserInfo } = await import('@/utils/userManager.js');

        // 验证输入
        if (!this.userInfo.name.trim()) {
          uni.showToast({
            title: '请输入姓名',
            icon: 'none'
          });
          return;
        }

        // 更新用户信息
        const updateData = {
          name: this.userInfo.name.trim(),
          displayName: this.userInfo.name.trim(),
          employeeId: this.userInfo.employeeId.trim(),
          phone: this.userInfo.phone.trim()
        };

        const success = updateUserInfo(updateData);

        if (success) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });

          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('保存用户信息失败:', error);
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style>
.personal-info-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.info-form {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.form-item {
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 140rpx;
  font-size: 28rpx;
  color: #333;
}

.input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
}

.avatar-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  margin-right: 20rpx;
}

.change-btn {
  font-size: 28rpx;
  color: #007AFF;
}

.save-btn {
  background: #007AFF;
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 12rpx;
  margin-top: 40rpx;
}
</style> 