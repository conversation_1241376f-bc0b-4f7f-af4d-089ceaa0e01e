<template>
  <view class="settings-container">
    <view class="settings-list">
      <view class="settings-group">
        <view class="settings-item">
          <text class="item-title">夜间模式</text>
          <switch :checked="settings.darkMode" @change="toggleDarkMode" color="#007AFF" />
        </view>
        <view class="settings-item">
          <text class="item-title">推送通知</text>
          <switch :checked="settings.pushNotification" @change="togglePushNotification" color="#007AFF" />
        </view>
      </view>

      <view class="settings-group">
        <view class="settings-item">
          <text class="item-title">字体大小</text>
          <view class="slider-container">
            <slider :value="settings.fontSize" @change="changeFontSize" min="12" max="20" show-value />
          </view>
        </view>
        <view class="settings-item">
          <text class="item-title">自动更新</text>
          <switch :checked="settings.autoUpdate" @change="toggleAutoUpdate" color="#007AFF" />
        </view>
      </view>

      <view class="settings-group">
        <view class="settings-item" @click="clearCache">
          <text class="item-title">清除缓存</text>
          <text class="cache-size">{{cacheSize}} MB</text>
        </view>
        <view class="settings-item" @click="checkUpdate">
          <text class="item-title">检查更新</text>
          <text class="version-info">当前版本: v1.0.0</text>
        </view>
      </view>
    </view>
    
    <button class="save-btn" @click="saveSettings">保存设置</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      settings: {
        darkMode: false,
        pushNotification: true,
        fontSize: 14,
        autoUpdate: true,
      },
      cacheSize: 2.5
    }
  },
  methods: {
    toggleDarkMode(e) {
      this.settings.darkMode = e.detail.value;
    },
    togglePushNotification(e) {
      this.settings.pushNotification = e.detail.value;
    },
    toggleAutoUpdate(e) {
      this.settings.autoUpdate = e.detail.value;
    },
    changeFontSize(e) {
      this.settings.fontSize = e.detail.value;
    },
    clearCache() {
      uni.showModal({
        title: '提示',
        content: '确定要清除缓存吗？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '清除中...'
            });
            
            // 模拟清除缓存操作
            setTimeout(() => {
              uni.hideLoading();
              this.cacheSize = 0;
              uni.showToast({
                title: '缓存已清除',
                icon: 'success'
              });
            }, 1000);
          }
        }
      });
    },
    checkUpdate() {
      uni.showLoading({
        title: '检查更新...'
      });
      
      // 模拟检查更新操作
      setTimeout(() => {
        uni.hideLoading();
        uni.showModal({
          title: '检查更新',
          content: '当前已是最新版本',
          showCancel: false
        });
      }, 1000);
    },
    saveSettings() {
      uni.showLoading({
        title: '保存中...'
      });
      
      // 模拟保存设置操作
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '设置已保存',
          icon: 'success'
        });
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }, 1000);
    }
  }
}
</script>

<style>
.settings-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.settings-list {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.settings-group {
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.settings-group:last-child .settings-item:last-child {
  border-bottom: none;
}

.item-title {
  font-size: 28rpx;
  color: #333;
}

.slider-container {
  width: 400rpx;
}

.cache-size, .version-info {
  font-size: 24rpx;
  color: #999;
}

.save-btn {
  background: #007AFF;
  color: #fff;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 12rpx;
  margin-top: 40rpx;
}
</style> 