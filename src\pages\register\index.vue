<template>
  <view class="register-container">
    <view class="bg-overlay"></view>
    
    <!-- 返回按钮 -->
    <view class="back-btn" @click="goBack">
      <uni-icons type="close" size="28" color="#ffffff"></uni-icons>
    </view>
    
    <view class="register-card">
      <view class="header">
        <text class="title">创建账号</text>
        <text class="subtitle">请填写注册信息</text>
      </view>
      
      <view class="form">
        <view class="input-group">
          <uni-icons type="person" size="22" color="#666"></uni-icons>
          <input type="text" v-model="username" placeholder="请输入用户名" class="input" />
        </view>
        <view class="input-group">
          <uni-icons type="email" size="22" color="#666"></uni-icons>
          <input type="email" v-model="email" placeholder="请输入邮箱" class="input" />
        </view>
        <view class="input-group">
          <uni-icons type="locked" size="22" color="#666"></uni-icons>
          <input type="password" v-model="password" placeholder="请输入密码" class="input" />
        </view>
        <view class="input-group">
          <uni-icons type="locked-filled" size="22" color="#666"></uni-icons>
          <input type="password" v-model="confirmPassword" placeholder="请确认密码" class="input" />
          <uni-icons v-if="passwordVisibility" @click="togglePasswordVisibility" type="eye" size="22" color="#666"></uni-icons>
          <uni-icons v-else @click="togglePasswordVisibility" type="eye-slash" size="22" color="#666"></uni-icons>
        </view>
        <view class="privacy-checkbox">
          <checkbox :checked="agreePrivacy" @click="toggleAgreePrivacy" />
          <text class="privacy-text">我已阅读并同意</text>
          <text class="privacy-link" @click="goToPrivacyPolicy">用户协议和隐私政策</text>
        </view>
        <button class="register-btn" :disabled="loading" @click="handleRegister">
          {{loading ? '注册中...' : '注册'}}
        </button>
        <view class="login-link" @click="goToLogin">已有账号？立即登录</view>
      </view>
    </view>
    
    <view class="footer-text">Copyright © 2023-2024 石化管巡</view>
  </view>
</template>

<script>
import { backgroundImages } from '@/static/images/bg.js';
import { register } from '@/api/auth';

export default {
  data() {
    return {
      username: '',
      password: '',
      confirmPassword: '',
      email: '',
      agreePrivacy: false,
      loading: false,
      passwordVisibility: false,
      backgroundImage: backgroundImages.loginBg
    }
  },
  methods: {
    goBack() {
      this.navigateToLogin();
    },
    togglePasswordVisibility() {
      this.passwordVisibility = !this.passwordVisibility;
    },
    toggleAgreePrivacy() {
      this.agreePrivacy = !this.agreePrivacy;
    },
    goToPrivacyPolicy() {
      uni.showToast({
        title: '跳转到用户协议和隐私政策页面',
        icon: 'none'
      });
    },
    async handleRegister() {
      if (!this.username || !this.password || !this.confirmPassword || !this.email) {
        uni.showToast({
          title: '请填写所有必填信息',
          icon: 'none'
        });
        return;
      }
      
      const emailRegex = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
      if (!emailRegex.test(this.email)) {
        uni.showToast({
          title: '请输入有效的邮箱地址',
          icon: 'none'
        });
        return;
      }

      if (this.password.length < 6) {
        uni.showToast({
          title: '密码长度不能少于6位',
          icon: 'none'
        });
        return;
      }

      if (this.password !== this.confirmPassword) {
        uni.showToast({
          title: '两次输入的密码不一致',
          icon: 'none'
        });
        return;
      }

      if (!this.agreePrivacy) {
        uni.showToast({
          title: '请阅读并同意用户协议和隐私政策',
          icon: 'none'
        });
        return;
      }
      
      this.loading = true;
      try {
        const response = await register({
          username: this.username,
          password: this.password,
          email: this.email
        });
        
        if (response.success) {
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          });
          setTimeout(() => {
            this.navigateToLogin();
          }, 1500);
        } else {
          uni.showToast({
            title: response.message || '注册失败，请稍后重试',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.showToast({
          title: error.response?.data?.message || '注册请求失败，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    navigateToLogin() {
      // 判断环境，使用不同的跳转方式
      // #ifdef H5
      if (typeof window !== 'undefined' && window.location) {
        window.location.href = '#/pages/login/index';
      } else {
      // #endif
      
      // #ifndef H5
        uni.navigateTo({
          url: '/pages/login/index',
          fail: (err) => {
            console.error('导航错误:', err);
            // 如果navigateTo失败，尝试使用reLaunch
            uni.reLaunch({
              url: '/pages/login/index'
            });
          }
        });
      // #endif
      // #ifdef H5
      }
      // #endif
    },
    goToLogin() {
      this.navigateToLogin();
    }
  }
}
</script>

<style>
.register-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background-image: url('https://raw.githubusercontent.com/liuyibo666666/pic/main/img/202404250132071.jpg');
  background-size: cover;
  background-position: center;
  padding: 0;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5));
  z-index: 0;
}

.back-btn {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  z-index: 2;
  color: #ffffff;
}

.back-btn .uni-icons {
  padding: 10rpx;
}

.register-card {
  width: 85%;
  max-width: 600rpx;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(8px);
}

.header {
  margin-bottom: 60rpx;
  text-align: center;
}

.title {
  font-size: 56rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 36rpx;
  color: #666;
  display: block;
}

.form {
  width: 100%;
}

.input-group {
  display: flex;
  align-items: center;
  background-color: #f8f9fc;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  padding: 20rpx 25rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.input-group .uni-icons {
  margin-right: 20rpx;
}

.input {
  flex: 1;
  height: 60rpx;
  font-size: 30rpx;
  color: #333;
  background-color: transparent;
  border: none;
  outline: none;
}

.privacy-checkbox {
  display: flex;
  align-items: center;
  margin-top: -10rpx;
  margin-bottom: 40rpx;
  font-size: 26rpx;
  color: #666;
}

.privacy-checkbox checkbox {
  transform: scale(0.7);
  margin-right: 10rpx;
}

.privacy-text {
  margin-right: 5rpx;
}

.privacy-link {
  color: #007AFF;
  text-decoration: underline;
  cursor: pointer;
}

.register-btn {
  width: 100%;
  background: linear-gradient(to right, #007AFF, #00CFFF);
  color: #fff;
  border-radius: 12rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 36rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.register-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(0, 122, 255, 0.3);
}

.register-btn[disabled] {
  background: #a8d4ff;
  box-shadow: none;
  cursor: not-allowed;
}

.login-link {
  margin-top: 40rpx;
  text-align: center;
  font-size: 28rpx;
  color: #007AFF;
  cursor: pointer;
}

.footer-text {
  position: absolute;
  bottom: 40rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  z-index: 1;
}
</style> 