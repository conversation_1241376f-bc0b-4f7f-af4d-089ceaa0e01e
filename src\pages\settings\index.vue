<template>
  <view class="settings-container">
    <view class="header">
      <text class="title">设备设置</text>
    </view>

    <view class="settings-list">
      <view class="settings-item">
        <text class="item-label">设备名称</text>
        <input type="text" v-model="deviceName" placeholder="请输入设备名称" class="item-input" />
      </view>
      <view class="settings-item">
        <text class="item-label">设备ID</text>
        <input type="text" v-model="deviceId" placeholder="请输入设备ID" class="item-input" />
      </view>
      <view class="settings-item">
        <text class="item-label">设备类型</text>
        <picker @change="handleTypeChange" :value="deviceTypeIndex" :range="deviceTypes" class="item-picker">
          <view class="picker-text">{{deviceTypes[deviceTypeIndex]}}</view>
        </picker>
      </view>
    </view>

    <button class="save-btn" @click="saveSettings">保存设置</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceName: '',
      deviceId: '',
      deviceTypeIndex: 0,
      deviceTypes: ['类型A', '类型B', '类型C', '类型D']
    }
  },
  methods: {
    handleTypeChange(e) {
      this.deviceTypeIndex = e.detail.value
    },
    saveSettings() {
      if (!this.deviceName || !this.deviceId) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        })
        return
      }
      // TODO: 保存设置
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
}
</script>

<style>
.settings-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40rpx;
}

.header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.settings-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
}

.settings-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
}

.item-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.item-picker {
  flex: 1;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.save-btn {
  margin-top: 60rpx;
  background: #007AFF;
  color: #fff;
  border-radius: 12rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
}
</style> 