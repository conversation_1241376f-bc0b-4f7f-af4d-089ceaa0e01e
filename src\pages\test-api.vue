<template>
  <view class="container">
    <view class="header">
      <text class="title">API测试页面</text>
    </view>
    
    <view class="content">
      <button @click="testUserUpdate" class="test-btn">测试用户信息更新</button>
      <button @click="testDirectRequest" class="test-btn">测试直接请求</button>
      <button @click="checkUserInfo" class="test-btn">检查用户信息</button>
      
      <view class="result">
        <text class="result-title">测试结果：</text>
        <text class="result-text">{{ result }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { updateUserProfile } from '@/api/user';

export default {
  data() {
    return {
      result: '等待测试...'
    };
  },
  
  methods: {
    // 检查用户信息
    checkUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      const token = uni.getStorageSync('token');
      
      this.result = `用户信息: ${JSON.stringify(userInfo, null, 2)}\n\nToken存在: ${!!token}`;
      console.log('用户信息:', userInfo);
      console.log('Token:', token);
    },
    
    // 测试用户信息更新API
    async testUserUpdate() {
      try {
        this.result = '正在测试用户信息更新...';
        
        const userInfo = uni.getStorageSync('userInfo');
        if (!userInfo || !userInfo.id) {
          this.result = '错误：用户信息缺少ID';
          return;
        }
        
        const updateData = {
          display_name: '测试名称' + Date.now(),
          real_name: '测试真实姓名',
          email: '<EMAIL>'
        };
        
        console.log('测试数据:', updateData);
        console.log('用户ID:', userInfo.id);
        
        const result = await updateUserProfile(userInfo.id, updateData);
        
        this.result = `成功！结果: ${JSON.stringify(result, null, 2)}`;
        console.log('测试结果:', result);
        
      } catch (error) {
        this.result = `失败！错误: ${error.message}`;
        console.error('测试失败:', error);
      }
    },
    
    // 测试直接请求
    async testDirectRequest() {
      try {
        this.result = '正在测试直接请求...';
        
        const userInfo = uni.getStorageSync('userInfo');
        const token = uni.getStorageSync('token');
        
        if (!userInfo || !userInfo.id) {
          this.result = '错误：用户信息缺少ID';
          return;
        }
        
        const updateData = {
          display_name: '直接请求测试' + Date.now(),
          real_name: '直接请求真实姓名'
        };
        
        console.log('直接请求数据:', updateData);
        console.log('请求URL:', `http://localhost:3001/api/users/${userInfo.id}`);
        
        const response = await new Promise((resolve, reject) => {
          uni.request({
            url: `http://localhost:3001/api/users/${userInfo.id}`,
            method: 'PUT',
            data: updateData,
            header: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            success: resolve,
            fail: reject
          });
        });
        
        console.log('直接请求响应:', response);
        
        if (response.statusCode === 200) {
          this.result = `直接请求成功！响应: ${JSON.stringify(response.data, null, 2)}`;
        } else {
          this.result = `直接请求失败！状态码: ${response.statusCode}, 响应: ${JSON.stringify(response.data, null, 2)}`;
        }
        
      } catch (error) {
        this.result = `直接请求失败！错误: ${error.message || JSON.stringify(error)}`;
        console.error('直接请求失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.container {
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.test-btn {
  background: #007AFF;
  color: white;
  border: none;
  padding: 15px;
  border-radius: 8px;
  font-size: 16px;
}

.test-btn:active {
  background: #0056CC;
}

.result {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.result-title {
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.result-text {
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
