<template>
  <view class="container">
    <view class="title">时间显示测试页面</view>
    
    <!-- 时间显示测试 -->
    <view class="test-section">
      <text class="section-title">实时时间显示</text>
      <view class="time-display">
        <text class="time-text">{{ currentTime }}</text>
      </view>
      <text class="description">应该每秒更新一次</text>
    </view>
    
    <!-- 时间格式测试 -->
    <view class="test-section">
      <text class="section-title">时间格式测试</text>
      <view class="format-display">
        <text class="format-text">ISO格式: {{ isoTime }}</text>
        <text class="format-text">显示格式: {{ formattedTime }}</text>
        <text class="format-text">时间戳: {{ timestamp }}</text>
      </view>
    </view>
    
    <!-- 控制按钮 -->
    <view class="button-section">
      <button class="test-btn" @tap="startTimer">开始计时</button>
      <button class="test-btn" @tap="stopTimer">停止计时</button>
      <button class="test-btn" @tap="resetTimer">重置时间</button>
    </view>
    
    <!-- 状态显示 -->
    <view class="status-section">
      <text class="status-text">定时器状态: {{ timerStatus }}</text>
      <text class="status-text">更新次数: {{ updateCount }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TestTime',
  data() {
    return {
      currentTime: '',
      isoTime: '',
      formattedTime: '',
      timestamp: '',
      timeInterval: null,
      timerStatus: '未启动',
      updateCount: 0
    };
  },
  
  onLoad() {
    console.log('时间测试页面加载');
    this.initTime();
    this.startTimeUpdate();
  },
  
  onShow() {
    console.log('时间测试页面显示');
    if (!this.timeInterval) {
      this.initTime();
      this.startTimeUpdate();
    }
  },
  
  onHide() {
    console.log('时间测试页面隐藏');
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
      this.timerStatus = '已暂停';
    }
  },
  
  onUnload() {
    console.log('时间测试页面卸载');
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }
  },
  
  methods: {
    // 初始化时间
    initTime() {
      this.updateAllTimes();
      this.timerStatus = '已启动';
      this.updateCount = 0;
    },
    
    // 开始时间更新
    startTimeUpdate() {
      if (this.timeInterval) {
        clearInterval(this.timeInterval);
      }
      
      this.timeInterval = setInterval(() => {
        this.updateAllTimes();
        this.updateCount++;
      }, 1000);
      
      this.timerStatus = '运行中';
    },
    
    // 更新所有时间格式
    updateAllTimes() {
      const now = new Date();
      this.currentTime = this.formatDateTime(now);
      this.isoTime = now.toISOString();
      this.formattedTime = this.formatDateTime(now);
      this.timestamp = now.getTime();
    },
    
    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    
    // 手动开始计时
    startTimer() {
      this.startTimeUpdate();
    },
    
    // 停止计时
    stopTimer() {
      if (this.timeInterval) {
        clearInterval(this.timeInterval);
        this.timeInterval = null;
        this.timerStatus = '已停止';
      }
    },
    
    // 重置时间
    resetTimer() {
      this.stopTimer();
      this.initTime();
      this.startTimeUpdate();
    }
  }
};
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.test-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.time-display {
  background-color: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 10rpx;
}

.time-text {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: bold;
  text-align: center;
  display: block;
}

.description {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.format-display {
  background-color: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 15rpx;
}

.format-text {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  font-family: monospace;
}

.button-section {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.test-btn {
  width: 100%;
  height: 70rpx;
  background-color: #007AFF;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}

.test-btn:last-child {
  margin-bottom: 0;
}

.status-section {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
}

.status-text {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.status-text:last-child {
  margin-bottom: 0;
}
</style>
