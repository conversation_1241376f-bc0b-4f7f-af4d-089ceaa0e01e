<template>
  <view class="container">
    <view class="title">三角拍测试页面</view>
    
    <!-- 状态选择区域 -->
    <view class="form-section">
      <text class="form-label">检查状态:</text>
      <view class="status-selector">
        <view 
          class="status-option" 
          :class="{ active: selectedStatus === 'normal' }"
          @tap="selectStatus('normal')"
        >
          <text class="status-text">✅ 正常</text>
        </view>
        <view 
          class="status-option" 
          :class="{ active: selectedStatus === 'unknown' }"
          @tap="selectStatus('unknown')"
        >
          <text class="status-text">❓ 未知</text>
        </view>
        <view 
          class="status-option" 
          :class="{ active: selectedStatus === 'abnormal' }"
          @tap="selectStatus('abnormal')"
        >
          <text class="status-text">⚠️ 异常</text>
        </view>
      </view>
    </view>

    <!-- 显示当前状态 -->
    <view class="status-display">
      <text>当前选择的状态: {{ selectedStatus || '未选择' }}</text>
    </view>

    <!-- 异常信息录入区域 - 只有选择异常时才显示 -->
    <view v-if="selectedStatus === 'abnormal'" class="form-section">
      <text class="form-label">异常情况描述:</text>
      <textarea v-model="abnormalInfo" placeholder="请详细描述异常情况" class="textarea"></textarea>
    </view>

    <!-- 测试按钮 -->
    <view class="button-group">
      <button class="btn btn-primary" @tap="testFunction">
        测试函数调用
      </button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TestTriangle',
  data() {
    return {
      selectedStatus: '',
      abnormalInfo: ''
    };
  },
  methods: {
    selectStatus(status) {
      console.log('selectStatus 被调用，参数:', status);
      this.selectedStatus = status;
      console.log('状态已设置为:', this.selectedStatus);
      
      // 如果不是异常状态，清空异常信息
      if (status !== 'abnormal') {
        this.abnormalInfo = '';
      }
    },
    
    testFunction() {
      console.log('测试函数被调用');
      console.log('当前状态:', this.selectedStatus);
      console.log('异常信息:', this.abnormalInfo);
      
      uni.showModal({
        title: '测试结果',
        content: `状态: ${this.selectedStatus || '未选择'}\n异常信息: ${this.abnormalInfo || '无'}`,
        showCancel: false
      });
    }
  }
};
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.form-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.status-selector {
  display: flex;
  justify-content: space-between;
}

.status-option {
  flex: 1;
  margin: 0 10rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.status-option.active {
  border-color: #007AFF;
  background-color: #e3f2fd;
}

.status-option:first-child {
  margin-left: 0;
}

.status-option:last-child {
  margin-right: 0;
}

.status-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.status-option.active .status-text {
  color: #007AFF;
  font-weight: bold;
}

.status-display {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #e0e0e0;
}

.button-group {
  padding: 20rpx 0;
}

.btn {
  width: 100%;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-primary {
  background-color: #007AFF;
  color: #ffffff;
}
</style>
