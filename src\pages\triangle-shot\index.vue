<template>
  <!-- 三角拍页面容器 - 整个页面的容器 -->
  <view class="container">
    <!-- 页面标题 -->
    <text class="title">三角拍</text>

    <!-- 拍照区域 - 包含左中右三个拍照区域 -->
    <view class="photo-area">
      <!-- 左侧拍照区域 -->
      <view class="photo-section">
        <!-- 左侧标签 -->
        <text class="section-label">左</text>
        <!-- 左侧拍照框 - 点击触发拍照功能 -->
        <view class="photo-box" @click="takePhoto('left')">
          <!-- 如果已拍照则显示预览图 -->
          <image v-if="photos.left" :src="photos.left" mode="aspectFill" class="preview-image"></image>
          <!-- 如果未拍照则显示占位符 -->
          <view v-else class="placeholder"></view>
        </view>
      </view>

      <!-- 中间拍照区域 -->
      <view class="photo-section">
        <!-- 中间标签 -->
        <text class="section-label">中</text>
        <!-- 中间拍照框 - 点击触发拍照功能 -->
        <view class="photo-box" @click="takePhoto('center')">
          <!-- 如果已拍照则显示预览图 -->
          <image v-if="photos.center" :src="photos.center" mode="aspectFill" class="preview-image"></image>
          <!-- 如果未拍照则显示占位符 -->
          <view v-else class="placeholder"></view>
        </view>
      </view>

      <!-- 右侧拍照区域 -->
      <view class="photo-section">
        <!-- 右侧标签 -->
        <text class="section-label">右</text>
        <!-- 右侧拍照框 - 点击触发拍照功能 -->
        <view class="photo-box" @click="takePhoto('right')">
          <!-- 如果已拍照则显示预览图 -->
          <image v-if="photos.right" :src="photos.right" mode="aspectFill" class="preview-image"></image>
          <!-- 如果未拍照则显示占位符 -->
          <view v-else class="placeholder"></view>
        </view>
      </view>
    </view>

    <!-- 设备ID输入区域 -->
    <view class="form-section">
      <text class="form-label">设备ID:</text>
      <input
        v-model="deviceId"
        placeholder="请输入设备ID"
        class="input"
      />
    </view>

    <!-- 时间信息区域 -->
    <view class="form-section">
      <text class="form-label">检查时间:</text>
      <view class="time-display">
        <text class="time-text">{{ currentTime }}</text>
      </view>
    </view>

    <!-- 状态选择区域 -->
    <view class="form-section">
      <text class="form-label">检查状态:</text>
      <view class="status-selector">
        <view
          class="status-option"
          :class="{ active: selectedStatus === 'normal' }"
          @tap="selectNormal"
        >
          <text class="status-text">✅ 正常</text>
        </view>
        <view
          class="status-option"
          :class="{ active: selectedStatus === 'unknown' }"
          @tap="selectUnknown"
        >
          <text class="status-text">❓ 未知</text>
        </view>
        <view
          class="status-option"
          :class="{ active: selectedStatus === 'abnormal' }"
          @tap="selectAbnormal"
        >
          <text class="status-text">⚠️ 异常</text>
        </view>
      </view>
    </view>

    <!-- 异常信息录入区域 - 只有选择异常时才显示 -->
    <view v-if="selectedStatus === 'abnormal'" class="form-section">
      <text class="form-label">异常情况描述:</text>
      <textarea v-model="abnormalInfo" placeholder="请详细描述异常情况" class="textarea"></textarea>
    </view>

    <!-- 底部按钮区域 -->
    <view class="button-group">
      <!-- 图片回传按钮 - 上传三张照片 -->
      <button
        class="btn btn-primary"
        @click="handleUpload"
        :disabled="!selectedStatus"
      >
        {{ getUploadButtonText() }}
      </button>
      <!-- 异常信息录入按钮 - 只有选择异常时才显示 -->
      <button
        v-if="selectedStatus === 'abnormal'"
        class="btn btn-warning"
        @click="handleSubmit"
      >
        提交异常信息
      </button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TriangleShot',
  data() {
    return {
      // 照片对象 - 包含左中右三个位置的照片URL
      photos: {
        left: '',
        center: '',
        right: ''
      },
      // 异常信息
      abnormalInfo: '',
      // 选择的状态
      selectedStatus: '',
      // 设备ID
      deviceId: '',
      // 时间相关
      currentTime: '',
      uploadTime: '',
      timeInterval: null
    };
  },

  onLoad(options) {
    console.log("三角拍页面加载", options);
    // 如果从其他页面传递了设备ID，则自动填充
    if (options && options.deviceId) {
      this.deviceId = options.deviceId;
    }
    this.initTime();
    this.startTimeUpdate();
  },

  onShow() {
    // 确保时间更新正常运行
    if (!this.timeInterval) {
      this.initTime();
      this.startTimeUpdate();
    }
  },

  onHide() {
    // 页面隐藏时清除定时器，节省资源
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }
  },

  onUnload() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }
  },
  methods: {
    // 初始化时间
    initTime() {
      this.updateCurrentTime();
    },

    // 开始时间更新
    startTimeUpdate() {
      this.timeInterval = setInterval(() => {
        this.updateCurrentTime();
      }, 1000);
    },

    // 更新当前时间显示
    updateCurrentTime() {
      const now = new Date();
      this.currentTime = this.formatDateTime(now);
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 选择正常状态
    selectNormal() {
      console.log('选择正常状态');
      this.selectedStatus = 'normal';
      this.abnormalInfo = '';
    },

    // 选择未知状态
    selectUnknown() {
      console.log('选择未知状态');
      this.selectedStatus = 'unknown';
      this.abnormalInfo = '';
    },

    // 选择异常状态
    selectAbnormal() {
      console.log('选择异常状态');
      this.selectedStatus = 'abnormal';
    },

    // 选择状态函数（通用）
    selectStatus(status) {
      console.log('selectStatus 被调用，参数:', status);
      this.selectedStatus = status;
      console.log('状态已设置为:', this.selectedStatus);

      // 如果不是异常状态，清空异常信息
      if (status !== 'abnormal') {
        this.abnormalInfo = '';
      }
    },

    // 获取上传按钮文本
    getUploadButtonText() {
      if (!this.selectedStatus) {
        return '请先选择检查状态';
      }

      switch (this.selectedStatus) {
        case 'normal':
          return '上传正常检查结果';
        case 'unknown':
          return '上传未知状态结果';
        case 'abnormal':
          return '上传异常检查结果';
        default:
          return '图片回传';
      }
    },

    // 拍照函数 - 根据position参数确定拍摄位置
    takePhoto(position) {
      // 调用uni-app的选择图片API（这里用相机模式）
      uni.chooseImage({
        count: 1, // 一次只能选择一张图片
        sourceType: ['camera'], // 仅使用相机拍照
        success: (res) => {
          // 拍照成功后更新对应位置的照片URL
          this.photos[position] = res.tempFilePaths[0];
        }
      });
    },

    // 图片上传处理函数
    async handleUpload() {
  // 检查登录状态
  const token = uni.getStorageSync('token');
  const userInfo = uni.getStorageSync('userInfo');

  if (!token || !userInfo) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    });
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/index'
      });
    }, 1500);
    return;
  }

      // 检查是否所有位置都已拍照
      if (!this.photos.left || !this.photos.center || !this.photos.right) {
        // 如果有位置未拍照，提示用户完成所有拍照
        uni.showToast({
          title: '请完成所有位置的拍照',
          icon: 'none'
        });
        return;
      }

      // 检查是否选择了状态
      if (!this.selectedStatus) {
        uni.showToast({
          title: '请先选择检查状态',
          icon: 'none'
        });
        return;
      }

      // 如果选择异常但没有填写异常信息，提示用户
      if (this.selectedStatus === 'abnormal' && !this.abnormalInfo.trim()) {
        uni.showToast({
          title: '请填写异常情况描述',
          icon: 'none'
        });
        return;
      }

      // 显示上传中提示
      uni.showLoading({
        title: '上传中...'
      });

      try {
        // 导入完整版巡检API
        const { saveTriangleShotRecord } = await import('@/api/inspection');

        // 直接保存三角拍记录（新API会处理图片上传）
        const recordData = {
          deviceId: this.deviceId || 'PIPE001',
          uploadTime: new Date().toISOString(),
          inspector: userInfo.displayName || userInfo.username || '当前用户',
          status: this.selectedStatus === 'normal' ? '正常' : (this.selectedStatus === 'abnormal' ? '异常' : '未知'),
          abnormalInfo: this.selectedStatus === 'abnormal' ? this.abnormalInfo.trim() : '',
          location: '',
          notes: '',
          photos: {
            left: this.photos.left,
            center: this.photos.center,
            right: this.photos.right
          }
        };

        console.log('保存三角拍记录:', recordData);
        const saveResult = await saveTriangleShotRecord(recordData);
        console.log('三角拍保存结果:', saveResult);

        // 检查保存是否成功
        if (!saveResult.success) {
          throw new Error(saveResult.message || '保存记录失败');
        }

        // 记录实际上传时间
        this.uploadTime = new Date().toISOString();
        const uploadTimeFormatted = this.formatDateTime(new Date());

        uni.hideLoading();

          // 数据保存成功后立即通知其他页面
          console.log('=== 准备发送数据更新事件 ===');
          console.log('事件数据:', {
            type: 'triangle-shot',
            data: recordData,
            saveResult: saveResult
          });

          // 尝试多种事件发送方式
          try {
            // 方式1: uni.$emit
            uni.$emit('dataUpdated', {
              type: 'triangle-shot',
              data: recordData,
              saveResult: saveResult
            });
            console.log('uni.$emit 事件已发送');

            // 方式2: 使用存储标记
            uni.setStorageSync('dataUpdatedFlag', {
              timestamp: Date.now(),
              type: 'triangle-shot',
              data: recordData
            });
            console.log('存储标记已设置');

          } catch (error) {
            console.error('发送事件失败:', error);
          }

          console.log('=== 数据更新事件已发送 ===');

          // 显示成功提示，包含状态信息和时间
          const statusText = {
            'normal': '正常',
            'unknown': '未知',
            'abnormal': '异常'
          }[this.selectedStatus];

          uni.showModal({
            title: '上传成功',
            content: `状态: ${statusText}\n上传时间: ${uploadTimeFormatted}`,
            showCancel: false,
            confirmText: '确定',
            success: () => {
              setTimeout(() => {
                // 跳转到历史数据页面
                uni.switchTab({
                  url: '/pages/history/index'
                });
              }, 500);
            }
          });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '上传失败: ' + error.message,
          icon: 'none'
        });
      }
    },

    // 异常信息提交处理函数
    async handleSubmit() {
      // 检查登录状态
      const token = uni.getStorageSync('token');
      const userInfo = uni.getStorageSync('userInfo');

      if (!token || !userInfo) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/index'
          });
        }, 1500);
        return;
      }

      // 检查异常信息是否为空
      if (!this.abnormalInfo.trim()) {
        // 如果异常信息为空，提示用户输入
        uni.showToast({
          title: '请输入异常情况',
          icon: 'none'
        });
        return;
      }

      try {
        // 导入完整版巡检API
        const { saveTriangleShotRecord } = await import('@/api/inspection');

        // 保存异常信息记录到历史数据
        const recordData = {
          deviceId: this.deviceId || 'PIPE001',
          type: 'triangle-shot',
          photos: {
            left: this.photos.left || '',
            center: this.photos.center || '',
            right: this.photos.right || ''
          },
          abnormalInfo: this.abnormalInfo.trim(),
          inspector: userInfo.displayName || userInfo.username || '当前用户',
          status: '异常',
          uploadTime: new Date().toISOString()
        };

        await saveTriangleShotRecord(recordData);

        // 显示提交成功提示
        uni.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 成功后延迟2秒返回上一页
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
          }
        });
      } catch (error) {
        uni.showToast({
          title: '提交失败: ' + error.message,
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style>
/* 容器样式 - 设置最小高度、背景色、内边距和弹性布局 */
.container {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

/* 标题样式 - 设置字体大小、字体粗细、颜色、底部外边距和文本对齐方式 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

/* 拍照区域样式 - 设置显示方式、对齐方式、内边距和背景色 */
.photo-area {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #ffffff;
}

/* 拍照区域各部分样式 - 设置弹性布局和外边距 */
.photo-section {
  flex: 1;
  margin: 0 10rpx;
}

/* 拍照区域标签样式 - 设置字体大小、颜色、底部外边距、显示方式和文本对齐方式 */
.section-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
  text-align: center;
}

/* 拍照框样式 - 设置宽度、底部内边距、位置、背景色和圆角 */
.photo-box {
  width: 100%;
  padding-bottom: 100%; /* 保持正方形 */
  position: relative;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

/* 预览图片样式 - 设置位置、尺寸、填充方式和圆角 */
.preview-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8rpx;
}

/* 占位符样式 - 设置位置、尺寸、背景色和圆角 */
.placeholder {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

/* 表单区域样式 - 设置内边距和弹性布局 */
.form-section {
  padding: 20rpx;
  flex: 1;
}

/* 表单标签样式 - 设置字体大小、颜色和底部外边距 */
.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

/* 文本框样式 - 设置宽度、高度、背景色、圆角、内边距和字体大小 */
.textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

/* 按钮组样式 - 设置显示方式、对齐方式、内边距和间距 */
.button-group {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  gap: 20rpx;
}

/* 按钮通用样式 - 设置弹性布局、高度、圆角、字体大小、显示方式、对齐方式、居中方式和无边框 */
.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* 主要按钮样式 - 设置背景色和文字颜色 */
.btn-primary {
  background-color: #007AFF;
  color: #ffffff;
}

/* 警告按钮样式 - 设置背景色和文字颜色 */
.btn-warning {
  background-color: #FF3B30;
  color: #ffffff;
}

/* 禁用按钮样式 */
.btn:disabled {
  background-color: #ccc !important;
  color: #999 !important;
  cursor: not-allowed;
}

/* 状态选择器样式 */
.status-selector {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.status-option {
  flex: 1;
  margin: 0 10rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.status-option.active {
  border-color: #007AFF;
  background-color: #e3f2fd;
}

.status-option:first-child {
  margin-left: 0;
}

.status-option:last-child {
  margin-right: 0;
}

.status-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.status-option.active .status-text {
  color: #007AFF;
  font-weight: bold;
}

/* 时间显示样式 */
.time-display {
  background-color: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 10rpx;
}

.time-text {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: bold;
  text-align: center;
  display: block;
}

/* 输入框样式 */
.input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border: 1rpx solid #e0e0e0;
  margin-top: 10rpx;
}

.input:focus {
  border-color: #007AFF;
  background-color: #fff;
}
</style>