<template>
  <!-- 仰拍页面容器 -->
  <view class="container">
    <!-- 页面标题 -->
    <text class="title">仰拍</text>

    <!-- 拍照区域 -->
    <view class="photo-area">
      <!-- 仰拍区域 -->
      <view class="photo-section">
        <text class="section-label">仰拍</text>
        <view class="photo-box" @click="takePhoto">
          <image
            v-if="photo"
            :src="photo"
            mode="aspectFill"
            class="preview-image"
          ></image>
          <view v-else class="placeholder"></view>
        </view>
      </view>
    </view>

    <!-- 设备ID输入区域 -->
    <view class="form-section">
      <text class="form-label">设备ID:</text>
      <input
        type="text"
        v-model="deviceId"
        placeholder="请输入设备ID"
        class="input"
      />
    </view>

    <!-- 时间信息区域 -->
    <view class="form-section">
      <text class="form-label">检查时间:</text>
      <view class="time-display">
        <text class="time-text">{{ currentTime }}</text>
      </view>
    </view>

    <!-- 状态选择区域 -->
    <view class="form-section">
      <text class="form-label">检查状态:</text>
      <view class="status-selector">
        <view
          class="status-option"
          :class="{ active: selectedStatus === 'normal' }"
          @click="selectStatus('normal')"
        >
          <text class="status-text">✅ 正常</text>
        </view>
        <view
          class="status-option"
          :class="{ active: selectedStatus === 'unknown' }"
          @click="selectStatus('unknown')"
        >
          <text class="status-text">❓ 未知</text>
        </view>
        <view
          class="status-option"
          :class="{ active: selectedStatus === 'abnormal' }"
          @click="selectStatus('abnormal')"
        >
          <text class="status-text">⚠️ 异常</text>
        </view>
      </view>
    </view>

    <!-- 异常信息录入区域 - 只有选择异常时才显示 -->
    <view v-if="selectedStatus === 'abnormal'" class="form-section">
      <text class="form-label">异常情况描述:</text>
      <textarea
        v-model="abnormalInfo"
        placeholder="请详细描述异常情况"
        class="textarea"
      ></textarea>
    </view>

    <!-- 底部按钮区域 -->
    <view class="button-group">
      <button
        class="btn btn-primary"
        @click="handleUpload"
        :disabled="!selectedStatus"
      >
        {{ getUploadButtonText() }}
      </button>
      <button
        v-if="selectedStatus === 'abnormal'"
        class="btn btn-warning"
        @click="handleSubmit"
      >
        提交异常信息
      </button>
    </view>
  </view>
</template>

<script>
import { saveUpwardShotRecord } from "@/api/inspection";

export default {
  data() {
    return {
      photo: "",
      photoUrl: "", // 存储上传后的图片URL
      abnormalInfo: "",
      deviceId: "", // 设备ID
      selectedStatus: "", // 选择的状态：normal, unknown, abnormal
      currentTime: "", // 当前时间显示
      uploadTime: "", // 实际上传时间
      timeInterval: null, // 时间更新定时器
    };
  },
  onLoad(options) {
    console.log("仰拍页面加载", options);
    // 如果从其他页面传递了设备ID，则自动填充
    if (options && options.deviceId) {
      this.deviceId = options.deviceId;
    }
    // 初始化时间
    this.initTime();
    this.startTimeUpdate();
  },
  onShow() {
    console.log("仰拍页面显示");
    // 确保时间更新正常运行
    if (!this.timeInterval) {
      this.initTime();
      this.startTimeUpdate();
    }
  },
  onHide() {
    // 页面隐藏时清除定时器，节省资源
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }
  },
  onUnload() {
    // 清除定时器
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  },
  methods: {
    // 初始化时间
    initTime() {
      this.updateCurrentTime();
    },

    // 开始时间更新
    startTimeUpdate() {
      // 每秒更新一次时间显示
      this.timeInterval = setInterval(() => {
        this.updateCurrentTime();
      }, 1000);
    },

    // 更新当前时间显示
    updateCurrentTime() {
      const now = new Date();
      this.currentTime = this.formatDateTime(now);
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 选择状态
    selectStatus(status) {
      this.selectedStatus = status;
      console.log('选择状态:', status);

      // 如果不是异常状态，清空异常信息
      if (status !== 'abnormal') {
        this.abnormalInfo = '';
      }
    },
    // 获取上传按钮文本
    getUploadButtonText() {
      if (!this.selectedStatus) {
        return '请先选择检查状态';
      }

      switch (this.selectedStatus) {
        case 'normal':
          return '上传正常检查结果';
        case 'unknown':
          return '上传未知状态结果';
        case 'abnormal':
          return '上传异常检查结果';
        default:
          return '图片回传';
      }
    },
    takePhoto() {
      console.log("开始拍照");
      uni.chooseImage({
        count: 1,
        sourceType: ["camera"],
        success: (res) => {
          console.log("拍照成功:", res);
          this.photo = res.tempFilePaths[0];
        },
        fail: (err) => {
          console.error("拍照失败:", err);
          uni.showToast({
            title: "拍照失败",
            icon: "none",
          });
        },
      });
    },
    async handleUpload() {
      console.log("开始上传图片");

      // 检查登录状态
      const token = uni.getStorageSync('token');
      const userInfo = uni.getStorageSync('userInfo');

      if (!token || !userInfo) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/index'
          });
        }, 1500);
        return;
      }

      if (!this.photo) {
        uni.showToast({
          title: "请先拍照",
          icon: "none",
        });
        return;
      }

      if (!this.selectedStatus) {
        uni.showToast({
          title: "请先选择检查状态",
          icon: "none",
        });
        return;
      }

      // 如果选择异常但没有填写异常信息，提示用户
      if (this.selectedStatus === 'abnormal' && !this.abnormalInfo.trim()) {
        uni.showToast({
          title: "请填写异常情况描述",
          icon: "none",
        });
        return;
      }

      // 显示上传中提示
      uni.showLoading({
        title: "上传中...",
      });

      try {
        console.log('开始保存仰拍记录');

        // 保存仰拍记录（包含图片上传）
        const recordData = {
          deviceId: this.deviceId || 'PIPE001',
          imageUrl: this.photo,
          uploadTime: new Date().toISOString(),
          inspector: userInfo.displayName || userInfo.username || '当前用户',
          status: this.selectedStatus === 'normal' ? '正常' : (this.selectedStatus === 'abnormal' ? '异常' : '未知'),
          abnormalInfo: this.selectedStatus === 'abnormal' ? this.abnormalInfo.trim() : '',
          location: '',
          notes: ''
        };

        console.log('仰拍记录数据:', recordData);
        const response = await saveUpwardShotRecord(recordData);
        console.log('仰拍保存响应:', response);

        if (response.success) {
          // 记录实际上传时间
          this.uploadTime = new Date().toISOString();
          const uploadTimeFormatted = this.formatDateTime(new Date());

          // 数据已经保存成功，不需要重复保存

          // 数据保存成功后立即通知其他页面
          console.log('发送数据更新事件');
          uni.$emit('dataUpdated', {
            type: 'upward-shot',
            data: response.data,
            saveResult: response
          });

          // 显示成功提示，包含状态信息和时间
          const statusText = {
            'normal': '正常',
            'unknown': '未知',
            'abnormal': '异常'
          }[this.selectedStatus];

          uni.showModal({
            title: '上传成功',
            content: `状态: ${statusText}\n上传时间: ${uploadTimeFormatted}`,
            showCancel: false,
            confirmText: '确定',
            success: () => {
              setTimeout(() => {
                // 跳转到历史数据页面
                uni.switchTab({
                  url: '/pages/history/index'
                });
              }, 500);
            }
          });
        } else {
          uni.showToast({
            title: response.message || "上传失败",
            icon: "none",
          });
        }
      } catch (error) {
        uni.showToast({
          title: "上传失败: " + error.message,
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    },
    async handleSubmit() {
      console.log("开始提交异常信息");

      // 检查登录状态
      const token = uni.getStorageSync('token');
      const userInfo = uni.getStorageSync('userInfo');

      if (!token || !userInfo) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        });
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/index'
          });
        }, 1500);
        return;
      }

      if (!this.abnormalInfo.trim()) {
        uni.showToast({
          title: "请输入异常情况",
          icon: "none",
        });
        return;
      }

      if (!this.photoUrl) {
        uni.showToast({
          title: "请先上传照片",
          icon: "none",
        });
        return;
      }

      // 显示提交中提示
      uni.showLoading({
        title: "提交中...",
      });

      // 提交异常信息
      try {
        // 使用已导入的saveUpwardShotRecord
        const recordData = {
          deviceId: this.deviceId || 'PIPE001',
          type: 'upward-shot',
          imageUrl: this.photoUrl,
          abnormalInfo: this.abnormalInfo.trim(),
          inspector: '当前用户',
          status: '异常',
          uploadTime: new Date().toISOString()
        };

        await saveUpwardShotRecord(recordData);

        uni.hideLoading();
        uni.showToast({
          title: "提交成功",
          icon: "success",
          duration: 2000,
          success: () => {
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
          },
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: "提交失败: " + error.message,
          icon: "none",
        });
      }
    },
  },
};
</script>

<style>
/* 容器样式 */
.container {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  text-align: center;
}

/* 拍照区域样式 */
.photo-area {
  display: flex;
  justify-content: center;
  padding: 20rpx;
  background-color: #ffffff;
}

/* 拍照区域各部分样式 */
.photo-section {
  flex: 1;
  margin: 0 10rpx;
}

/* 拍照区域标签样式 */
.section-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
  text-align: center;
}

/* 拍照框样式 */
.photo-box {
  width: 100%;
  padding-bottom: 100%;
  position: relative;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

/* 预览图片样式 */
.preview-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8rpx;
}

/* 占位符样式 */
.placeholder {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

/* 表单区域样式 */
.form-section {
  padding: 20rpx;
}

/* 表单标签样式 */
.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

/* 输入框样式 */
.input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 文本框样式 */
.textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  gap: 20rpx;
  margin-top: auto;
}

/* 按钮通用样式 */
.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* 主要按钮样式 */
.btn-primary {
  background-color: #007aff;
  color: #ffffff;
}

/* 警告按钮样式 */
.btn-warning {
  background-color: #ff3b30;
  color: #ffffff;
}

/* 禁用按钮样式 */
.btn:disabled {
  background-color: #ccc !important;
  color: #999 !important;
  cursor: not-allowed;
}

/* 状态选择器样式 */
.status-selector {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.status-option {
  flex: 1;
  margin: 0 10rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.status-option.active {
  border-color: #007AFF;
  background-color: #e3f2fd;
}

.status-option:first-child {
  margin-left: 0;
}

.status-option:last-child {
  margin-right: 0;
}

.status-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.status-option.active .status-text {
  color: #007AFF;
  font-weight: bold;
}

/* 时间显示样式 */
.time-display {
  background-color: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 10rpx;
}

.time-text {
  font-size: 32rpx;
  color: #007AFF;
  font-weight: bold;
  text-align: center;
  display: block;
}
</style>
