// 检查用户信息工具
export const checkUserInfo = () => {
  console.log('=== 检查用户信息 ===');
  
  const userInfo = uni.getStorageSync('userInfo');
  const token = uni.getStorageSync('token');
  
  console.log('本地存储的用户信息:', userInfo);
  console.log('Token存在:', !!token);
  
  if (userInfo) {
    console.log('用户信息字段:');
    Object.keys(userInfo).forEach(key => {
      console.log(`- ${key}:`, userInfo[key]);
    });
    
    if (!userInfo.id) {
      console.error('❌ 用户信息缺少ID字段！需要重新登录');
      return false;
    } else {
      console.log('✅ 用户信息包含ID字段:', userInfo.id);
      return true;
    }
  } else {
    console.error('❌ 没有用户信息！需要登录');
    return false;
  }
};

// 在浏览器控制台中运行这个函数来检查
if (typeof window !== 'undefined') {
  window.checkUserInfo = checkUserInfo;
}
