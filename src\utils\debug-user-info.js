// 调试用户信息工具
export const debugUserInfo = () => {
  console.log('=== 调试用户信息 ===');
  
  // 检查本地存储
  const userInfo = uni.getStorageSync('userInfo');
  console.log('本地存储的用户信息:', userInfo);
  
  // 检查token
  const token = uni.getStorageSync('token');
  console.log('本地存储的token:', token ? '存在' : '不存在');
  
  // 检查用户信息字段
  if (userInfo) {
    console.log('用户信息字段检查:');
    console.log('- id:', userInfo.id);
    console.log('- username:', userInfo.username);
    console.log('- displayName:', userInfo.displayName);
    console.log('- name:', userInfo.name);
    console.log('- email:', userInfo.email);
    console.log('- role:', userInfo.role);
    
    if (!userInfo.id) {
      console.error('❌ 用户信息缺少ID字段！');
      return false;
    } else {
      console.log('✅ 用户信息包含ID字段');
      return true;
    }
  } else {
    console.error('❌ 本地存储中没有用户信息！');
    return false;
  }
};

// 修复用户信息ID问题
export const fixUserInfoId = async () => {
  try {
    const userInfo = uni.getStorageSync('userInfo');
    const token = uni.getStorageSync('token');
    
    if (!userInfo || !userInfo.id) {
      console.log('尝试从服务器获取用户信息...');
      
      // 从服务器获取用户信息
      const response = await uni.request({
        url: 'http://localhost:3001/api/auth/me',
        method: 'GET',
        header: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.statusCode === 200) {
        const serverUser = response.data.user;
        
        // 更新本地存储
        const updatedUserInfo = {
          id: serverUser.id,
          username: serverUser.username,
          displayName: serverUser.display_name,
          name: serverUser.real_name,
          role: serverUser.role,
          employeeId: serverUser.employee_id,
          email: serverUser.email,
          phone: serverUser.phone,
          department: serverUser.department,
          avatar: serverUser.avatar ? `http://localhost:3001${serverUser.avatar}` : '/static/logo.png',
          status: serverUser.status
        };
        
        uni.setStorageSync('userInfo', updatedUserInfo);
        console.log('✅ 用户信息已从服务器更新:', updatedUserInfo);
        return updatedUserInfo;
      } else {
        console.error('❌ 从服务器获取用户信息失败:', response);
        return null;
      }
    } else {
      console.log('✅ 用户信息已包含ID，无需修复');
      return userInfo;
    }
  } catch (error) {
    console.error('❌ 修复用户信息失败:', error);
    return null;
  }
};
