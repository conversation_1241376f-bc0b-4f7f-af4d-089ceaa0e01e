// 网络请求工具
import { getCurrentConfig, useMockData } from '@/config/index.js';

// 请求拦截器
const requestInterceptor = (options) => {
  const config = getCurrentConfig();
  
  // 添加基础URL
  if (config.baseURL && !options.url.startsWith('http')) {
    options.url = config.baseURL + options.url;
  }
  
  // 添加超时时间
  options.timeout = options.timeout || config.timeout;
  
  // 添加请求头
  options.header = options.header || {};
  options.header['Content-Type'] = 'application/json';
  
  // 添加认证token
  const token = uni.getStorageSync('token');
  if (token) {
    options.header['Authorization'] = `Bearer ${token}`;
  }
  
  console.log('发起请求:', options);
  return options;
};

// 响应拦截器
const responseInterceptor = (response, options) => {
  console.log('收到响应:', response);
  
  // 处理HTTP状态码
  if (response.statusCode >= 200 && response.statusCode < 300) {
    return response.data;
  } else if (response.statusCode === 401) {
    // 未授权，清除token并跳转到登录页
    uni.removeStorageSync('token');
    uni.removeStorageSync('userInfo');
    uni.reLaunch({
      url: '/pages/login/index'
    });
    throw new Error('登录已过期，请重新登录');
  } else {
    throw new Error(`请求失败: ${response.statusCode}`);
  }
};

// 统一请求方法
export const request = (options) => {
  return new Promise((resolve, reject) => {
    // 检查是否使用模拟数据
    if (useMockData()) {
      console.log('🔄 模拟数据模式，跳过真实网络请求');
      // 在模拟数据模式下，直接返回成功响应
      setTimeout(() => {
        resolve({
          success: true,
          message: '模拟请求成功',
          data: {}
        });
      }, 100);
      return;
    }
    
    try {
      // 请求拦截
      const processedOptions = requestInterceptor(options);
      
      // 发起请求
      uni.request({
        ...processedOptions,
        success: (response) => {
          try {
            const data = responseInterceptor(response, processedOptions);
            resolve(data);
          } catch (error) {
            reject(error);
          }
        },
        fail: (error) => {
          console.error('请求失败:', error);
          
          // 处理网络错误
          if (error.errMsg && error.errMsg.includes('timeout')) {
            reject(new Error('请求超时，请检查网络连接'));
          } else if (error.errMsg && error.errMsg.includes('fail')) {
            reject(new Error('网络连接失败，请检查网络设置'));
          } else {
            reject(new Error('请求失败: ' + (error.errMsg || '未知错误')));
          }
        }
      });
    } catch (error) {
      reject(error);
    }
  });
};

// GET请求
export const get = (url, params = {}) => {
  return request({
    url,
    method: 'GET',
    data: params
  });
};

// POST请求
export const post = (url, data = {}) => {
  return request({
    url,
    method: 'POST',
    data
  });
};

// PUT请求
export const put = (url, data = {}) => {
  return request({
    url,
    method: 'PUT',
    data
  });
};

// DELETE请求
export const del = (url, params = {}) => {
  return request({
    url,
    method: 'DELETE',
    data: params
  });
};

// 文件上传
export const upload = (url, filePath, formData = {}) => {
  return new Promise((resolve, reject) => {
    // 在模拟数据模式下，模拟文件上传
    if (useMockData()) {
      console.log('🔄 模拟数据模式，模拟文件上传');
      setTimeout(() => {
        resolve({
          success: true,
          message: '模拟上传成功',
          data: {
            url: filePath,
            filename: filePath.split('/').pop()
          }
        });
      }, 500);
      return;
    }
    
    const config = getCurrentConfig();
    const token = uni.getStorageSync('token');
    
    uni.uploadFile({
      url: config.baseURL + url,
      filePath,
      name: 'file',
      formData,
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (response) => {
        try {
          const data = JSON.parse(response.data);
          resolve(data);
        } catch (error) {
          reject(new Error('响应解析失败'));
        }
      },
      fail: (error) => {
        reject(new Error('上传失败: ' + error.errMsg));
      }
    });
  });
};
