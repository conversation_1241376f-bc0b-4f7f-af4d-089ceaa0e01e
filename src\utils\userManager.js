// 用户信息管理工具
import { getLocalPhotos } from '@/api/photo.js';

// 获取当前登录用户信息
export const getCurrentUserInfo = () => {
  try {
    const token = uni.getStorageSync('token');
    const userInfo = uni.getStorageSync('userInfo');
    
    if (!token || !userInfo) {
      return null;
    }
    
    // 返回完整的用户信息
    return {
      ...userInfo,
      avatar: userInfo.avatar || '/static/logo.png', // 使用默认头像
      displayName: userInfo.username || userInfo.name || '未知用户',
      role: userInfo.role || '巡检员',
      employeeId: userInfo.employeeId || userInfo.username || 'N/A',
      phone: userInfo.phone || '未设置'
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

// 更新用户信息
export const updateUserInfo = (newUserInfo) => {
  try {
    const currentUserInfo = uni.getStorageSync('userInfo') || {};
    const updatedUserInfo = {
      ...currentUserInfo,
      ...newUserInfo
    };
    
    uni.setStorageSync('userInfo', updatedUserInfo);
    console.log('用户信息更新成功:', updatedUserInfo);
    return true;
  } catch (error) {
    console.error('更新用户信息失败:', error);
    return false;
  }
};

// 获取用户统计数据
export const getUserStats = () => {
  try {
    const userInfo = getCurrentUserInfo();
    if (!userInfo) {
      return getDefaultStats();
    }
    
    // 获取当前用户的照片数据
    const photos = getLocalPhotos();
    
    // 计算统计数据
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    // 筛选本月数据
    const thisMonthPhotos = photos.filter(photo => {
      const photoDate = new Date(photo.uploadTime || photo.date);
      return photoDate.getMonth() === currentMonth && 
             photoDate.getFullYear() === currentYear;
    });
    
    // 计算各种统计
    const totalInspections = thisMonthPhotos.length;

    // 计算异常次数：异常=1，未知=0.5，正常=0
    let abnormalCount = 0;
    thisMonthPhotos.forEach(photo => {
      const status = photo.status;
      if (status === 'abnormal' || status === '异常') {
        abnormalCount += 1;
      } else if (status === 'unknown' || status === '未知') {
        abnormalCount += 0.5;
      }
      // 正常状态不增加异常计数
    });

    console.log(`本月巡检统计: 总数=${totalInspections}, 异常计数=${abnormalCount}`);

    const normalInspections = totalInspections - abnormalCount;
    const normalRate = totalInspections > 0 ?
      Math.round((normalInspections / totalInspections) * 100) : 100;
    
    // 设备状态分布（模拟数据，基于巡检结果）
    const deviceStats = calculateDeviceStats(thisMonthPhotos);
    
    // 计算全部时间的统计
    let allTimeAbnormalCount = 0;
    photos.forEach(photo => {
      const status = photo.status;
      if (status === 'abnormal' || status === '异常') {
        allTimeAbnormalCount += 1;
      } else if (status === 'unknown' || status === '未知') {
        allTimeAbnormalCount += 0.5;
      }
    });

    console.log(`全部时间统计: 总数=${photos.length}, 异常计数=${allTimeAbnormalCount}`);

    return {
      thisMonth: {
        total: totalInspections,
        abnormal: abnormalCount,
        normal: normalInspections,
        normalRate: normalRate
      },
      deviceStatus: deviceStats,
      allTime: {
        total: photos.length,
        abnormal: allTimeAbnormalCount,
        normal: photos.length - allTimeAbnormalCount
      }
    };
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return getDefaultStats();
  }
};

// 计算设备状态分布
const calculateDeviceStats = (photos) => {
  if (photos.length === 0) {
    return {
      normal: 85,
      maintenance: 10,
      fault: 5
    };
  }
  
  const abnormalCount = photos.filter(p => p.status === '异常').length;
  const abnormalRate = (abnormalCount / photos.length) * 100;
  
  // 根据异常率计算设备状态分布
  let normal, maintenance, fault;
  
  if (abnormalRate <= 5) {
    normal = 90;
    maintenance = 8;
    fault = 2;
  } else if (abnormalRate <= 15) {
    normal = 80;
    maintenance = 15;
    fault = 5;
  } else {
    normal = 70;
    maintenance = 20;
    fault = 10;
  }
  
  return { normal, maintenance, fault };
};

// 默认统计数据
const getDefaultStats = () => {
  return {
    thisMonth: {
      total: 0,
      abnormal: 0,
      normal: 0,
      normalRate: 100
    },
    deviceStatus: {
      normal: 85,
      maintenance: 10,
      fault: 5
    },
    allTime: {
      total: 0,
      abnormal: 0,
      normal: 0
    }
  };
};

// 检查用户是否已登录
export const isUserLoggedIn = () => {
  const token = uni.getStorageSync('token');
  const userInfo = uni.getStorageSync('userInfo');
  return !!(token && userInfo);
};

// 退出登录
export const logout = () => {
  try {
    uni.removeStorageSync('token');
    uni.removeStorageSync('userInfo');
    console.log('用户已退出登录');
    return true;
  } catch (error) {
    console.error('退出登录失败:', error);
    return false;
  }
};

// 格式化显示数字
export const formatNumber = (num) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  }
  return num.toString();
};
