<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成导航图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .icon-preview {
            width: 48px;
            height: 48px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .icon-preview svg {
            width: 32px;
            height: 32px;
        }
        .icon-normal {
            color: #999999;
        }
        .icon-selected {
            color: #007AFF;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 5px;
        }
        .download-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #0056CC;
        }
    </style>
</head>
<body>
    <h1>导航图标生成器</h1>
    <p>点击下载按钮生成PNG格式的图标</p>

    <div class="icon-container">
        <!-- 首页图标 -->
        <div class="icon-item">
            <h3>首页</h3>
            <div class="icon-preview icon-normal">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="icon-preview icon-selected">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <button class="download-btn" onclick="generateIcon('home')">下载图标</button>
        </div>

        <!-- 历史数据图标 -->
        <div class="icon-item">
            <h3>历史数据</h3>
            <div class="icon-preview icon-normal">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="icon-preview icon-selected">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <button class="download-btn" onclick="generateIcon('history')">下载图标</button>
        </div>

        <!-- 数据分析图标 -->
        <div class="icon-item">
            <h3>数据分析</h3>
            <div class="icon-preview icon-normal">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M9 17V7M13 17V11M17 17V15M21 21H3L3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="icon-preview icon-selected">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M9 17V7M13 17V11M17 17V15M21 21H3L3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <button class="download-btn" onclick="generateIcon('analysis')">下载图标</button>
        </div>

        <!-- 个人中心图标 -->
        <div class="icon-item">
            <h3>个人中心</h3>
            <div class="icon-preview icon-normal">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="icon-preview icon-selected">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <button class="download-btn" onclick="generateIcon('profile')">下载图标</button>
        </div>
    </div>

    <script>
        const icons = {
            home: 'M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15',
            history: 'M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z',
            analysis: 'M9 17V7M13 17V11M17 17V15M21 21H3L3 3',
            profile: 'M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z'
        };

        function generateIcon(iconName) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const size = 48; // 48x48 for normal, 96x96 for @2x
            
            canvas.width = size;
            canvas.height = size;
            
            // 创建SVG
            const svg = `
                <svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="${icons[iconName]}" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                </svg>
            `;
            
            const svgSelected = `
                <svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="${icons[iconName]}" stroke="#007AFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                </svg>
            `;
            
            // 生成普通状态图标
            generatePNG(svg, `${iconName}.png`);
            
            // 生成选中状态图标
            generatePNG(svgSelected, `${iconName}_selected.png`);
        }
        
        function generatePNG(svgString, filename) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 48;
            canvas.height = 48;
            
            const img = new Image();
            const svgBlob = new Blob([svgString], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0);
                
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = filename;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(url);
                    URL.revokeObjectURL(link.href);
                });
            };
            
            img.src = url;
        }
    </script>
</body>
</html>
