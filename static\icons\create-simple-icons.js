// 创建简单的图标文件
// 这个脚本可以在浏览器控制台中运行来生成图标

const createIcon = (pathData, color, size = 48) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = size;
  canvas.height = size;
  
  // 清除画布
  ctx.clearRect(0, 0, size, size);
  
  // 创建SVG字符串
  const svgString = `
    <svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="${pathData}" stroke="${color}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    </svg>
  `;
  
  return new Promise((resolve) => {
    const img = new Image();
    const svgBlob = new Blob([svgString], {type: 'image/svg+xml'});
    const url = URL.createObjectURL(svgBlob);
    
    img.onload = function() {
      ctx.drawImage(img, 0, 0);
      canvas.toBlob((blob) => {
        resolve(blob);
        URL.revokeObjectURL(url);
      });
    };
    
    img.src = url;
  });
};

// 图标路径数据
const iconPaths = {
  home: 'M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15',
  history: 'M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z',
  analysis: 'M9 17V7M13 17V11M17 17V15M21 21H3L3 3',
  profile: 'M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z'
};

// 生成所有图标
const generateAllIcons = async () => {
  const normalColor = '#999999';
  const selectedColor = '#007AFF';
  
  for (const [name, path] of Object.entries(iconPaths)) {
    // 生成普通状态图标
    const normalBlob = await createIcon(path, normalColor);
    const normalUrl = URL.createObjectURL(normalBlob);
    const normalLink = document.createElement('a');
    normalLink.download = `${name}.png`;
    normalLink.href = normalUrl;
    normalLink.click();
    
    // 生成选中状态图标
    const selectedBlob = await createIcon(path, selectedColor);
    const selectedUrl = URL.createObjectURL(selectedBlob);
    const selectedLink = document.createElement('a');
    selectedLink.download = `${name}_selected.png`;
    selectedLink.href = selectedUrl;
    selectedLink.click();
    
    // 清理URL
    setTimeout(() => {
      URL.revokeObjectURL(normalUrl);
      URL.revokeObjectURL(selectedUrl);
    }, 1000);
  }
};

// 在浏览器控制台中运行: generateAllIcons()
console.log('运行 generateAllIcons() 来生成所有图标');
