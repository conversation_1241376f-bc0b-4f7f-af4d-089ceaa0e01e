# uni-app 项目文件详细说明

## 项目目录结构

### 顶级目录

- **dist**: 构建后的生产环境文件，包含编译、压缩后的代码，直接用于部署
- **node_modules**: 存放项目依赖的 npm 包，通过 package.json 管理，不需要手动修改
- **src**: 源代码目录，项目的主要开发区域

### src 目录结构

- **pages/**: 存放所有页面组件，按功能模块划分子文件夹
- **static/**: 存放静态资源(图片、图标、字体等)
- **components/**: 可复用的组件（如果有）
- **store/**: Vuex 状态管理（如果使用）
- **utils/**: 工具函数和通用方法（如果有）
- **api/**: API 调用封装（如果有）

## 核心配置文件

### 1. pages.json

**文件路径**: src/pages.json  
**文件格式**: JSON  
**文件图标**: {}

**主要功能**:

- uni-app 项目的全局配置文件，类似于小程序的 app.json
- 定义应用的所有页面路由配置
- 配置导航栏样式、底部选项卡等 UI 元素
- 设置全局样式和窗口表现

**详细配置项**:

- **pages 数组**: 定义所有页面路径和窗口表现
  - path: 页面路径
  - style: 页面样式设置
    - navigationBarTitleText: 页面标题
    - navigationStyle: 导航栏样式(default/custom)
    - enablePullDownRefresh: 是否开启下拉刷新
- **globalStyle**: 全局默认样式，会被页面样式覆盖
  - navigationBarTextStyle: 导航栏标题颜色(black/white)
  - navigationBarBackgroundColor: 导航栏背景色
  - backgroundColor: 窗口背景色
- **tabBar**: 底部选项卡配置

  - color: 未选中的文字颜色
  - selectedColor: 选中时的文字颜色
  - backgroundColor: 背景色
  - list: 选项卡列表
    - pagePath: 页面路径
    - text: 显示文本
    - iconPath: 未选中图标路径
    - selectedIconPath: 选中图标路径

- **condition**: 启动模式配置(开发环境)
- **subPackages**: 分包加载配置

### 2. manifest.json

**文件路径**: src/manifest.json  
**文件格式**: JSON  
**文件图标**: {}

**主要功能**:

- 应用的配置清单
- 用于各平台打包和发布的关键配置文件
- 包含应用标识、版本号、图标、权限等信息

**详细配置项**:

- **name**: 应用名称
- **appid**: 应用标识
- **description**: 应用描述
- **versionName**: 版本名称(如 1.0.0)
- **versionCode**: 版本号(整数值)
- **app-plus**: App 特有配置
  - **modules**: 依赖的原生模块
  - **distribute**: 发布配置
    - **android**: Android 平台配置(包名、权限、签名等)
    - **ios**: iOS 平台配置(BundleID 等)
- **h5**: H5 特有配置
- **mp-weixin**: 微信小程序配置
- **mp-alipay**: 支付宝小程序配置
- **mp-baidu**: 百度小程序配置
- **permission**: 应用权限配置(相机、位置等)

### 3. package.json

**文件路径**: package.json  
**文件格式**: JSON  
**文件图标**: {}

**主要功能**:

- npm 项目标准配置文件
- 定义项目的元数据和依赖关系
- 配置项目脚本命令
- 管理项目版本

**详细配置项**:

- **name**: 项目名称
- **version**: 项目版本
- **description**: 项目描述
- **main**: 入口文件
- **scripts**: 可执行的脚本命令
  - dev: 开发环境命令
  - build: 构建生产环境命令
- **dependencies**: 生产环境依赖
- **devDependencies**: 开发环境依赖
- **author**: 作者信息
- **license**: 许可证类型

### 4. package-lock.json

**文件路径**: package-lock.json  
**文件格式**: JSON  
**文件图标**: {}

**主要功能**:

- 锁定 npm 依赖的精确版本
- 确保团队成员使用一致的依赖版本
- 加速 npm 安装过程
- 自动生成，通常不需要手动修改

### 5. vite.config.js

**文件路径**: vite.config.js  
**文件格式**: JavaScript  
**文件图标**: ⚡

**主要功能**:

- Vite 构建工具的配置文件
- 定制构建、打包过程
- 配置开发服务器
- 设置项目别名和插件

**详细配置项**:

- **plugins**: 使用的 Vite 插件列表
- **resolve.alias**: 路径别名设置
- **server**: 开发服务器配置
- **build**: 构建选项配置
- **css**: CSS 相关配置
- **optimizeDeps**: 依赖优化选项

## 核心应用文件

### 1. App.vue

**文件路径**: src/App.vue  
**文件格式**: Vue 组件  
**文件图标**: ✓

**主要功能**:

- 应用的根组件
- 处理应用生命周期
- 定义全局样式
- 初始化全局状态和检测环境

**组件结构**:

- **\<script\>**: 组件逻辑
  - onLaunch(): 应用初始化时执行
  - onShow(): 应用显示/切换到前台时执行
  - onHide(): 应用隐藏/切换到后台时执行
  - checkEnvironment(): 检测运行环境(H5 或原生)
- **\<style\>**: 全局样式定义

### 2. main.js

**文件路径**: src/main.js  
**文件格式**: JavaScript  
**文件图标**: JS

**主要功能**:

- 应用的 JavaScript 入口
- 创建并配置 Vue 应用实例
- 注册全局组件、插件和指令
- 导出 createApp 函数供框架调用

**主要代码**:

```javascript
import { createSSRApp } from "vue";
import App from "./App.vue";

export function createApp() {
  const app = createSSRApp(App);
  // 注册全局组件、插件等
  return {
    app,
  };
}
```

### 3. uni.scss

**文件路径**: src/uni.scss  
**文件格式**: SCSS  
**文件图标**: 🎨

**主要功能**:

- 全局 SCSS 变量和样式
- 定义主题色彩和间距变量
- 提供公共样式混合(mixin)
- 被其他组件样式文件引用

**示例内容**:

```scss
/* 颜色变量 */
$uni-primary: #007aff;
$uni-success: #4cd964;
$uni-warning: #f0ad4e;
$uni-error: #dd524d;

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 间距 */
$uni-spacing-sm: 8rpx;
$uni-spacing-base: 16rpx;
$uni-spacing-lg: 24rpx;
```

### 4. androidPrivacy.json

**文件路径**: src/androidPrivacy.json  
**文件格式**: JSON  
**文件图标**: {}

**主要功能**:

- Android 隐私政策配置文件
- 满足 Google Play 商店政策要求
- 声明应用使用的隐私相关功能和权限
- 配置隐私协议 URL 和说明文本

**详细配置项**:

- **version**: 隐私政策版本
- **prompt**: 用户提示文本
- **privacyItems**: 隐私项目数组
  - **title**: 隐私项目标题(如"位置信息")
  - **details**: 具体说明
  - **necessary**: 是否必须
- **policyUrl**: 隐私政策网址

### 5. TypeScript 类型定义文件

#### shime-uni.d.ts / shims-uni.d.ts

**文件路径**: src/shime-uni.d.ts 或 src/shims-uni.d.ts  
**文件格式**: TypeScript 声明文件  
**文件图标**: TS

**主要功能**:

- 为 uni-app API 提供 TypeScript 类型定义
- 增强 IDE 代码提示和类型检查
- 定义全局命名空间和接口
- 扩展 Vue 类型

**主要内容**:

```typescript
// 声明uni全局对象
declare namespace UniApp {
  interface Uni {
    request(options): Promise<any>;
    navigateTo(options): Promise<any>;
    // 其他API类型定义
  }
}

// 声明全局uni对象
declare const uni: UniApp.Uni;

// 扩展Vue类型
declare module "vue" {
  interface ComponentCustomProperties {
    uni: typeof uni;
  }
}
```

### 6. .gitignore

**文件路径**: .gitignore  
**文件格式**: 文本  
**文件图标**: 👁

**主要功能**:

- Git 版本控制忽略配置
- 指定不需要提交到代码仓库的文件和目录
- 避免提交临时文件、构建产物和敏感信息
- 减小仓库体积

**典型内容**:

```
node_modules/
dist/
unpackage/
.DS_Store
*.log
.vscode/
```

## 页面组件文件

### 1. 首页 (src/pages/home/<USER>

**文件格式**: Vue 组件  
**主要功能**:

- 应用的主入口页面
- 显示用户信息和巡检统计
- 提供主要功能入口按钮
- 展示预警信息

**组件特点**:

- 用户信息展示区
- 功能按钮区域(设备设置、扫一扫等)
- 预警功能按钮
- 适配 H5 和原生环境的页面跳转逻辑

### 2. 三角拍功能 (src/pages/triangle-shot/index.vue)

**文件格式**: Vue 组件  
**主要功能**:

- 多角度拍摄设备的专用功能
- 左中右三个角度拍照
- 异常信息录入
- 图片上传功能

**组件特点**:

- 使用 Vue 3 组合式 API(setup)
- TypeScript 类型定义
- 照片预览和存储
- 调用原生相机 API
- 表单验证和数据提交

### 3. 预警页面 (src/pages/alert/level\*.vue)

**文件格式**: Vue 组件  
**主要功能**:

- 显示不同级别的设备异常预警
- 展示设备位置和信息
- 提供短信通知和电话联系功能
- 导航到其他功能页面

**组件特点**:

- 地图组件集成
- 标签式异常类型展示
- 设备信息显示
- 通讯功能(短信、电话)
- 使用 Vue 选项式 API

### 4. 历史数据 (src/pages/history/index.vue)

**文件格式**: Vue 组件  
**主要功能**:

- 展示历史巡检记录
- 显示设备 ID、日期、巡检人员和状态
- 区分正常和异常状态
- 提供数据筛选功能

## 技术特点

### 1. 兼容性处理

- 使用条件编译(`#ifdef H5`和`#ifndef H5`)区分 H5 和原生环境
- 适配不同平台的导航方式(window.location 和 uni.navigateTo)
- 根据环境动态加载组件和 API

### 2. UI 组件

- 使用 flex 布局实现响应式界面
- 统一的色彩方案和圆角设计
- 基于 rpx 的弹性布局
- 统一的卡片式设计

### 3. API 调用

- uni.chooseImage: 调用相机拍照
- uni.makePhoneCall: 拨打电话
- uni.showToast: 显示提示信息
- uni.navigateTo/switchTab: 页面导航
- 多种数据存储 API(uni.setStorage 等)

### 4. Vue 特性应用

- 组合式 API(setup())和选项式 API 混合使用
- 响应式数据管理(ref, reactive)
- 计算属性和侦听器(computed, watch)
- 条件渲染(v-if/v-else)
- 列表渲染(v-for)
- 事件处理(@click)
- 生命周期钩子(onMounted 等)

## 开发和构建流程

### 1. 开发环境启动

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev:h5        # H5版本
npm run dev:app       # App版本
npm run dev:mp-weixin # 微信小程序
```

### 2. 构建生产版本

```bash
# 构建H5版本
npm run build:h5

# 构建App版本
npm run build:app

# 构建小程序版本
npm run build:mp-weixin
```

### 3. 发布流程

- H5 版本: 将 dist 目录部署到 web 服务器
- App 版本: 使用 HBuilderX 云打包生成安装包
- 小程序: 使用开发者工具上传代码

## 学习资源

- [uni-app 官方文档](https://uniapp.dcloud.io/)
- [Vue 3 官方文档](https://v3.cn.vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/zh/)
- [Vite 官方文档](https://cn.vitejs.dev/)
![1749886724245](image/uni-app项目文件详解/1749886724245.png)![1749886726236](image/uni-app项目文件详解/1749886726236.png)![1749886727306](image/uni-app项目文件详解/1749886727306.png)![1749886727620](image/uni-app项目文件详解/1749886727620.png)![1749886729579](image/uni-app项目文件详解/1749886729579.png)![1749886800700](image/uni-app项目文件详解/1749886800700.png)
## 关于 Markdown 格式

### Markdown 文件格式介绍

**文件扩展名**: .md 或 .markdown  
**文件图标**: 📝

**主要特点**:

- 轻量级标记语言，创建于 2004 年
- 易读易写，纯文本格式
- 可以转换成 HTML、PDF 等其他格式
- 被广泛用于编写文档、README 文件、论坛帖子等

**基本语法**:

- 标题: 使用`#`符号，1-6 个代表不同级别标题
- 列表: 无序列表使用`-`或`*`，有序列表使用数字加`.`
- 强调: `*斜体*`, `**粗体**`, `***粗斜体***`
- 链接: `[链接文字](URL)`
- 图片: `![替代文字](图片URL)`
- 代码: 行内代码使用`` `代码` ``，代码块使用```包围
- 表格: 使用`|`和`-`创建表格
- 引用: 使用`>`符号

**优势**:

- 专注于内容而非排版
- 跨平台兼容
- 方便版本控制
- 易于转换为其他格式
- 广泛支持（GitHub、Gitee、GitLab 等）

**应用场景**:

- 项目文档
- API 文档
- 技术笔记
- 博客文章
- 静态网站内容
- 快速记录
