# 后端和后台管理系统架构说明

## 🏗️ 整体架构

```
管道巡检系统
├── 前端应用 (uni-app)     - 移动端/H5应用
├── 后端API (Node.js)      - RESTful API服务
├── 后台管理 (Vue 3)       - Web管理界面
└── 数据库 (MySQL)         - 数据存储
```

## 🚀 后端API服务 (Node.js + Express)

### 技术栈
- **Node.js 16+**: 运行环境
- **Express 4**: Web框架
- **MySQL 8**: 关系型数据库
- **Sequelize 6**: ORM框架
- **JWT**: 用户认证
- **Multer**: 文件上传
- **bcryptjs**: 密码加密

### 项目结构
```
backend/
├── src/
│   ├── app.js              # 应用入口
│   ├── models/             # 数据模型
│   │   ├── index.js        # 模型索引
│   │   ├── User.js         # 用户模型
│   │   ├── Device.js       # 设备模型
│   │   ├── Inspection.js   # 巡检记录模型
│   │   └── ...
│   ├── routes/             # 路由控制器
│   │   ├── auth.js         # 认证路由
│   │   ├── users.js        # 用户管理
│   │   ├── devices.js      # 设备管理
│   │   ├── inspections.js  # 巡检记录
│   │   └── ...
│   ├── middleware/         # 中间件
│   ├── utils/              # 工具函数
│   └── config/             # 配置文件
├── database/
│   └── schema.sql          # 数据库结构
├── uploads/                # 上传文件目录
├── package.json
└── .env                    # 环境配置
```

### 核心功能模块

#### 1. 用户认证模块 (`/api/auth`)
- **POST /api/auth/register** - 用户注册
- **POST /api/auth/login** - 用户登录
- **POST /api/auth/refresh** - 刷新令牌
- **GET /api/auth/verify** - 验证令牌
- **POST /api/auth/logout** - 用户登出

#### 2. 用户管理模块 (`/api/users`)
- **GET /api/users** - 获取用户列表
- **GET /api/users/:id** - 获取用户详情
- **PUT /api/users/:id** - 更新用户信息
- **DELETE /api/users/:id** - 删除用户
- **GET /api/users/stats** - 用户统计

#### 3. 设备管理模块 (`/api/devices`)
- **GET /api/devices** - 获取设备列表
- **POST /api/devices** - 创建设备
- **PUT /api/devices/:id** - 更新设备
- **DELETE /api/devices/:id** - 删除设备

#### 4. 巡检记录模块 (`/api/inspections`)
- **GET /api/inspections** - 获取巡检记录
- **POST /api/inspections** - 创建巡检记录
- **GET /api/inspections/:id** - 获取记录详情
- **PUT /api/inspections/:id** - 更新记录
- **DELETE /api/inspections/:id** - 删除记录

#### 5. 文件上传模块 (`/api/upload`)
- **POST /api/upload/image** - 上传图片
- **POST /api/upload/avatar** - 上传头像
- **DELETE /api/upload/:filename** - 删除文件

#### 6. 统计数据模块 (`/api/statistics`)
- **GET /api/statistics/dashboard** - 仪表板统计
- **GET /api/statistics/trend** - 趋势数据
- **GET /api/statistics/user/:id** - 用户统计

### 数据库设计

#### 核心表结构
1. **users** - 用户表
2. **devices** - 设备表
3. **inspections** - 巡检记录表
4. **inspection_images** - 巡检图片表
5. **system_logs** - 系统日志表
6. **statistics** - 统计数据表
7. **system_config** - 系统配置表

#### 关键特性
- **外键约束**: 保证数据完整性
- **索引优化**: 提升查询性能
- **视图支持**: 简化复杂查询
- **触发器**: 自动更新统计数据

## 🖥️ 后台管理系统 (Vue 3 + Element Plus)

### 技术栈
- **Vue 3**: 前端框架
- **Vue Router 4**: 路由管理
- **Vuex 4**: 状态管理
- **Element Plus**: UI组件库
- **ECharts**: 数据可视化
- **Axios**: HTTP客户端

### 项目结构
```
admin/
├── src/
│   ├── main.js             # 应用入口
│   ├── App.vue             # 根组件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   ├── views/              # 页面组件
│   │   ├── Dashboard.vue   # 仪表板
│   │   ├── Users.vue       # 用户管理
│   │   ├── Devices.vue     # 设备管理
│   │   ├── Inspections.vue # 巡检记录
│   │   └── ...
│   ├── components/         # 公共组件
│   ├── utils/              # 工具函数
│   ├── api/                # API接口
│   └── styles/             # 样式文件
├── public/
├── package.json
└── vue.config.js           # Vue配置
```

### 核心功能页面

#### 1. 仪表板 (Dashboard)
- **系统概览**: 用户数、巡检数、设备数统计
- **趋势图表**: 巡检趋势、状态分布
- **实时数据**: 最近巡检记录、活跃用户
- **快速操作**: 常用功能入口

#### 2. 用户管理 (Users)
- **用户列表**: 分页、搜索、筛选
- **用户详情**: 个人信息、巡检统计
- **权限管理**: 角色分配、状态控制
- **批量操作**: 导入、导出、批量编辑

#### 3. 设备管理 (Devices)
- **设备档案**: 设备信息、位置、状态
- **维护记录**: 巡检历史、故障记录
- **设备监控**: 实时状态、预警提醒
- **设备分析**: 故障率、维护周期

#### 4. 巡检记录 (Inspections)
- **记录查询**: 多条件搜索、时间筛选
- **详情查看**: 图片预览、异常描述
- **数据分析**: 统计报表、趋势分析
- **数据导出**: Excel、PDF报告

#### 5. 系统管理 (System)
- **系统配置**: 参数设置、功能开关
- **日志管理**: 操作日志、错误日志
- **数据备份**: 自动备份、手动备份
- **系统监控**: 性能指标、资源使用

### UI设计特点

#### 1. 响应式布局
- **桌面端**: 侧边栏 + 主内容区
- **平板端**: 可折叠侧边栏
- **手机端**: 抽屉式导航

#### 2. 数据可视化
- **ECharts图表**: 柱状图、折线图、饼图
- **实时更新**: WebSocket推送
- **交互式**: 钻取、筛选、缩放

#### 3. 用户体验
- **加载状态**: 骨架屏、进度条
- **错误处理**: 友好提示、重试机制
- **操作反馈**: 成功提示、确认对话框

## 🔧 部署和配置

### 环境要求
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **Nginx**: 1.18+ (生产环境)

### 配置文件

#### 后端环境配置 (.env)
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=pipeline_inspection
DB_USER=root
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3001
NODE_ENV=production

# 文件上传
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 前端地址
FRONTEND_URL=http://localhost:8080
ADMIN_URL=http://localhost:8081
```

### 启动命令

#### 后端服务
```bash
cd backend
npm install
npm run migrate  # 初始化数据库
npm start        # 生产环境
npm run dev      # 开发环境
```

#### 后台管理
```bash
cd admin
npm install
npm run serve    # 开发环境
npm run build    # 构建生产版本
```

## 📊 API接口文档

### 认证接口
```
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

Response:
{
  "message": "登录成功",
  "token": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": 1,
    "username": "admin",
    "display_name": "管理员",
    "role": "admin"
  }
}
```

### 巡检记录接口
```
GET /api/inspections?page=1&limit=10&status=normal
Authorization: Bearer <token>

Response:
{
  "data": [
    {
      "id": 1,
      "device_id": "PIPE001",
      "inspection_type": "triangle",
      "inspection_status": "normal",
      "inspection_time": "2024-01-15T10:30:00Z",
      "user": {
        "display_name": "张三"
      },
      "images": [...]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "pages": 15
  }
}
```

## 🔒 安全特性

### 1. 认证和授权
- **JWT令牌**: 无状态认证
- **角色权限**: 基于角色的访问控制
- **令牌刷新**: 自动续期机制

### 2. 数据安全
- **密码加密**: bcrypt哈希
- **SQL注入防护**: Sequelize ORM
- **XSS防护**: 输入验证和转义

### 3. 接口安全
- **请求限制**: 防止暴力攻击
- **CORS配置**: 跨域访问控制
- **HTTPS**: 传输加密

## 📈 性能优化

### 1. 数据库优化
- **索引策略**: 查询字段索引
- **连接池**: 复用数据库连接
- **查询优化**: 避免N+1查询

### 2. 接口优化
- **分页查询**: 减少数据传输
- **缓存策略**: Redis缓存热点数据
- **压缩传输**: Gzip压缩

### 3. 前端优化
- **代码分割**: 按需加载
- **图片优化**: 懒加载、压缩
- **CDN加速**: 静态资源分发

## 🎯 后续扩展

### 1. 功能扩展
- **移动端适配**: 响应式设计
- **实时通知**: WebSocket推送
- **报表系统**: 自定义报表

### 2. 技术升级
- **微服务架构**: 服务拆分
- **容器化部署**: Docker部署
- **监控告警**: 系统监控

### 3. 集成扩展
- **第三方集成**: 企业微信、钉钉
- **IoT设备**: 传感器数据接入
- **AI分析**: 图像识别、预测分析

现在您有了一个完整的后端API服务和后台管理系统架构！🎉
