# 🚀 管道巡检系统完整启动指南

## 📋 系统概览

您现在拥有一个完整的管道巡检系统：
- ✅ **前端应用** (uni-app) - 移动端/H5应用
- ✅ **后端API** (Node.js + MySQL) - RESTful API服务  
- ✅ **后台管理** (Vue 3) - Web管理界面
- ✅ **数据库设计** (MySQL) - 完整的数据结构

## 🎯 立即开始 - 按顺序执行

### 第一步：启动后端API服务 ⭐⭐⭐ (最重要)

#### 1.1 切换到MySQL版本
```bash
cd backend

# 备份原MongoDB版本
move package.json package-mongodb.json

# 使用MySQL版本
move package-mysql.json package.json
```

#### 1.2 安装后端依赖
```bash
npm install
```

#### 1.3 创建MySQL数据库
```bash
# 打开命令行，登录MySQL
mysql -u root -p

# 输入MySQL密码后，执行以下SQL命令：
CREATE DATABASE pipeline_inspection DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 退出MySQL
exit

# 导入数据库结构
mysql -u root -p pipeline_inspection < database/schema.sql
```

#### 1.4 配置数据库连接
编辑 `backend/.env` 文件，设置您的MySQL密码：
```env
DB_PASSWORD=你的MySQL密码
```

#### 1.5 初始化数据库数据
```bash
npm run migrate
```

#### 1.6 启动后端服务
```bash
npm run dev
```

**✅ 成功标志：**
看到以下信息表示后端启动成功：
```
✅ 数据库连接成功
✅ 数据库模型同步完成
🚀 服务器启动成功
📍 地址: http://localhost:3001
```

**🧪 测试后端：**
在浏览器访问：http://localhost:3001/health

---

### 第二步：连接前端到后端 ⭐⭐

#### 2.1 修改前端API配置
找到前端项目中的API配置文件，通常在：
- `src/utils/request.js` 或
- `src/config/api.js` 或  
- `src/api/config.js`

将API基础地址改为：
```javascript
const baseURL = 'http://localhost:3001/api'
```

#### 2.2 测试前端登录
使用默认管理员账户测试：
- **用户名**: admin
- **密码**: admin123

---

### 第三步：启动后台管理系统 ⭐

#### 3.1 安装后台管理依赖
```bash
cd admin
npm install
```

#### 3.2 启动后台管理
```bash
npm run serve
```

**✅ 成功标志：**
后台管理系统将在 http://localhost:8081 启动

#### 3.3 登录后台管理
使用相同的管理员账户：
- **用户名**: admin  
- **密码**: admin123

---

## 🔧 如果遇到问题

### 问题1：MySQL连接失败
**解决方案：**
1. 确保MySQL服务已启动
2. 检查用户名密码是否正确
3. 确认数据库 `pipeline_inspection` 已创建

### 问题2：端口被占用
**解决方案：**
修改 `backend/.env` 文件中的端口：
```env
PORT=3002
```

### 问题3：依赖安装失败
**解决方案：**
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules
npm install
```

### 问题4：前端无法连接后端
**解决方案：**
1. 确认后端服务正在运行
2. 检查前端API配置地址
3. 查看浏览器控制台错误信息

---

## 📱 系统使用流程

### 1. 后台管理系统使用
1. 访问 http://localhost:8081
2. 使用 admin/admin123 登录
3. 管理用户、设备、查看统计数据

### 2. 前端应用使用
1. 在开发工具中运行前端项目
2. 使用创建的用户账户登录
3. 进行巡检记录、查看历史等操作

### 3. API接口测试
访问 http://localhost:3001/api 查看完整API文档

---

## 🎯 默认账户信息

### 管理员账户
- **用户名**: admin
- **密码**: admin123
- **权限**: 完全管理权限

### 系统端口
- **后端API**: http://localhost:3001
- **后台管理**: http://localhost:8081  
- **前端应用**: 根据您的开发工具配置

---

## 📊 系统功能概览

### 后端API功能
- ✅ 用户认证和管理
- ✅ 设备管理
- ✅ 巡检记录CRUD
- ✅ 图片上传处理
- ✅ 统计数据分析
- ✅ 系统配置管理

### 前端应用功能  
- ✅ 用户登录注册
- ✅ 巡检记录创建
- ✅ 图片拍照上传
- ✅ 历史记录查看
- ✅ 个人信息管理

### 后台管理功能
- ✅ 用户管理
- ✅ 设备管理  
- ✅ 巡检记录管理
- ✅ 数据统计分析
- ✅ 系统配置

---

## 🚨 重要提醒

### 安全提醒
⚠️ **立即修改默认密码！**
生产环境使用前，请务必：
1. 修改管理员密码
2. 更改JWT密钥
3. 配置HTTPS
4. 设置防火墙

### 数据备份
💾 **定期备份数据库**
```bash
mysqldump -u root -p pipeline_inspection > backup.sql
```

---

## 📞 获取帮助

### 查看日志
```bash
# 后端日志
cd backend
npm run dev

# 查看详细错误信息
```

### 常用命令
```bash
# 重启后端服务
cd backend
npm run dev

# 重启后台管理
cd admin  
npm run serve

# 数据库重新初始化
cd backend
npm run migrate
```

---

## 🎉 完成！

按照以上步骤操作后，您将拥有一个完全运行的管道巡检系统！

**下一步建议：**
1. 创建几个测试用户
2. 添加一些设备数据
3. 进行巡检记录测试
4. 查看统计数据

**需要帮助？**
如果在任何步骤遇到问题，请检查：
1. 控制台错误信息
2. 网络连接状态  
3. 服务运行状态
4. 配置文件设置

祝您使用愉快！🎊
